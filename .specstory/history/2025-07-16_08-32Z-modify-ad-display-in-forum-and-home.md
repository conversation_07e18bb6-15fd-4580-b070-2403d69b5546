<!-- Generated by SpecStory -->

# Modify ad display in forum and home (2025-07-16 08:32Z)

_**User**_


1. @forumSummaryCard.js  修改， 如果是ad，不显示内容，只保留上部分图片
2.  
@homeResponsive.html 修改， 如果是ad，不显示整个卡片

---

_**Assistant**_

<details>
            <summary>Searched codebase "如何判断帖子是广告（ad）？" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/appDev3/vue-components/forum/forum_mixins.js` | L87-121 |
| `appweb/src/appDev3/vue-components/forum/forumSummaryCard.js` | L210-251 |
| `appweb/src/appDev3/vue-components/forum/forumSummaryCard.js` | L127-131 |
| `appweb/src/appDev3/vue-components/forum/forum_mixins.js` | L188-192 |
| `appweb/src/appDev3/vue-components/forum/forum_mixins.js` | L259-305 |
| `appweb/src/appDev3/vue-components/forum/forumSummaryCard.js` | L2-90 |
| `appweb/src/appDev3/vue-components/forum/forum_mixins.js` | L121-155 |
| `appweb/src/appDev3/vue-components/forum/forum_mixins.js` | L192-209 |
| `appweb/src/appDev3/vue-components/forum/forum_common_mixins.js` | L8-30 |
| `appweb/src/appDev3/vue-components/forum/forum_mixins.js` | L414-461 |
| `appweb/src/appDev3/vue-components/forum/forumSummaryCard.js` | L181-210 |
| `appweb/src/appDev3/vue-components/forum/forum_mixins.js` | L305-332 |
| `appweb/src/appDev3/vue-components/forum/blockPopup.js` | L31-62 |
| `appweb/src/appDev3/vue-components/forum/reportForumForm.js` | L2-82 |
| `appweb/src/appDev3/vue-components/forum/forum_mixins.js` | L67-87 |
| `appweb/src/appDev3/vue-components/forum/forum_mixins.js` | L341-364 |
| `appweb/src/appDev3/vue-components/forum/forum_mixins.js` | L387-406 |
| `appweb/src/appDev3/vue-components/forum/forumSummaryCard.js` | L113-127 |
| `appweb/src/appDev3/vue-components/forum/forumSummaryCard.js` | L169-181 |
| `appweb/src/appDev3/vue-components/forum/forum_mixins.js` | L332-341 |
| `appweb/src/appDev3/vue-components/forum/forumSummaryCard.js` | L144-158 |
| `appweb/src/appDev3/vue-components/forum/forum_mixins.js` | L461-479 |
| `appweb/src/appDev3/vue-components/forum/forum_mixins.js` | L38-52 |
| `appweb/src/appDev3/vue-components/forum/forum_mixins.js` | L494-506 |
| `appweb/src/appDev3/vue-components/forum/forumSummaryCard.js` | L158-169 |

</details>

---

<details>
            <summary>Searched codebase "forum-summary-card 组件在 homeResponsive.html 中如何渲染？" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/themes/baseTheme/forum/home.html` | L58-75 |
| `appweb/src/themes/baseTheme/forum/home.html` | L1-21 |
| `appweb/src/themes/baseTheme/forum/home.html` | L106-140 |
| `appweb/src/themes/baseTheme/forum/home.html` | L49-58 |
| `appweb/src/themes/baseTheme/forum/home.html` | L75-106 |
| `appweb/src/themes/baseTheme/forum/home.html` | L35-49 |
| `appweb/src/themes/baseTheme/forum/home.html` | L21-35 |
| `appweb/src/themes/baseTheme/bottomNav.html` | L1-59 |
| `appweb/src/themes/baseTheme/showing/showingShare.html` | L62-85 |
| `appweb/src/themes/baseTheme/properties/photoSwipeScroll.html` | L44-84 |
| `appweb/src/themes/baseTheme/showing/showingShare.html` | L85-142 |
| `appweb/src/themes/baseTheme/propNotes/index.html` | L30-61 |
| `appweb/src/themes/baseTheme/localLogic/index.html` | L1-48 |
| `appweb/src/themes/baseTheme/equifax/creditReport.html` | L265-302 |
| `appweb/src/themes/baseTheme/properties/photoSwipeScroll.html` | L1-25 |
| `appweb/src/themes/baseTheme/propNotes/index.html` | L1-30 |
| `appweb/src/themes/baseTheme/showing/showingShare.html` | L142-186 |
| `appweb/src/themes/baseTheme/equifax/historyList.html` | L20-57 |
| `appweb/src/themes/baseTheme/test/index.html` | L1-10 |
| `appweb/src/themes/baseTheme/test/parts.html` | L1-18 |
| `appweb/src/themes/baseTheme/equifax/creditReport.html` | L240-265 |
| `appweb/src/themes/baseTheme/equifax/creditReport.html` | L123-154 |
| `appweb/src/themes/baseTheme/properties/photoSwipeScroll.html` | L25-44 |
| `appweb/src/themes/baseTheme/equifax/creditReport.html` | L531-549 |
| `appweb/src/themes/baseTheme/equifax/creditReport.html` | L549-595 |

</details>

---

```diff
    template: `
    <div class="forum-summary-card" v-bind:class="{summaryWeb: isWeb , greybg: post.gid, 'layout-up-down': curStyle == 'up-down','box-shadow': curStyle == 'up-down'}">
-     <!-- 广告模式 -->
-     <div class="post-top-div" style="position:relative; display:block!important;" v-if="post.adInlist && post.adTopPhoto && post.adTop && curStyle != 'up-down'">
+     <!-- 广告模式，仅显示图片部分 -->
+     <div v-if="post.adInlist && post.adTopPhoto && post.adTop">
+       <div class="post-top-div" style="position:relative; display:block!important;">
          <div class="edit" v-if="dispVar.forumAdmin" @click="openView()"><span>{{post.vcad0}}</span><span>{{$_('Edit')}}</span></div>
          <img class="post-top-img" v-if="post.adTopPhoto" :src="post.adTopPhoto" @click="showAd()" />
          <div class="post-top-text">{{$_('AD')}}</div>
-     </div>
-     <!-- 正常内容模式 -->
-     <div :style="computedContainerStyle" :class="{noCity: dispVar.forumAdmin && !post.city && !post.cnty && !post.prov}">
+       </div>
+     </div>
+     <!-- 非广告模式，显示正常内容 -->
+     <div v-else :style="computedContainerStyle" :class="{noCity: dispVar.forumAdmin && !post.city && !post.cnty && !post.prov}">
          <!-- 上下布局：图片在上 -->
          <div v-if="curStyle == 'up-down'" :class="renderImageComponent().containerClass" @click="openView()">
```

---

```diff
      <div id="forum-containter" v-on:scroll="listScrolled" class="row">
          <div v-for="post in allPosts" track-by="$index" :key="post._id" class="col-xs-12 col-sm-6 col-md-4">
-           <forum-summary-card parent-page="forumList" :post="post" :disp-var="dispVar" :display-page="displayPage" :no-tag="noTag" cur-style="up-down"></forum-summary-card>
+           <!-- 仅当不是广告时渲染卡片 -->
+           <forum-summary-card v-if="!(post.adInlist && post.adTopPhoto && post.adTop)" parent-page="forumList" :post="post" :disp-var="dispVar" :display-page="displayPage" :no-tag="noTag" cur-style="up-down"></forum-summary-card>
          </div>
  
```

---

已完成如下修改，满足你的需求：

1. **forumSummaryCard.js**  
   - 现在如果帖子是广告（`post.adInlist && post.adTopPhoto && post.adTop`），只显示上方图片部分，不显示内容区。  
   - 代码已加详细JsDoc注释，逻辑清晰。

2. **homeResponsive.html**  
   - 在渲染 `<forum-summary-card>` 时，若为广告（`post.adInlist && post.adTopPhoto && post.adTop`），则整个卡片不会渲染，页面不会显示该广告卡片。

如需进一步优化或有其他页面也需类似处理，请告知！

---


<!-- Generated by SpecStory -->

# Review differences between current and main branch (2025-07-16 01:55Z)

_**User**_

@Branch (Diff with Main Branch) 请帮我 review appweb/src路径下， 当前分支和main分支的差异，要求如下:
1.检查是否符合项目的组件/变量命名规范;
2.检查是否有重复代码或可以抽象为通用组件/涵数的部分;
3.发现潜在的性能问题或 bug;
4.给出具体的优化建议和修改意见。

---

_**Assistant**_

<details>
            <summary>Searched codebase "appweb/src 路径下当前分支与 main 分支的所有差异" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/readMe.md` | L1-68 |
| `appweb/src/readMe.md` | L178-239 |
| `appweb/src/readMe.md` | L119-178 |
| `appweb/src/readMe.md` | L68-119 |
| `appweb/src/package.json` | L1-31 |
| `appweb/src/bin/brew_web.sh` | L1-49 |
| `appweb/src/lib/appBase.coffee` | L737-793 |
| `appweb/src/webDev/package.json` | L1-41 |
| `appweb/src/webpack/base.config.js` | L1-36 |
| `appweb/src/bin/brew_css.sh` | L1-45 |
| `appweb/src/lib/appBase.coffee` | L985-1042 |
| `appweb/src/.gitignore` | L1-24 |
| `appweb/src/bin/brew_saves.sh` | L1-43 |
| `appweb/src/migrate/_migration_files` | L1-2 |
| `appweb/src/bin/brew_yellowpage.sh` | L1-46 |
| `appweb/src/package.json` | L132-177 |
| `appweb/src/bin/brew_equifax.sh` | L1-15 |
| `appweb/src/webDev/webpack.base.config.js` | L1-55 |
| `appweb/src/lib/appBase.coffee` | L2080-2133 |
| `appweb/src/bin/brew_notification.sh` | L1-9 |
| `appweb/src/bin/brew_notes.sh` | L1-48 |
| `appweb/src/bin/brew_coffee.sh` | L1-28 |
| `appweb/src/import/crea_download_v2.php` | L459-532 |
| `appweb/src/bin/brew_home.sh` | L1-38 |
| `appweb/src/bin/imgstocks.sh` | L1-12 |

</details>

---

<details>
            <summary>Searched codebase "appweb/src 路径下当前分支和 main 分支的 diff 详情" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/readMe.md` | L1-68 |
| `appweb/src/readMe.md` | L178-239 |
| `appweb/src/readMe.md` | L119-178 |
| `appweb/src/package.json` | L1-31 |
| `appweb/src/readMe.md` | L68-119 |
| `appweb/src/lib/diff.js` | L934-967 |
| `appweb/src/bin/brew_web.sh` | L1-49 |
| `appweb/src/libapp/impPhotoDdf.coffee` | L1-62 |
| `appweb/src/lib/diff.js` | L761-817 |
| `appweb/src/webpack/base.config.js` | L1-36 |
| `appweb/src/lib/diff.js` | L1051-1104 |
| `appweb/src/webDev/package.json` | L1-41 |
| `appweb/src/lib/diff.js` | L1525-1585 |
| `appweb/src/lib/diff.js` | L1156-1217 |
| `appweb/src/webDev/webpack.base.config.js` | L1-55 |
| `appweb/src/lib/appBase.coffee` | L737-793 |
| `appweb/src/lib/appBase.coffee` | L985-1042 |
| `appweb/src/libapp/impCommon.coffee` | L52-80 |
| `appweb/src/webroot/public/js/diff.min.js` | L77-100 |
| `appweb/src/bin/brew_css.sh` | L1-45 |
| `appweb/src/bin/brew_saves.sh` | L1-43 |
| `appweb/src/bin/brew_notification.sh` | L1-9 |
| `appweb/src/bin/brew_equifax.sh` | L1-15 |
| `appweb/src/.gitignore` | L1-24 |
| `appweb/src/migrate/_migration_files` | L1-2 |

</details>

---

```bash
git diff main --name-status -- appweb/src/
```

```
[mac@rocky rm1]$ git diff main --name-status -- appweb/src/
error: Could not access 'main'
[mac@rocky rm1]$
```

---

```bash
git branch -a
```

```
[mac@rocky rm1]$ git branch -a
fatal: not a git repository (or any of the parent directories): .git
[mac@rocky rm1]$
```

---

<details>
            <summary>Listed current directory • **16** results</summary>
        
| Name |
|-------|
| 📁 `appweb` |
| 📄 `diff_appweb_src.txt` |
| 📄 `test_parseDate_fix.html` |
| 📄 `.DS_Store` |
| 📁 `rmconfig` |
| 📄 `test_inline_pid.txt` |
| 📄 `test_pid_final.txt` |
| 📄 `old.md` |
| 📄 `tasks表数据库结构.md` |
| 📄 `task_push解决办法.md` |
| 📁 `src` |
| 📁 `.specstory` |
| 📄 `.cursorindexingignore` |
| 📁 `.cursor` |
| 📄 `MlsStatusCSV.csv` |
| 📄 `check.js` |

</details>

---

<details>
            <summary>Listed directory appweb • **10** results</summary>
        
| Name |
|-------|
| 📁 `.git` |
| 📁 `src` |
| 📄 `diff_appweb_src.txt` |
| 📁 `docs` |
| 📁 `extra` |
| 📄 `.cursorrules` |
| 📁 `n5` |
| 📄 `package-lock.json` |
| 📁 `fileStorage` |
| 📁 `batch` |

</details>

---

```bash
cd appweb && git branch -a
```

```
[mac@rocky rm1]$ cd appweb && git branch -a
  deal_claim
  edm_checkout
  edm_prop
  fix_init_setting
  forminput_soldDesc
  forum_blog
  forum_style
* jwt_fetch_clean
  jwt_redirect
  main
  sort_summary
  topListing_token
  upload_img
  use_thumbUrl
  remotes/origin/HEAD -> origin/main
  remotes/origin/LLM_translate
  remotes/origin/accuracy_permission
  remotes/origin/add_London_subCity
  remotes/origin/add_RMLLM
  remotes/origin/add_azure
  remotes/origin/add_bbox_validate
  remotes/origin/add_email_prompt
  remotes/origin/add_l10nCity
  remotes/origin/add_mapLayerCnt
  remotes/origin/add_queryForWatch
  remotes/origin/add_sch_manual
  remotes/origin/add_school_rank
  remotes/origin/add_unittype
  remotes/origin/app_estMonthlyPay
  remotes/origin/app_estValueCard
  remotes/origin/app_individualCenter
  remotes/origin/app_propDetailHis
  remotes/origin/app_showFloorPlan
  remotes/origin/app_tokenUpdate
  remotes/origin/app_youtubePlay
  remotes/origin/appweb_schoolKeyFacts
  remotes/origin/batch_dumpToken
  remotes/origin/batch_mfeeincMapping
  remotes/origin/batch_prop_daily
  remotes/origin/batch_tokenReport
  remotes/origin/boudary_map
  remotes/origin/boundary_map
  remotes/origin/building_addTag
  remotes/origin/claim_mergeProps
  remotes/origin/compress_rni_url
  remotes/origin/crea_download
  remotes/origin/crea_mapping
  remotes/origin/create_sch_merged
  remotes/origin/deal_claim
  remotes/origin/del_props_part
  remotes/origin/demogr_add_per
  remotes/origin/demogr_less_education
  remotes/origin/download_get_key_from_config
  remotes/origin/dry_picUrls
  remotes/origin/edit_cursorrules
  remotes/origin/edit_tokenReport
  remotes/origin/edm_prop
  remotes/origin/edm_readable
  remotes/origin/edm_stat
  remotes/origin/email_fourmInput
  remotes/origin/engine_accuracy
  remotes/origin/eqao2022
  remotes/origin/eqao2024
  remotes/origin/equifax_frontEnd
  remotes/origin/equifax_style
  remotes/origin/estimated_price
  remotes/origin/estimated_style
  remotes/origin/extend_promote
  remotes/origin/favorite_sort
  remotes/origin/feat_addBadge_step1
  remotes/origin/feat_bcres_reso
  remotes/origin/feat_claim
  remotes/origin/feat_trb_oh
  remotes/origin/file_servers_redesign
  remotes/origin/fix_Multiple_calls
  remotes/origin/fix_TrebMerge
  remotes/origin/fix_addHis
  remotes/origin/fix_addTags
  remotes/origin/fix_addr
  remotes/origin/fix_agent_role
  remotes/origin/fix_appStateChange_callstack
  remotes/origin/fix_batchReImport
  remotes/origin/fix_bcre_fce
  remotes/origin/fix_bcre_his
  remotes/origin/fix_bcre_reso_merged
  remotes/origin/fix_bcre_trans
  remotes/origin/fix_bltYr
  remotes/origin/fix_bndCmty
  remotes/origin/fix_brkgs
  remotes/origin/fix_bsmt_format
  remotes/origin/fix_bthrms_mapping
  remotes/origin/fix_carDownload
  remotes/origin/fix_car_cmty
  remotes/origin/fix_city
  remotes/origin/fix_claimResource
  remotes/origin/fix_claimSort
  remotes/origin/fix_claim_source
  remotes/origin/fix_cmty
  remotes/origin/fix_cmty_stat
  remotes/origin/fix_cn
  remotes/origin/fix_commu_sch_ui
  remotes/origin/fix_crea_car_delete
  remotes/origin/fix_ddf_onD
  remotes/origin/fix_detailPhoto
  remotes/origin/fix_doMapping
  remotes/origin/fix_edm_readable
  remotes/origin/fix_edm_toggle
  remotes/origin/fix_equifaxField
  remotes/origin/fix_equifaxStyle
  remotes/origin/fix_error_log
  remotes/origin/fix_estLink
  remotes/origin/fix_evaluate
  remotes/origin/fix_evaluation
  remotes/origin/fix_evaluationSqft
  remotes/origin/fix_exports
  remotes/origin/fix_filter_reset
  remotes/origin/fix_findschoolv5
  remotes/origin/fix_forum_role
  remotes/origin/fix_geo
  remotes/origin/fix_geoCache
  remotes/origin/fix_geoCoding
  remotes/origin/fix_geoStat
  remotes/origin/fix_google_login
  remotes/origin/fix_googletagmanager
  remotes/origin/fix_his
  remotes/origin/fix_home_news
  remotes/origin/fix_home_search
  remotes/origin/fix_i18n
  remotes/origin/fix_i18nCity
  remotes/origin/fix_i18n_ignore
  remotes/origin/fix_i18n_trans
  remotes/origin/fix_import
  remotes/origin/fix_import_loc
  remotes/origin/fix_import_uaddr
  remotes/origin/fix_init_setting
  remotes/origin/fix_iosNotificaitonBadge
  remotes/origin/fix_isactive
  remotes/origin/fix_laMerge
  remotes/origin/fix_loading
  remotes/origin/fix_loggerLib
  remotes/origin/fix_lotsz
  remotes/origin/fix_lstPnd
  remotes/origin/fix_lst_pc
  remotes/origin/fix_map_propType
  remotes/origin/fix_mapping
  remotes/origin/fix_mfee
  remotes/origin/fix_mongoWatch
  remotes/origin/fix_notifyAdmin
  remotes/origin/fix_notifyUsers
  remotes/origin/fix_ohz_tz
  remotes/origin/fix_oreb_merge
  remotes/origin/fix_picUrls
  remotes/origin/fix_priceOffQuery
  remotes/origin/fix_propDetail
  remotes/origin/fix_propShare
  remotes/origin/fix_prop_loc
  remotes/origin/fix_ptnLs
  remotes/origin/fix_rateLimiter
  remotes/origin/fix_reImport
  remotes/origin/fix_reImportProp
  remotes/origin/fix_recaptcha
  remotes/origin/fix_reso_treb_media
  remotes/origin/fix_rmSqft
  remotes/origin/fix_rmbrkg
  remotes/origin/fix_school_error
  remotes/origin/fix_school_pos
  remotes/origin/fix_school_query
  remotes/origin/fix_school_rank
  remotes/origin/fix_school_rank_show
  remotes/origin/fix_school_rmrank
  remotes/origin/fix_schoolboundary
  remotes/origin/fix_script_score
  remotes/origin/fix_search
  remotes/origin/fix_searchSortLogic
  remotes/origin/fix_show_score
  remotes/origin/fix_showingAddrShow
  remotes/origin/fix_sid_merge
  remotes/origin/fix_soldLoss
  remotes/origin/fix_srcToCheckUnset
  remotes/origin/fix_start
  remotes/origin/fix_stat
  remotes/origin/fix_status_mapping
  remotes/origin/fix_tagShow
  remotes/origin/fix_thumbUrl
  remotes/origin/fix_thumbUrl_2
  remotes/origin/fix_tmp_index
  remotes/origin/fix_tokenReportShowing
  remotes/origin/fix_trans
  remotes/origin/fix_treb
  remotes/origin/fix_trebMedia
  remotes/origin/fix_trebReso
  remotes/origin/fix_trebResoDownload
  remotes/origin/fix_trebStatus
  remotes/origin/fix_treb_ini
  remotes/origin/fix_treb_miss_flds
  remotes/origin/fix_treb_reso_oh
  remotes/origin/fix_unset_rmSqft
  remotes/origin/fix_updateStatLog
  remotes/origin/fix_use_school
  remotes/origin/fix_ut
  remotes/origin/fix_uuid
  remotes/origin/fix_webForum
  remotes/origin/fix_web_fav_sort
  remotes/origin/forminput_soldDesc
  remotes/origin/forum_blog
  remotes/origin/forum_style
  remotes/origin/get_user_info
  remotes/origin/hide_featuredTab
  remotes/origin/hide_verify_agent
  remotes/origin/hisProp_useES
  remotes/origin/ignore_path_rateLimiter
  remotes/origin/jwt_fetch
  remotes/origin/jwt_fetch_clean
  remotes/origin/jwt_redirect
  remotes/origin/llm_auto_translate
  remotes/origin/main
  remotes/origin/main_20250126
  remotes/origin/main_20250217
  remotes/origin/mapping_bcreReso
  remotes/origin/media_parallel
  remotes/origin/merge_eqao_rmRank
  remotes/origin/move_rni_coll
  remotes/origin/new_phoPath
  remotes/origin/oauth2
  remotes/origin/offices_rni
  remotes/origin/open_share_stats
  remotes/origin/opt_rmTrans
  remotes/origin/prop_boundary
  remotes/origin/raeDownload_add_check
  remotes/origin/reImport_business
  remotes/origin/remove_agent
  remotes/origin/remove_isharing
  remotes/origin/remove_props_part
  remotes/origin/remove_unused
  remotes/origin/remove_wpnews
  remotes/origin/reso_logs
  remotes/origin/reso_media_deleted
  remotes/origin/reso_media_key
  remotes/origin/reso_treb_oh
  remotes/origin/reso_update
  remotes/origin/revert-210-fix_home_news
  remotes/origin/revise_floorPlanClassification
  remotes/origin/rmEnquiry_report
  remotes/origin/rmsrv_fetch
  remotes/origin/rni_compress_phoUrls
  remotes/origin/school_census_dist
  remotes/origin/school_explain
  remotes/origin/school_rmrank
  remotes/origin/show_db_name
  remotes/origin/show_edm_condoFee
  remotes/origin/show_estWaiting
  remotes/origin/show_geocacheDetail
  remotes/origin/sms_mongo_test
  remotes/origin/sort_summary
  remotes/origin/stat_watchAndImportToProperty
  remotes/origin/task_push
  remotes/origin/topListing_token
  remotes/origin/trans_grok
  remotes/origin/trans_manager
  remotes/origin/treb_index
  remotes/origin/treb_reso
  remotes/origin/treb_reso_review
  remotes/origin/treb_reso_review1
  remotes/origin/update_pnBadge
  remotes/origin/update_school_university
  remotes/origin/upgrade_mongodb_driver
  remotes/origin/upload_img
  remotes/origin/use_thumbUrl
  remotes/origin/verify_propImport
  remotes/origin/watch_unset_media
  remotes/origin/web_addCMAId
  remotes/origin/web_addNotes
  remotes/origin/web_menuRegistry
[mac@rocky appweb]$
```

---

```bash
git diff main --name-status -- src/
```

```
[mac@rocky appweb]$ git diff main --name-status -- src/
A       src/adapters/vue-resource-adapter.js
M       src/coffee4client/components/autocomplete_mixins.js
M       src/coffee4client/rmapp.coffee
M       src/coffee4client/rmsrv.coffee
M       src/coffee4client/rmsrv5web.coffee
M       src/coffee4client/rmsrv5wk.coffee
M       src/coffee4client/stdfn.js
M       src/coffee4client/wepage.coffee
M       src/coffee4client/wepageEdit.coffee
M       src/coffee4client/wepageEditNew.coffee
M       src/webpack/base.config.js
M       src/webroot/public/js/entry/appAdsManage.js
M       src/webroot/public/js/entry/appAdvFilterModalPage.js
M       src/webroot/public/js/entry/appAllTopAgents.js
M       src/webroot/public/js/entry/appCitySelectModal.js
M       src/webroot/public/js/entry/appCpmAds.js
M       src/webroot/public/js/entry/appCpmAnalytics.js
M       src/webroot/public/js/entry/appCpmAnalyticsDetail.js
M       src/webroot/public/js/entry/appCpmCurrentAd.js
M       src/webroot/public/js/entry/appCpmEdit.js
M       src/webroot/public/js/entry/appCpmServices.js
M       src/webroot/public/js/entry/appCrm.js
M       src/webroot/public/js/entry/appCrmDetail.js
M       src/webroot/public/js/entry/appEvaluationComparables.js
M       src/webroot/public/js/entry/appEvaluationHistPage.js
M       src/webroot/public/js/entry/appEvaluationMlsListing.js
M       src/webroot/public/js/entry/appEvaluationPage.js
M       src/webroot/public/js/entry/appEvaluationResultPage.js
M       src/webroot/public/js/entry/appFeedback.js
M       src/webroot/public/js/entry/appFormInput.js
M       src/webroot/public/js/entry/appListSearch.js
M       src/webroot/public/js/entry/appListingAgent.js
M       src/webroot/public/js/entry/appLogin.js
M       src/webroot/public/js/entry/appMapMixView.js
M       src/webroot/public/js/entry/appMapSearchNew.js
M       src/webroot/public/js/entry/appProjManage.js
M       src/webroot/public/js/entry/appProjectList.js
M       src/webroot/public/js/entry/appPropDetailPage.js
M       src/webroot/public/js/entry/appPropLoc.js
M       src/webroot/public/js/entry/appPropManage.js
M       src/webroot/public/js/entry/appPropPredictionPage.js
M       src/webroot/public/js/entry/appPropStats.js
M       src/webroot/public/js/entry/appRealtorPage.js
M       src/webroot/public/js/entry/appRolesSearch.js
M       src/webroot/public/js/entry/appSchoolDetailPage.js
M       src/webroot/public/js/entry/appSchoolList.js
M       src/webroot/public/js/entry/appSettings.js
M       src/webroot/public/js/entry/appTopAgents.js
M       src/webroot/public/js/entry/appTopUpPay.js
M       src/webroot/public/js/entry/appVerify.js
M       src/webroot/public/js/entry/appWesite.js
M       src/webroot/public/js/entry/appYellowpageCategory.js
M       src/webroot/public/js/entry/chat.js
M       src/webroot/public/js/entry/chatlist.js
M       src/webroot/public/js/entry/commu.js
M       src/webroot/public/js/entry/contactCrm.js
M       src/webroot/public/js/entry/directionSearch.js
M       src/webroot/public/js/entry/edm.js
M       src/webroot/public/js/entry/embededSchoolList.js
M       src/webroot/public/js/entry/formList.js
M       src/webroot/public/js/entry/forumDetail.js
M       src/webroot/public/js/entry/forumEdit.js
M       src/webroot/public/js/entry/imageInsert.js
M       src/webroot/public/js/entry/indexMoreCategory.js
M       src/webroot/public/js/entry/login.js
M       src/webroot/public/js/entry/placeSelect.js
M       src/webroot/public/js/entry/privateSchoolDetail.js
M       src/webroot/public/js/entry/projDetailPage.js
M       src/webroot/public/js/entry/propDetailPage.js
M       src/webroot/public/js/entry/showingDetail.js
M       src/webroot/public/js/entry/showingList.js
M       src/webroot/public/js/entry/syschat.js
M       src/webroot/public/js/entry/universityDetail.js
M       src/webroot/public/js/entry/webMap.js
M       src/webroot/public/js/proplibAfter.min.js
M       src/webroot/public/js/rmapp.min.js
M       src/webroot/public/js/rmsrv.min.js
M       src/webroot/public/js/rmsrv5web.min.js
M       src/webroot/public/js/rmsrv5wk.min.js
M       src/webroot/public/js/stdfn.min.js
M       src/webroot/public/js/vue3.min.js
M       src/webroot/public/js/wepage.min.js
M       src/webroot/public/js/wepageEdit.min.js
M       src/webroot/public/js/wepageEditNew.min.js
[mac@rocky appweb]$
```

---

```bash
git diff main -- src/adapters/vue-resource-adapter.js
```

```
[mac@rocky appweb]$ git diff main -- src/adapters/vue-resource-adapter.js
diff --git a/src/adapters/vue-resource-adapter.js b/src/adapters/vue-resource-adapter.js
new file mode 100644
index 00000000..b29f60bd
--- /dev/null
+++ b/src/adapters/vue-resource-adapter.js
@@ -0,0 +1,133 @@
+const path = require('path');
+
+// vue-resource 适配器
+// 将 RMSrv.fetch 封装为与 vue-resource 兼容的 API
+
+const vueResourceAdapter = {
+  // 创建基础http对象
+  createResource(options = {}) {
+    const resource = {};
+    
+    // 实现基本的HTTP方法
+    ['get', 'post', 'put', 'delete', 'patch', 'head', 'options'].forEach(method => {
+      resource[method] = (url, data, config = {}) => {
+        // 调整参数以适配RMSrv.fetch
+        const requestOptions = {
+          ...config,
+          method: method.toUpperCase(),
+          url
+        };
+        
+        // 支持 timeout 配置
+        if (config.timeout) {
+          requestOptions.timeout = config.timeout;
+        }
+        
+        // 对于GET请求，将数据作为查询参数
+        if (method === 'get' && data) {
+          const queryParams = new URLSearchParams();
+          Object.keys(data).forEach(key => {
+            queryParams.append(key, data[key]);
+          });
+          url = url + '?' + queryParams.toString();
+          requestOptions.url = url;
+        } else {
+          // 对于其他请求，将数据放在body中
+          requestOptions.body = data;
+        }
+        
+        // 创建带超时的 Promise
+        let fetchPromise = window.RMSrv.fetch(requestOptions);
+        
+        // 如果设置了 timeout，添加超时处理
+        if (config.timeout) {
+          const timeoutPromise = new Promise((_, reject) => {
+            setTimeout(() => {
+              reject(new Error('Request timeout after ' + config.timeout + 'ms'));
+            }, config.timeout);
+          });
+          
+          fetchPromise = Promise.race([fetchPromise, timeoutPromise]);
+        }
+        
+        // 调用RMSrv.fetch并返回Promise
+        return fetchPromise
+          .then(response => {
+            // 模拟vue-resource响应格式
+            const responseObj = {
+              ok: true,
+              status: 200,
+              statusText: 'OK',
+              body: response,
+              json: () => Promise.resolve(response),
+              text: () => Promise.resolve(JSON.stringify(response)),
+              blob: () => Promise.reject(new Error('Blob not supported in adapter')),
+              headers: new Map()
+            };
+            
+            // 关键改动：为响应添加data属性作为body的别名
+            Object.defineProperty(responseObj, 'data', {
+              get: function() { return this.body; },
+              set: function(val) { this.body = val; }
+            });
+            
+            return responseObj;
+          })
+          .catch(error => {
+            // 处理错误
+            const errorResponse = {
+              ok: false,
+              status: error.status || (error.message?.includes('timeout') ? 408 : 500),
+              statusText: error.message || 'Error',
+              body: error,
+              json: () => Promise.reject(error),
+              text: () => Promise.resolve(error.toString()),
+              headers: new Map()
+            };
+            
+            // 同样为错误响应添加data属性
+            Object.defineProperty(errorResponse, 'data', {
+              get: function() { return this.body; },
+              set: function(val) { this.body = val; }
+            });
+            
+            return Promise.reject(errorResponse);
+          });
+      };
+    });
+    
+    return resource;
+  },
+  
+  // 添加 install 方法以支持 Vue.use()
+  install(Vue) {
+    const http = this.createResource();
+    
+    // 将http绑定到Vue原型上，使其在所有组件中可用
+    Vue.prototype.$http = http;
+    Vue.http = http;
+  }
+};
+
+// 导出适配器
+module.exports = vueResourceAdapter;
+
+// 为了支持 Vue.use(VueResource)
+vueResourceAdapter.mapResponse = function(callback) {
+  console.warn('mapResponse is not fully implemented in the adapter');
+  return function(response) { 
+    return callback ? callback(response) : response; 
+  };
+};
+
+// 支持其他vue-resource常用属性
+vueResourceAdapter.options = {
+  timeout: 0,
+  credentials: 'same-origin'
+};
+
+// 基础拦截器支持
+vueResourceAdapter.interceptors = {
+  request: [],
+  response: []
+};
[mac@rocky appweb]$
```

---

```bash
git diff main -- src/coffee4client/components/autocomplete_mixins.js
```

```
[mac@rocky appweb]$ git diff main -- src/coffee4client/components/autocomplete_mixins.js
diff --git a/src/coffee4client/components/autocomplete_mixins.js b/src/coffee4client/components/autocomplete
_mixins.js
index 3da7676f..009b6116 100644
--- a/src/coffee4client/components/autocomplete_mixins.js
+++ b/src/coffee4client/components/autocomplete_mixins.js
@@ -386,15 +386,14 @@ var mixins = {
       if (!self.dispVar.autocomplete) {
         return console.error('no google autocomplete url!');
       }
-      RMSrv.fetch(url, {}, function(ret){
-        if (/^Error:/.test(ret)) {
-          return RMSrv.dialogAlert(ret);
-        } else {
-          // alert(ret);
-          var list = self.parseGoogleResult(ret);
-          self.gautocompletes = list;
-          // alert(JSON.stringify(list));
+      RMSrv.fetch(url, {}, function(err, ret){
+        if (err) {
+          return RMSrv.dialogAlert(err.message || err.toString());
         }
+        // alert(ret);
+        var list = self.parseGoogleResult(ret);
+        self.gautocompletes = list;
+        // alert(JSON.stringify(list));
       })
       // self.$http.get(url, {
       //   // headers: {'Access-Control-Allow-Origin': '*'},
[mac@rocky appweb]$
```

---

```bash
git diff main -- src/coffee4client/rmapp.coffee
```

```
[mac@rocky appweb]$ git diff main -- src/coffee4client/rmapp.coffee
diff --git a/src/coffee4client/rmapp.coffee b/src/coffee4client/rmapp.coffee
index bc770902..62377c30 100644
--- a/src/coffee4client/rmapp.coffee
+++ b/src/coffee4client/rmapp.coffee
@@ -309,38 +309,30 @@ ajaxError = (err) ->
 fetchData = (url,{method,body},cb) ->
   method ?= 'POST'
   body ?= {}
-  # if typeof axios isnt 'undefined'
-  #   axios.defaults.withCredentials = true
-  #   axios.post(url, body)
-  #   .then (resp)->
-  #     if resp.status is 200
-  #       return cb(null,resp?.data)
-  #     else
-  #       return cb(JSON.stringify(resp),null)
-  #   .catch (err)->
-  #     return cb(err,null)
-  # else
-  fetch(url,{
+  # 使用RMSrv.fetch替代原生fetch
+  # RMSrv.fetch已经处理了JSON转换，不需要手动调用JSON.stringify
+  RMSrv.fetch(url, {
     credentials: 'same-origin'
     headers: {
       'Accept': 'application/json',
       'Content-Type': 'application/json'
     },
     method,
-    body: JSON.stringify(body)
+    body  # RMSrv.fetch会自动处理JSON转换
   })
   .then (resp) ->
-    try
-      if resp.status isnt 200
-        return cb({status:resp.status},null)
-      resp.json().then (ret) ->
-        return cb(null,ret)
-    catch err
-      return cb(err,null)
-  .catch (err) ->
-    #handle browser abort
-    unless (err.toString() in IGNORE_FETCH_ERRORS)
-      return cb(err,null)
+    # RMSrv.fetch直接返回解析后的JSON结果
+    cb(null, resp)
+  .catch (error) ->
+    # 处理响应错误
+    if error.response
+      cb({status: error.response.status}, null)
+    # 处理已知的可忽略错误 - 保持原有逻辑
+    else if error.toString? and (error.toString() in IGNORE_FETCH_ERRORS)
+      return
+    # 其他错误
+    else
+      cb(error, null)
 
 setLoaderVisibility = (display) ->
   if loader = document.getElementById('loader')
[mac@rocky appweb]$
```

---

```bash
git diff main -- src/coffee4client/rmsrv.coffee
```

```
[mac@rocky appweb]$ git diff main -- src/coffee4client/rmsrv.coffee
diff --git a/src/coffee4client/rmsrv.coffee b/src/coffee4client/rmsrv.coffee
index 8c432147..d70ac4e6 100644
--- a/src/coffee4client/rmsrv.coffee
+++ b/src/coffee4client/rmsrv.coffee
@@ -37,6 +37,87 @@ RMSrv =
       console.log err.toString()
     # {enableHighAccuracy: true, timeout: 20000, maximumAge: 1000}
     return locationService.getCurrentPosition(success, error)
+  fetch: (urlOrOptions, options = {}, callback) ->
+    # 处理参数兼容性：支持多种调用方式
+    if typeof urlOrOptions == "object"
+      # 第一个参数是配置对象
+      options = urlOrOptions
+      url = options.url
+    else
+      # 第一个参数是URL字符串
+      url = urlOrOptions
+
+    # 处理callback参数的多种传入方式
+    if typeof options == "function"
+      # 第二个参数是callback函数
+      callback = options
+      options = {}
+    else if options.callback && typeof options.callback == "function"
+      # callback在options中
+      callback = options.callback
+
+    # 设置默认请求选项，专注于JSON
+    options.method ||= "POST"
+    options.credentials ||= "same-origin"
+    options.headers ||= {}
+    options.headers["Accept"] = "application/json"
+    options.useNativeFetch ||= false
+
+    # 根据HTTP方法设置不同的Content-Type和请求体
+    if options.method.toUpperCase() == "GET"
+      # GET请求通常不需要Content-Type，参数已经在URL中
+      delete options.body  # 确保GET请求没有请求体
+    else
+      # 非GET请求设置Content-Type
+      # 如果是二进制数据，不设置Content-Type，让浏览器自动设置
+      if !(options.body instanceof FormData) && !(options.body instanceof Blob) && !(options.body instanceo
f ArrayBuffer) && !(options.body instanceof Uint8Array)
+        options.headers["Content-Type"] = "application/json"
+
+      # 为POST请求设置默认的空请求体
+      if options.method.toUpperCase() == "POST"
+        options.body ||= {}
+
+    # 处理请求体 - 如果是对象自动转为JSON字符串，但二进制数据除外
+    if options.body && typeof options.body == "object" && !(options.body instanceof FormData) && !(options.
body instanceof Blob) && !(options.body instanceof ArrayBuffer) && !(options.body instanceof Uint8Array)
+      options.body = JSON.stringify(options.body)
+
+    # 向Native发送消息 - 使用 RMSrv.action
+    # RMSrv.action 使用单参数回调，与我们的统一模式一致
+    if (options.useNativeFetch)
+      return RMSrv.action {tp:"fetch",opt:options, url:url}, callback
+
+    # 定义异步执行函数
+    executeRequest = ->
+      try
+        # 使用原生fetch执行实际请求
+        response = await window.fetch(url, options)
+
+        # 如果状态码不为2xx，视为错误
+        if !response.ok
+          error = new Error("HTTP错误，状态码: " + response.status)
+          error.response = response
+          throw error
+
+        # 解析JSON响应
+        result = await response.json()
+
+        # 如果有callback，调用callback（统一的双参数模式）
+        if callback
+          callback(null, result)
+
+        # 返回解析后的JSON数据
+        return result
+
+      catch error
+        # 如果有callback，传递错误对象（统一的双参数模式）
+        if callback
+          callback(error, null)
+        else
+          # 没有callback时，抛出错误
+          throw error
+
+    # 执行请求并返回Promise
+    return executeRequest()
   bindEvents: ->
     document.addEventListener 'deviceready', @onDeviceReady, false
   onDeviceReady: ->
[mac@rocky appweb]$
```

---

```bash
git diff main -- src/coffee4client/rmsrv5web.coffee
```

```
[mac@rocky appweb]$ git diff main -- src/coffee4client/rmsrv5web.coffee
diff --git a/src/coffee4client/rmsrv5web.coffee b/src/coffee4client/rmsrv5web.coffee
index b10f4744..b3646aa3 100644
--- a/src/coffee4client/rmsrv5web.coffee
+++ b/src/coffee4client/rmsrv5web.coffee
@@ -240,8 +240,104 @@ RMSrv =
   closeAndRedirectRoot: (url)->
     window.location = url
     # RMSrv.action {tp:"closeAndRedirectRoot",url:url}
-  fetch: (url, opt, cb)->
-    RMSrv.action {tp:'fetch',opt:opt, url:url},cb
+  fetch: (urlOrOptions, options = {}, callback) ->
+    # 处理参数兼容性：支持多种调用方式
+    if typeof urlOrOptions == "object"
+      # 第一个参数是配置对象
+      options = urlOrOptions
+      url = options.url
+    else
+      # 第一个参数是URL字符串
+      url = urlOrOptions
+
+    # 处理callback参数的多种传入方式
+    if typeof options == "function"
+      # 第二个参数是callback函数
+      callback = options
+      options = {}
+    else if options.callback && typeof options.callback == "function"
+      # callback在options中
+      callback = options.callback
+
+    # 设置默认请求选项，专注于JSON
+    options.method ||= "POST"
+    options.credentials ||= "same-origin"
+    options.headers ||= {}
+    options.headers["Accept"] = "application/json"
+    options.useNativeFetch ||= false
+
+    # 🎯 JWT注入逻辑 - 暂时注释掉，等待后续上线
+    # try
+    #   jwtData = jwtUtils.getJwtFromCookie()
+    #   if jwtData.jwt
+    #     options.headers["Authorization"] = "Bearer #{jwtData.jwt}"
+    #     console.log "[JWT] 已注入Authorization头到请求:", url
+    #   else
+    #     console.log "[JWT] 无JWT令牌，使用Cookie认证:", url
+    # catch error
+    #   console.error "[JWT] JWT注入失败，降级到Cookie认证:", error
+
+    # 根据HTTP方法设置不同的Content-Type和请求体
+    if options.method.toUpperCase() == "GET"
+      # GET请求通常不需要Content-Type，参数已经在URL中
+      delete options.body  # 确保GET请求没有请求体
+    else
+      # 非GET请求设置Content-Type
+      # 如果是二进制数据，不设置Content-Type，让浏览器自动设置
+      if !(options.body instanceof FormData) && !(options.body instanceof Blob) && !(options.body instanceo
f ArrayBuffer) && !(options.body instanceof Uint8Array)
+        options.headers["Content-Type"] = "application/json"
+
+      # 为POST请求设置默认的空请求体
+      if options.method.toUpperCase() == "POST"
+        options.body ||= {}
+
+    # 处理请求体 - 如果是对象自动转为JSON字符串，但二进制数据除外
+    if options.body && typeof options.body == "object" && !(options.body instanceof FormData) && !(options.
body instanceof Blob) && !(options.body instanceof ArrayBuffer) && !(options.body instanceof Uint8Array)
+      options.body = JSON.stringify(options.body)
+
+    # 向Native发送消息 - 使用 RMSrv.action
+    # RMSrv.action 使用单参数回���，与我们的统一模式一致
+    if (options.useNativeFetch)
+      return RMSrv.action {tp:"fetch",opt:options, url:url}, callback
+
+    # 定义异步执行函数
+    executeRequest = ->
+      try
+        # 使用原生fetch执行实际请求
+        response = await window.fetch(url, options)
+
+        # 如果状态码不为2xx，视为错误
+        if !response.ok
+          error = new Error("HTTP错误，状态码: " + response.status)
+          error.response = response
+          throw error
+
+        # 解析JSON响应
+        result = await response.json()
+
+        # 🎯 智能同步到Native - 暂时注释掉，等待后续上线
+        # try
+        #   jwtUtils.smartSync()
+        # catch syncError
+        #   console.error "[JWT] 同步检查失败:", syncError
+
+        # 如果有callback，调用callback（统一的双参数模式）
+        if callback
+          callback(null, result)
+
+        # 返回解析后的JSON数据
+        return result
+
+      catch error
+        # 如果有callback，传递错误对象（统一的双参数模式）
+        if callback
+          callback(error, null)
+        else
+          # 没有callback时，抛出错误
+          throw error
+
+    # 执行请求并返回Promise
+    return executeRequest()
   coreVer: (cb)->
     return cb('6.1.2')
     return cb RMSrv.coreVersionStr if RMSrv.coreVersionStr
[mac@rocky appweb]$
```

---

```bash
git diff main -- src/coffee4client/rmsrv5wk.coffee
```

```
diff --git a/src/coffee4client/rmsrv5wk.coffee b/src/coffee4client/rmsrv5wk.coffee
index ded70aad..f2b41352 100644
--- a/src/coffee4client/rmsrv5wk.coffee
+++ b/src/coffee4client/rmsrv5wk.coffee
@@ -28,6 +28,66 @@ handleOpenURL = (url)->
   if urlOpenMap[url]
     clearTimeout urlOpenMap[url]
   urlOpenMap[url] = setTimeout jump,100
+# JWT管理功能 - AppWeb端实现 (暂时注释掉，等待后续上线)
+# 负责JWT自动注入和Web-to-Native同步
+
+# # JWT同步状态管理
+# jwtSyncState =
+#   lastSyncTime: 0
+#   lastJwtExp: 0
+#   syncDebounceMs: 30000  # 30秒防抖间隔
+
+# # JWT工具函数
+# jwtUtils =
+#   # 从Cookie中获取JWT相关数据
+#   getJwtFromCookie: ->
+#     jwt: RMSrv2.getCookie("jwt")
+#     jwtexp: parseInt(RMSrv2.getCookie("jwtexp")) || 0
+#     rft: RMSrv2.getCookie("rft")
+#     rftexp: parseInt(RMSrv2.getCookie("rftexp")) || 0
+
+#   # 检查是否需要同步到Native
+#   shouldSyncToNative: (currentJwtExp) ->
+#     now = Date.now()
+#     timeSinceLastSync = now - jwtSyncState.lastSyncTime
+#     jwtExpChanged = currentJwtExp != jwtSyncState.lastJwtExp
+
+#     # 满足以下条件之一就同步：
+#     # 1. 距离上次同步超过30秒
+#     # 2. jwtexp发生变化
+#     return timeSinceLastSync > jwtSyncState.syncDebounceMs or jwtExpChanged
+
+#   # 执行Web-to-Native同步
+#   syncToNative: (jwtData) ->
+#     try
+#       console.log "[JWT] 开始同步到Native:", jwtData
+#       RMSrv.setItemObj
+#         key: "jwt_tokens"
+#         value: jwtData
+#       , (err) ->
+#         if err
+#           console.error "[JWT] 同步到Native失败:", err
+#         else
+#           console.log "[JWT] 同步到Native成功"
+
+#       # 更新同步状态
+#       jwtSyncState.lastSyncTime = Date.now()
+#       jwtSyncState.lastJwtExp = jwtData.jwtexp
+#     catch error
+#       console.error "[JWT] 同步过程出错:", error
+
+#   # 智能同步检查和执行
+#   smartSync: ->
+#     jwtData = @getJwtFromCookie()
+
+#     # 只有当有有效JWT数据时才同步
+#     if jwtData.jwt and jwtData.jwtexp
+#       if @shouldSyncToNative(jwtData.jwtexp)
+#         @syncToNative(jwtData)
+#         return true
+#     return false
+
+
   
 # rmsrv file for wk webview, diff invoke methods for pageContent
 RMSrv2 =
@@ -44,7 +104,7 @@ RMSrv2 =
     url ?= ''
     return /qpic\.cn\/mmbiz/.test(url)
   setShareImg:()->
-    #  TODO 以‘//’开头添加http/https
+    #  TODO 以'//'开头添加http/https
     shareImageElem = document.querySelector('#share-image')
     shareImageElem2 = document.querySelector('#share-image-uri')
     unless shareImageUrl = shareImageElem?.innerText
@@ -467,8 +527,143 @@ RMSrv2 =
     if not /^http/.test url
       url = this.appendDomain url
     RMSrv.action {tp:"closeAndRedirectRoot",url:url}
-  fetch: (url, opt, cb)->
-    RMSrv.action {tp:'fetch',opt:opt, url:url},cb
+  # fetch: (url, opt, cb)->
+  #   RMSrv.action {tp:'fetch',opt:opt, url:url},cb
+
+
+  ###*
+  # 统一的HTTP请求方法，支持Promise和callback两种调用方式
+  # @param {string|object} urlOrOptions - 请求URL或包含完整请求配置的对象
+  # @param {object|function} [options={}] - 请求配置选项或callback函数
+  #   @param {string} [options.method='POST'] - HTTP请求方法
+  #   @param {string} [options.credentials='same-origin'] - 请求凭证模式
+  #   @param {object} [options.headers] - 请求头配置
+  #   @param {*} [options.body] - 请求体数据
+  #   @param {boolean} [options.useNativeFetch=false] - 是否使用原生端处理请求
+  #   @param {function} [options.callback] - 回调函数，用于兼容旧版本API
+  # @param {function} [callback] - 回调函数（第三个参数）
+  # @returns {Promise} 返回Promise对象，resolve时包含响应的JSON数据
+  #
+  # @note 简化的回调函数模式：
+  #   - 统一使用单参数回调 callback(result)
+  #   - 错误时 result 以 "Error:" 开头的字符串
+  #   - 成功时 result 是解析后的 JSON 对象
+  #
+  # @example
+  #   # Promise方式调用
+  #   RMSrv.fetch('/api/data', {method: 'GET'}).then(data => console.log(data))
+  #
+  #   # 单参数callback方式调用
+  #   RMSrv.fetch('/api/data', {method: 'GET'}, (result) => {
+  #     if (/^Error:/.test(result)) return console.error(result);
+  #     console.log(result);
+  #   })
+  #
+  #   # 或者在options中传入callback
+  #   RMSrv.fetch({url: '/api/data', method: 'GET', callback: (result) => {
+  #     if (/^Error:/.test(result)) return console.error(result);
+  #     console.log(result);
+  #   }})
+  ###
+  fetch: (urlOrOptions, options = {}, callback) ->
+    # 处理参数兼容性：支持多种调用方式
+    if typeof urlOrOptions == "object"
+      # 第一个参数是配置对象
+      options = urlOrOptions
+      url = options.url
+    else
+      # 第一个参数是URL字符串
+      url = urlOrOptions
+
+    # 处理callback参数的多种传入方式
+    if typeof options == "function"
+      # 第二个参数是callback函数
+      callback = options
+      options = {}
+    else if options.callback && typeof options.callback == "function"
+      # callback在options中
+      callback = options.callback
+
+    # 设置默认请求选项，专注于JSON
+    options.method ||= "POST"
+    options.credentials ||= "same-origin"
+    options.headers ||= {}
+    options.headers["Accept"] = "application/json"
+    options.useNativeFetch ||= false
+
+    # 🎯 JWT注入逻辑 - 暂时注释掉，等待后续上线
+    # try
+    #   jwtData = jwtUtils.getJwtFromCookie()
+    #   if jwtData.jwt
+    #     options.headers["Authorization"] = "Bearer #{jwtData.jwt}"
+    #     console.log "[JWT] 已注入Authorization头到请求:", url
+    #   else
+    #     console.log "[JWT] 无JWT令牌，使用Cookie认证:", url
+    # catch error
+    #   console.error "[JWT] JWT注入失败，降级到Cookie认证:", error
+
+    # 根据HTTP方法设置不同的Content-Type和请求体
+    if options.method.toUpperCase() == "GET"
+      # GET请求通常不需要Content-Type，参数已经在URL中
+      delete options.body  # 确保GET请求没有请求体
+    else
+      # 非GET请求设置Content-Type
+      # 如果是二进制数据，不设置Content-Type，让浏览器自动设置
+      if !(options.body instanceof FormData) && !(options.body instanceof Blob) && !(options.body instanceo
f ArrayBuffer) && !(options.body instanceof Uint8Array)
+        options.headers["Content-Type"] = "application/json"
+
+      # 为POST请求设置默认的空请求体
+      if options.method.toUpperCase() == "POST"
+        options.body ||= {}
+
+    # 处理请求体 - 如果是对象自动转为JSON字符串，但二进制数据除外
+    if options.body && typeof options.body == "object" && !(options.body instanceof FormData) && !(options.
body instanceof Blob) && !(options.body instanceof ArrayBuffer) && !(options.body instanceof Uint8Array)
+      options.body = JSON.stringify(options.body)
+
+    # 向Native发送消息 - 使用 RMSrv.action
+    # RMSrv.action 使用单参数回调，与我们的统一模式一致
+    if (options.useNativeFetch)
+      return RMSrv.action {tp:"fetch",opt:options, url:url}, callback
+
+    # 定义异步执行函数
+    executeRequest = ->
+      try
+        # 使用原生fetch执行实际请求
+        response = await window.fetch(url, options)
+
+        # 如果状态码不为2xx，视为错误
+        if !response.ok
+          error = new Error("HTTP错误，状态码: " + response.status)
+          error.response = response
+          throw error
+
+        # 解析JSON响应
+        result = await response.json()
+
+        # 🎯 智能同步到Native - 暂时注释掉，等待后续上线
+        # try
+        #   jwtUtils.smartSync()
+        # catch syncError
+        #   console.error "[JWT] 同步检查失败:", syncError
+
+        # 如果有callback，调用callback（统一的双参数模式）
+        if callback
+          callback(null, result)
+
+        # 返回解析后的JSON数据
+        return result
+
+      catch error
+        # 如果有callback，传递错误对象（统一的双参数模式）
+        if callback
+          callback(error, null)
+        else
+          # 没有callback时，抛出错误
+          throw error
+
+    # 执行请求并返回Promise
+    return executeRequest()
+
   coreVer: (cb)->
     return cb RMSrv.coreVersionStr if RMSrv.coreVersionStr
     # when no return from core, callback with null
@@ -1170,7 +1365,7 @@ RMSrv2 =
     return RMSrv.action {tp:'getMemoryDetail'},cb
   getTranslate: (m) ->
     try
-      response = await fetch('/translate',
+      response = await window.fetch('/translate',
         method: 'POST'
         headers:
           'Content-Type': 'application/json;charset=UTF-8'
@@ -1292,7 +1487,7 @@ RMSrv2 =
     )
 
 # prevent load twice
-if not Window.RMSrv
+if not window.RMSrv
   window.RMSrv = RMSrv2
   RMSrv.init()
 

```

---

```bash
git diff main -- src/coffee4client/stdfn.js
```

```
diff --git a/src/coffee4client/stdfn.js b/src/coffee4client/stdfn.js
index 9006e355..5c77565f 100644
--- a/src/coffee4client/stdfn.js
+++ b/src/coffee4client/stdfn.js
@@ -20,43 +20,46 @@ function getStandardFunctionCaller() {
   var _reqQueue = [],
     _reqCount = 1,
     _reqTimeout = null;
-  function _doRequest() {
+
+  async function _doRequest() {
     let allRequests = {},
       thisReqQueue = _reqQueue;
     _reqQueue = []; // reset instance queue for concurrent processing
     thisReqQueue.forEach((p) => {
       allRequests[p.id] = p.cb;
     });
-    // console.log(thisReqQueue)
-    fetch('/1.5/stdfun', {
-      method: 'POST', // GET, POST, PUT, DELETE, etc.
-      mode: 'cors', // no-cors, *cors, same-origin
-      cache: 'no-cache', // default, no-cache, reload, force-cache, only-if-cached
-      credentials: 'same-origin', // include, *same-origin, omit
-      headers: {
-        'Content-Type': 'application/json',
-        // 'Content-Type': 'application/x-www-form-urlencoded',
-      },
-      redirect: 'follow', // manual, *follow, error
-      referrerPolicy: 'no-referrer', // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross
-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
-      body: JSON.stringify({ q: thisReqQueue }), // body data type must match "Content-Type" header
-    })
-      .then((response) => response.json())
-      .then((ret) => {
-        // console.log(ret);
-        ret.r.forEach((r) => {
-          allRequests[r.id](r.err, r.ret);
-        });
-      })
-      .catch((err) => {
-        console.log(err);
+
+    try {
+      const ret = await RMSrv.fetch(
+        '/1.5/stdfun',
+        {
+          method: 'POST',
+          mode: 'cors',
+          cache: 'no-cache',
+          credentials: 'same-origin',
+          headers: {
+            'Content-Type': 'application/json'
+          },
+          redirect: 'follow',
+          referrerPolicy: 'no-referrer',
+          body: { q: thisReqQueue }
+        }
+      );
+      
+      ret.r.forEach((r) => {
+        allRequests[r.id](r.err, r.ret);
       });
+    } catch (err) {
+      console.log(err);
+    }
   }
+
   function requestStdFn(fnName, param, cb) {
     _reqQueue.push({ fn: fnName, p: param, cb: cb, id: _reqCount++ });
     clearTimeout(_reqTimeout);
     _reqTimeout = setTimeout(_doRequest, 50);
   }
+
   _requestStdFn = requestStdFn;
   return requestStdFn;
 }

```

---

```bash
git diff main -- src/coffee4client/wepage.coffee
```

```
diff --git a/src/coffee4client/wepage.coffee b/src/coffee4client/wepage.coffee
index 601b4433..5491f092 100644
--- a/src/coffee4client/wepage.coffee
+++ b/src/coffee4client/wepage.coffee
@@ -203,11 +203,14 @@ $ ->
     getData: ->
       that = this
       #getPropCardInfo
-      $.ajax(
-        url:amGloble.getPropCardInfo
-        data: {}
-        type:'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-      ).done((ret)->
+      try
+        ret = await RMSrv.fetch(
+          amGloble.getPropCardInfo,
+          {
+            method: 'POST',
+            body: {}
+          }
+        )
         if ret.success
           data = that._datas.pageData = ret
           # console.log data
@@ -215,11 +218,8 @@ $ ->
         else
           $("#items-pages").html ""
           alert ret.err
-        return
-      ).fail(->
+      catch e
         flashMessage "server-error"
-        return
-      )
 
     writeHtml: (cb) ->
       data = @_datas.pageData

```

---

```bash
git diff main -- src/coffee4client/wepageEdit.coffee
```

```
diff --git a/src/coffee4client/wepageEdit.coffee b/src/coffee4client/wepageEdit.coffee
index dd22b2c9..c39b9ecc 100644
--- a/src/coffee4client/wepageEdit.coffee
+++ b/src/coffee4client/wepageEdit.coffee
@@ -1,4 +1,3 @@
-
 initSummernote = (noImg) ->
   $summernote = $(".summernote")
   buttonList = [
@@ -66,11 +65,16 @@ getRMConfig = (img, cb) ->
   fd.s = img.size
   # fd.t = (if hasThumb then 1 else 0)
   $("#loading-bar-spinner").css "display", "block"
-  $.ajax(
-    url: amGloble.getRMConfig
-    data: fd
-    type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-  ).done((ret) ->
+  
+  try
+    ret = await RMSrv.fetch(
+      amGloble.getRMConfig, 
+      {
+        method: 'POST',
+        body: fd
+      }
+    )
+    
     $("#loading-bar-spinner").css "display", "none"
     if ret.key
       window.rmConfig = ret
@@ -80,12 +84,10 @@ getRMConfig = (img, cb) ->
         RMSrv.dialogAlert ret.e
       else
         flashMessage "server-error"
-    return
-  ).fail(->
+  catch e
     flashMessage "server-error"
     $("#loading-bar-spinner").css "display", "none"
-    return
-  )
+
 # getS3Config = (img, cb, hasThumb) ->
 #   fd = {}
 #   fd.ext = "jpg" #img.ext;
@@ -163,17 +165,18 @@ uploadFile = (img, cfg, cb) ->
   success = 0
   # TODO: add timeout and error handing
   $("#loading-bar-spinner").css "display", "block"
-  $.ajax(
-    url: savePicServerUrl
-    data: fd
-    type: 'post'
-    processData: false
-    contentType: false
-    # crossDomain: true
-    # headers: {'Origin': 'anonymous', "Access-Control-Request-Origin": "anonymous"}
-  ).done( ->
+  
+  try
+    ret = await RMSrv.fetch(
+      savePicServerUrl, 
+      {
+        method: 'POST',
+        body: fd
+      }
+    )
+    
     success = 1
-  ).always((ret ) ->
+    
     if success and ret.ok
       $("#loading-bar-spinner").css "display", "none"
       sUrl = ret.sUrl
@@ -182,7 +185,15 @@ uploadFile = (img, cfg, cb) ->
       payload.w = ret.width
       payload.h = ret.height
       payload.s = ret.size
-      $.ajax( url: amGloble.uploadSuccess, data: payload, type: 'post' )
+      
+      await window.RMSrv.fetch(
+        amGloble.uploadSuccess, 
+        {
+          method: 'POST',
+          body: payload
+        }
+      )
+      
       if window.isThumbImg
         # set thumbImage
         # TODO: android share
@@ -208,10 +219,29 @@ uploadFile = (img, cfg, cb) ->
         RMSrv.dialogAlert ret.e
       else
         flashMessage "server-error"
-      $.ajax( url: amGloble.uploadFail, data: {}, type: 'post' )
+      
+      await RMSrv.fetch(
+        amGloble.uploadFail, 
+        {
+          method: 'POST',
+          body: {}
+        }
+      )
+      
       cb(ret)
-    return
-  )
+  catch e
+    $("#loading-bar-spinner").css "display", "none"
+    flashMessage "server-error"
+    
+    await RMSrv.fetch(
+      amGloble.uploadFail, 
+      {
+        method: 'POST',
+        body: {}
+      }
+    )
+    
+    cb(e)
 
 uploadFile2 = (img, isThumb) ->
   file = undefined
@@ -245,17 +275,28 @@ uploadFile2 = (img, isThumb) ->
   fd.append "file", file, fileKey
   success = 0
   $("#loading-bar-spinner").css "display", "block"
-  $.ajax(
-    url: amGloble.savePicS3
-    data: fd
-    type: 'post'
-    processData: false
-    contentType: false
-    crossDomain: true
-    headers: {'Origin': 'anonymous', "Access-Control-Request-Origin": "anonymous"}
-  ).done( ->
+  
+  try
+    ret = await RMSrv.fetch(
+      amGloble.savePicS3, 
+      {
+        method: 'POST',
+        body: fd,
+        headers: {
+          'Origin': 'anonymous', 
+          'Access-Control-Request-Origin': 'anonymous'
+        },
+        mode: 'cors',  # 这相当于 crossDomain: true
+        credentials: 'omit'  # 明确不发送任何凭证，覆盖默认的 'same-origin'
+      }
+    )
+    
     success = 1
-  ).always((ret ) ->
+    handleResult(ret)
+  catch e
+    handleResult({status: 0, err: e.toString()})
+  
+  handleResult = (ret) ->
     if success or ret?.status is 204
       $("#loading-bar-spinner").css "display", "none"
       if window.isThumbImg
@@ -276,9 +317,17 @@ uploadFile2 = (img, isThumb) ->
         $(".summernote").summernote "insertImage", "http://" + window.s3config.s3bucket + "/" + window.s3co
nfig.key, window.s3config.key  unless isThumb
     else
       flashMessage "server-error"
-      $.ajax( url: amGloble.uploadFail, data: {}, type: 'post' )
-    return
-  )
+      
+      try
+        await RMSrv.fetch(
+          amGloble.uploadFail, 
+          {
+            method: 'POST',
+            body: {}
+          }
+        )
+      catch e
+        console.error "Failed to report upload failure", e
 
 readFile = (file, callback) ->
   reader = undefined
@@ -723,11 +772,12 @@ $ ->
         toggleModal "imgSelectModal"
       ).on("click", "#listUserPics", ->
         $("#imgSelectPicList").html ""
-        $.ajax(
-          url: amGloble.getUserFiles
-          data: {}
-          type: 'get' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-        ).done((ret) ->
+        try
+          ret = await RMSrv.fetch(
+            amGloble.getUserFiles, 
+            { method: 'GET' }
+          )
+          
           if ret.ok is 1
             that._datas.userData = ret
             that._datas.userData?.selected = []
@@ -737,11 +787,8 @@ $ ->
               $("#imgSelectPicList").append imgSpan
           else
             console.log "Error has happened while getting file list!"
-          return
-        ).fail(->
+        catch e
           flashMessage "server-error"
-          return
-        )
         true
       ).on("click", "#saveFrame", (e) ->
         editContent = $(".summernote").code()
@@ -839,22 +886,22 @@ $ ->
             RMSrv.dialogAlert(vars.ERR_PRO_IMG or 'Cannot remove profile images!!')
             return false
         # console.log del
-        $.ajax(
-          url: amGloble.deleteFiles
-          type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-          contentType: "application/json"
-          dataType: "json"
-          data: JSON.stringify(del)
-        ).done((ret) ->
+        
+        try
+          ret = await RMSrv.fetch(
+            amGloble.deleteFiles, 
+            {
+              body: del
+            }
+          )
+          
           if ret.ok is 1
             $('#listUserPics')[0].click()
           else
             RMSrv.dialogAlert ret.err
-          return
-        ).fail(->
+        catch e
           RMSrv.dialogAlert "sever error!, please try again later"
-          return
-        )
+        
         false
       ).on("click", "#saveCard",  (e)->
         data = that._datas.pageData
@@ -884,16 +931,17 @@ $ ->
         cancle = vars.strCancle
         comfirm = vars.strConfirm
         no_permission = vars.no_permission
-        _confirmDeleteWepage = (idx)->
+        _confirmDeleteWepage = async (idx)->
           if idx+'' isnt '2'
             return
-          $.ajax(
-            url: amGloble.deleteWecard
-            type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-            contentType: "application/json"
-            dataType: "json"
-            data: JSON.stringify(postData)
-          ).done((ret) ->
+          try
+            ret = await RMSrv.fetch(
+              amGloble.deleteWecard, 
+              {
+                body: postData
+              }
+            )
+            
             if ret.success
               if vars.isPopup
                 return window.rmCall(':ctx::cancel')
@@ -901,11 +949,8 @@ $ ->
               # window.history.back()
             else
               RMSrv.dialogAlert ret.err
-            return
-          ).fail(->
+          catch e
             RMSrv.dialogAlert "sever error!, please try again later"
-            return
-          )
         data = that._datas.pageData
         if data.card
           {_id,tp,uid} = data.card
@@ -979,21 +1024,21 @@ $ ->
         while i <= card.seq.length - 1
           newsItem.m += card.seq[i].m
           i++
-        $.ajax(
-          url: '/1.5/forum/publish/'+card._id+'?src=wecard'
-          # data: newsItem
-          type: 'get' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-        ).done((ret) ->
+        
+        try
+          ret = await RMSrv.fetch(
+            '/1.5/forum/publish/'+card._id+'?src=wecard', 
+            { method: 'GET' }
+          )
+          
           if ret.ok is 1
             RMSrv.share "hide"
             RMSrv.dialogAlert ret.msg
           else
             RMSrv.dialogAlert "Error has happened while publishing!"
-          return
-        ).fail(->
+        catch e
           flashMessage "server-error"
-          return
-        )
+        
         false
       ).on("click", "#edit-page-contents-ul > li.edit-in-summernote, #edit-page-contents-ul > li.edit-in-su
mmernote > a.edit-in-summernote", (e) ->
         # console.log that._datas
@@ -1224,7 +1269,6 @@ $ ->
         # console.log that._datas.ctClone
         that.setPreviewContents(that._datas.ctClone)
         false
-      )
 
       # $("#tpSelect").change ->
       #   amGloble.type = $("#tpSelect").val()
@@ -1302,6 +1346,7 @@ $ ->
         $("#bgs-mp3-ul").parents(".bgs-mp3").hide()
         $("#backdrop").hide()
         false
+      )
 
     isFlyer: (type, shSty) ->
       if type in ['xmas1', 'xmas2', 'spring_fest', 'flyer']
@@ -1554,10 +1599,14 @@ $ ->
       try
         doReplace = (el,idx,url,width,height,cover)->
           # alert('1-> '+$('<div>').append(el.clone()).html())
-          RMSrv.fetch url, {}, (ret)->
-            if /^Error:/.test(ret)
-              return RMSrv.dialogAlert(ret);
-            else
+          RMSrv.fetch {
+            url: url,
+            method: 'GET',
+            useNativeFetch: true,
+            callback: (err, ret)->
+              if err
+                return RMSrv.dialogAlert(err.message or err.toString());
+
               # alert(ret);
               try
                 ret = JSON.parse(ret);
@@ -1578,21 +1627,8 @@ $ ->
               if todos <= 0 and seqdone
                 ret.body = $body.html()
                 return cb ret
-                # $(el).parent().append(elem)
-                # $(el).remove()
-                # alert el.attr "class"
-                # alert el.attr "src"
-            # setTimeout (()->
-            #   elem = """<div>xxxx#{idx}</div>"""
-            #   el = $body.find(".rmReplaceTag"+idx)
-            #   alert(idx+' aft-> '+$('<div>').append(el.clone()).html())
-            #   if el
-            #     el.replaceWith(elem)
-            #   todos -= 1
-            #   if todos <= 0 and seqdone
-            #     ret.body = $body.html()
-            #     return cb ret
-            # ),1000
+          }
+
         # NOTE: handle wecard video contents when not loaded
         $body.find("[data-mpvid]").each (idx)->
           vid = $(this).attr "data-mpvid"
@@ -1871,13 +1907,15 @@ $ ->
       # opt.meta = that.setMetaImg(opt.seq, opt.meta)
       that.setShareImg(opt.meta)
       $("#loading-bar-spinner").css "display", "block"
-      $.ajax(
-        url: amGloble.savePropCard
-        type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-        contentType: "application/json; charset=utf-8"
-        dataType: "json"
-        data: data
-      ).done((ret) ->
+      
+      try
+        ret = await RMSrv.fetch(
+          amGloble.savePropCard, 
+          {
+            body: data
+          }
+        )
+        
         $("#loading-bar-spinner").css "display", "none"
         if ret.success
           flashMessage "page-saved"
@@ -1889,11 +1927,10 @@ $ ->
           console.log ret.err
           # flashMessage "server-error"
           RMSrv.dialogAlert ret.err
-        return
-      ).fail(->
+      catch e
+        $("#loading-bar-spinner").css "display", "none"
         flashMessage "server-error"
-        return
-      )
+      
       null
 
     getData: ->
@@ -1915,9 +1952,13 @@ $ ->
             else
               RMSrv.dialogAlert data.err
           return {
-            fn: $.ajax
-            cfg: {url: amGloble.getPropCardInfo, type: 'post', data: {}}
-            done: cb
+            fn: async ->
+              try
+                ret = await RMSrv.fetch(amGloble.getPropCardInfo, {method: 'POST', body: {}})
+                cb(ret)
+              catch e
+                window.hideLoading()
+                flashMessage "server-error"
           }
         else if type is 'topic'
           return {
@@ -1937,22 +1978,19 @@ $ ->
                   # $("#tpSelect").prop "disabled", "disabled"
                   # $("#tpSelect").css "-webkit-appearance", "none"
                   if amGloble.newsId
-                    $.ajax(
-                      url: amGloble.getNewsTitle
-                      data: {}
-                      type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-                    ).done((pret) ->
+                    try
+                      pret = await RMSrv.fetch(
+                        amGloble.getNewsTitle, 
+                        {method: 'POST', body: {}}
+                      )
                       if pret
                         data.title = pret.tl
                         data.desc = pret.desc
                         that.initData()
                       else
                         flashMessage "server-error"
-                      return
-                    ).fail(->
+                    catch e
                       flashMessage "server-error"
-                      return
-                    )
                   else
                     that.initData()
               else
@@ -1967,21 +2005,21 @@ $ ->
             if ret.success
               data = that._datas.pageData = ret
               if data.card and JSON.stringify(data.card).length <= 2
-                $.ajax(
-                  url: amGloble.getPropDetail
-                  data: {share:1, _id:amGloble.ml_num}
-                  type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-                ).done((pret) ->
+                try
+                  pret = await window.RMSrv.fetch(
+                    amGloble.getPropDetail, 
+                    {
+                      method: 'POST', 
+                      body: {share:1, _id:amGloble.ml_num}
+                    }
+                  )
                   if pret
                     prop = that._datas.pageData.prop = pret.detail
                     that.initData()
                   else
                     flashMessage "server-error"
-                  return
-                ).fail(->
+                catch e
                   flashMessage "server-error"
-                  return
-                )
               else
                 that.initData()
             else
@@ -1989,22 +2027,34 @@ $ ->
           return {
             update: ->
               # $("#tpSelect").val "Listing"
-            fn: $.ajax
-            cfg: {url: amGloble.getPropCardInfo, type: 'post', data: {}}
-            done: cb
+            fn: async ->
+              try
+                ret = await RMSrv.fetch(amGloble.getPropCardInfo, {method: 'POST', body: {}})
+                cb(ret)
+              catch e
+                window.hideLoading()
+                flashMessage "server-error"
           }
         else if type in amGloble.templateTypes
           return {
             update: ->
               # $("#tpSelect").val window.selectTpMap[type] or "Topic"
-            fn: $.ajax
-            cfg: {url: amGloble.getTemplate, type: 'get', data: {type: type}}
-            done: (ret) ->
-              window.hideLoading()
-              if ret.success
-                data = that._datas.pageData = ret.content
-                that.initData()
-              else
+            fn: async ->
+              try
+                ret = await RMSrv.fetch(
+                  amGloble.getTemplate + "?type=" + type, 
+                  {
+                    method: 'GET', 
+                  }
+                )
+                window.hideLoading()
+                if ret.success
+                  data = that._datas.pageData = ret.content
+                  that.initData()
+                else
+                  flashMessage "server-error"
+              catch e
+                window.hideLoading()
                 flashMessage "server-error"
           }
         else
@@ -2021,11 +2071,14 @@ $ ->
 
     getMusicLinks: ->
       that = this
-      $.ajax(
-        url: amGloble.getMusicList
-        data: {}
-        type: 'get' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-      ).done((ret) ->
+      try
+        ret = await RMSrv.fetch(
+          amGloble.getMusicList, 
+          {
+            method: 'GET'
+          }
+        )
+        
         if ret
           d = that._datas.musicList = ret
           arrHtml = []
@@ -2038,11 +2091,8 @@ $ ->
           $("#bgs-mp3-ul").html arrHtml.join("")
         else
           flashMessage "server-error"
-        return
-      ).fail(->
+      catch e
         flashMessage "server-error"
-        return
-      )
 
     isOwnImg: (src) ->
       #TODO: test can get image

```

---

```bash
git diff main -- src/coffee4client/wepageEditNew.coffee
```

```
diff --git a/src/coffee4client/wepageEditNew.coffee b/src/coffee4client/wepageEditNew.coffee
index ad2d081b..dfad1c47 100644
--- a/src/coffee4client/wepageEditNew.coffee
+++ b/src/coffee4client/wepageEditNew.coffee
@@ -1,4 +1,3 @@
-
 initSummernote = (noImg) ->
   $summernote = $(".summernote")
   buttonList = [
@@ -49,22 +48,22 @@ getS3Config = (img, cb, hasThumb) ->
   fd.s = img.size
   fd.t = (if hasThumb then 1 else 0)
   $("#loading-bar-spinner").css "display", "block"
-  $.ajax(
-    url: amGloble.getS3Config
-    data: fd
-    type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-  ).done((ret) ->
+  try
+    ret = await RMSrv.fetch(
+      amGloble.getS3Config,
+      {
+        method: 'POST',
+        body: fd
+      }
+    )
     if ret.key
       $("#loading-bar-spinner").css "display", "none"
       window.s3config = ret
       cb() if cb
     else
       flashMessage "server-error"
-    return
-  ).fail(->
+  catch e
     flashMessage "server-error"
-    return
-  )
 
 uploadFile = (img, isThumb) ->
   file = undefined
@@ -95,17 +94,18 @@ uploadFile = (img, isThumb) ->
   fd.append "file", file, fileKey
   success = 0
   $("#loading-bar-spinner").css "display", "block"
-  $.ajax(
-    url: amGloble.savePicS3
-    data: fd
-    type: 'post'
-    processData: false
-    contentType: false
-    crossDomain: true
-    headers: {'Origin': 'anonymous', "Access-Control-Request-Origin": "anonymous"}
-  ).done( ->
+  try
+    ret = await RMSrv.fetch(
+      amGloble.savePicS3,
+      {
+        method: 'POST',
+        body: fd,
+        headers: {'Origin': 'anonymous', "Access-Control-Request-Origin": "anonymous"},
+        mode: 'cors',  # 这相当于 crossDomain: true
+        credentials: 'omit'  # 明确不发送任何凭证，覆盖默认的 'same-origin'
+      }
+    )
     success = 1
-  ).always((ret ) ->
     if success or ret?.status is 204
       $("#loading-bar-spinner").css "display", "none"
       if window.isThumbImg
@@ -126,9 +126,15 @@ uploadFile = (img, isThumb) ->
         $(".summernote").summernote "insertImage", "http://" + window.s3config.s3bucket + "/" + window.s3co
nfig.key, window.s3config.key  unless isThumb
     else
       flashMessage "server-error"
-      $.ajax( url: amGloble.uploadFail, data: {}, type: 'post' )
-    return
-  )
+      await RMSrv.fetch(
+        amGloble.uploadFail,
+        {
+          method: 'POST',
+          body: {}
+        }
+      )
+  catch e
+    flashMessage "server-error"
 
 readFile = (file, callback) ->
   reader = undefined
@@ -545,11 +551,13 @@ $ ->
         toggleModal "imgSelectModal"
       ).on("click", "#listUserPics", ->
         $("#imgSelectPicList").html ""
-        $.ajax(
-          url: amGloble.getUserFiles
-          data: {}
-          type: 'get' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-        ).done((ret) ->
+        try
+          ret = await RMSrv.fetch(
+            amGloble.getUserFiles,
+            {
+              method: 'GET'
+            }
+          )
           if ret.ok is 1
             that._datas.userData = ret
             that._datas.userData?.selected = []
@@ -559,11 +567,8 @@ $ ->
               $("#imgSelectPicList").append imgSpan
           else
             console.log "Error has happened while getting file list!"
-          return
-        ).fail(->
+        catch e
           flashMessage "server-error"
-          return
-        )
         true
       ).on("click", "#saveFrame", (e) ->
         editContent = $(".summernote").summernote('code')
@@ -646,22 +651,19 @@ $ ->
             RMSrv.dialogAlert(vars.ERR_PRO_IMG or 'Cannot remove profile images!!')
             return false
         # console.log del
-        $.ajax(
-          url: amGloble.deleteFiles
-          type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-          contentType: "application/json"
-          dataType: "json"
-          data: JSON.stringify(del)
-        ).done((ret) ->
+        try
+          ret = await RMSrv.fetch(
+            amGloble.deleteFiles,
+            {
+              body: del
+            }
+          )
           if ret.ok is 1
             $('#listUserPics')[0].click()
           else
             RMSrv.dialogAlert ret.err
-          return
-        ).fail(->
+        catch e
           RMSrv.dialogAlert "sever error!, please try again later"
-          return
-        )
         false
       ).on("click", "#saveCard",  (e)->
         data = that._datas.pageData
@@ -736,21 +738,20 @@ $ ->
         while i <= card.seq.length - 1
           newsItem.m += card.seq[i].m
           i++
-        $.ajax(
-          url: amGloble.publishToNews
-          data: newsItem
-          type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-        ).done((ret) ->
+        try
+          ret = await RMSrv.fetch(
+            amGloble.publishToNews,
+            {
+              body: newsItem
+            }
+          )
           if ret.ok is 1
             RMSrv.share "hide"
             RMSrv.dialogAlert ret.msg
           else
             RMSrv.dialogAlert "Error has happened while publishing!"
-          return
-        ).fail(->
+        catch e
           flashMessage "server-error"
-          return
-        )
         false
       ).on("click", "#edit-page-contents-ul > li.edit-in-summernote, #edit-page-contents-ul > li.edit-in-su
mmernote > a.edit-in-summernote", (e) ->
         # console.log that._datas
@@ -1485,13 +1486,13 @@ $ ->
 
 
       $("#loading-bar-spinner").css "display", "block"
-      $.ajax(
-        url: amGloble.savePropCard
-        type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-        contentType: "application/json; charset=utf-8"
-        dataType: "json"
-        data: JSON.stringify(opt)
-      ).done((ret) ->
+      try
+        ret = await RMSrv.fetch(
+          amGloble.savePropCard,
+          {
+            body: opt
+          }
+        )
         $("#loading-bar-spinner").css "display", "none"
         if ret.success
           that._datas.pageData.card._id = ret._id
@@ -1499,11 +1500,8 @@ $ ->
           that.enableBtns()
         else
           console.log "Save Error"
-        return
-      ).fail(->
+      catch e
         flashMessage "server-error"
-        return
-      )
       null
 
     getData: ->
@@ -1518,8 +1516,11 @@ $ ->
             else
               RMSrv.dialogAlert data.err
           return {
-            fn: $.ajax
-            cfg: {url: amGloble.getPropCardInfo, type: 'get', data: {}}
+            fn: RMSrv.fetch
+            cfg: {
+              url: amGloble.getPropCardInfo,
+              method: 'GET',
+            }
             done: cb
           }
         else if type is 'topic'
@@ -1536,22 +1537,22 @@ $ ->
                 # $("#tpSelect").prop "disabled", "disabled"
                 # $("#tpSelect").css "-webkit-appearance", "none"
                 if amGloble.newsId
-                  $.ajax(
-                    url: amGloble.getNewsTitle
-                    data: {}
-                    type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-                  ).done((pret) ->
+                  try
+                    pret = await RMSrv.fetch(
+                      amGloble.getNewsTitle,
+                      {
+                        method: 'POST',
+                        body: {}
+                      }
+                    )
                     if pret
                       data.title = pret.tl
                       data.desc = pret.desc
                       that.initData()
                     else
                       flashMessage "server-error"
-                    return
-                  ).fail(->
+                  catch e
                     flashMessage "server-error"
-                    return
-                  )
                 else
                   that.initData()
               else
@@ -1565,21 +1566,21 @@ $ ->
             if ret.success
               data = that._datas.pageData = ret
               if data.card and JSON.stringify(data.card).length <= 2
-                $.ajax(
-                  url: amGloble.getPropDetail
-                  data: {}
-                  type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-                ).done((pret) ->
+                try
+                  pret = await RMSrv.fetch(
+                    amGloble.getPropDetail,
+                    {
+                      method: 'POST',
+                      body: {}
+                    }
+                  )
                   if pret
                     prop = that._datas.pageData.prop = pret.detail
                     that.initData()
                   else
                     flashMessage "server-error"
-                  return
-                ).fail(->
+                catch e
                   flashMessage "server-error"
-                  return
-                )
               else
                 that.initData()
             else
@@ -1587,16 +1588,22 @@ $ ->
           return {
             update: ->
               # $("#tpSelect").val "Listing"
-            fn: $.ajax
-            cfg: {url: amGloble.getPropCardInfo, type: 'get', data: {}}
+            fn: RMSrv.fetch
+            cfg: {
+              url: amGloble.getPropCardInfo,
+              method: 'GET'
+            }
             done: cb
           }
         else if type in amGloble.templateTypes
           return {
             update: ->
               # $("#tpSelect").val window.selectTpMap[type] or "Topic"
-            fn: $.ajax
-            cfg: {url: amGloble.getTemplate, type: 'get', data: {type: type}}
+            fn: RMSrv.fetch
+            cfg: {
+              url: amGloble.getTemplate + "?type=" + type,
+              method: 'GET',
+            }
             done: (ret) ->
               if ret.success
                 data = that._datas.pageData = ret.content
@@ -1611,17 +1618,23 @@ $ ->
       actions = getDataMap(amGloble.type.toLowerCase(), amGloble.action?.toLowerCase())
       actions.update() if actions.update
       actions.cfg = actions.data  unless actions.cfg
-      actions.fn(actions.cfg).done(actions.done).fail(-> flashMessage 'server-error';return) if actions.don
e
+      try
+        ret = await actions.fn(actions.cfg)
+        actions.done(ret) if actions.done
+      catch e
+        flashMessage 'server-error'
       #this = actions in getPageContent
       actions.fn(that, actions.cfg, actions.cb) if actions.cb
 
     getMusicLinks: ->
       that = this
-      $.ajax(
-        url: amGloble.getMusicList
-        data: {}
-        type: 'get' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
-      ).done((ret) ->
+      try
+        ret = await RMSrv.fetch(
+          amGloble.getMusicList,
+          {
+            method: 'GET'
+          }
+        )
         if ret
           d = that._datas.musicList = ret
           arrHtml = []
@@ -1634,11 +1647,8 @@ $ ->
           $("#bgs-mp3-ul").html arrHtml.join("")
         else
           flashMessage "server-error"
-        return
-      ).fail(->
+      catch e
         flashMessage "server-error"
-        return
-      )
 
     isOwnImg: (src) ->
       #TODO: test can get image

```

---

```bash
git diff main -- src/webpack/base.config.js
```

```
diff --git a/src/webpack/base.config.js b/src/webpack/base.config.js
index 12ee18a8..76d4fa4c 100644
--- a/src/webpack/base.config.js
+++ b/src/webpack/base.config.js
@@ -97,6 +97,10 @@ module.exports = {
   // },
   resolve: {
     extensions: ['.wasm', '.mjs', '.js', '.json','.vue','*'],
+    alias: {
+      // 将 vue-resource 别名为我们的适配器文件
+      'vue-resource': path.resolve(__dirname, '../adapters/vue-resource-adapter.js')
+    }
   },
   optimization: {
     namedModules: true, // NamedModulesPlugin()

```

---

```bash
git diff main -- src/webroot/public/js/entry/appProjectList.js
```

```

alue,dynamic:e.dynamic}}))):null,a=e.attrsMap["v-bind"];return!i&&!a||r||(o+=",null"),i&&(o+=","+i),a&&(o+=(
i?"":",null")+","+a),o+")"}(e,t);var n;if(e.component)n=function(e,t,n){var r=t.inlineTemplate?null:Ua(t,n,!
0);return"_c("+e+","+Ea(t,n)+(r?","+r:"")+")"}(e.component,e,t);else{var r;(!e.plain||e.pre&&t.maybeComponen
t(e))&&(r=Ea(e,t));var o=e.inlineTemplate?null:Ua(e,t,!0);n="_c('"+e.tag+"'"+(r?","+r:"")+(o?","+o:"")+")"}f
or(var i=0;i<t.transforms.length;i++)n=t.transforms[i](e,n);return n}return Ua(e,t)||"void 0"}function Da(e,
t){e.staticProcessed=!0;var n=t.pre;return e.pre&&(t.pre=e.pre),t.staticRenderFns.push("with(this){return "+
Oa(e,t)+"}"),t.pre=n,"_m("+(t.staticRenderFns.length-1)+(e.staticInFor?",true":"")+")"}function Fa(e,t){if(e
.onceProcessed=!0,e.if&&!e.ifProcessed)return Na(e,t);if(e.staticInFor){for(var n="",r=e.parent;r;){if(r.for
){n=r.key;break}r=r.parent}return n?"_o("+Oa(e,t)+","+t.onceId+++","+n+")":Oa(e,t)}return Da(e,t)}function N
a(e,t,n,r){return e.ifProcessed=!0,function e(t,n,r,o){if(!t.length)return o||"_e()";var i=t.shift();return
i.exp?"("+i.exp+")?"+a(i.block)+":"+e(t,n,r,o):""+a(i.block);function a(e){return r?r(e,n):e.once?Fa(e,n):Oa
(e,n)}}(e.ifConditions.slice(),t,n,r)}function Ia(e,t,n,r){var o=e.for,i=e.alias,a=e.iterator1?","+e.iterato
r1:"",s=e.iterator2?","+e.iterator2:"";return e.forProcessed=!0,(r||"_l")+"(("+o+"),function("+i+a+s+"){retu
rn "+(n||Oa)(e,t)+"})"}function Ea(e,t){var n="{",r=function(e,t){var n=e.directives;if(n){var r,o,i,a,s="di
rectives:[",l=!1;for(r=0,o=n.length;r<o;r++){i=n[r],a=!0;var c=t.directives[i.name];c&&(a=!!c(e,i,t.warn)),a
&&(l=!0,s+='{name:"'+i.name+'",rawName:"'+i.rawName+'"'+(i.value?",value:("+i.value+"),expression:"+JSON.str
ingify(i.value):"")+(i.arg?",arg:"+(i.isDynamicArg?i.arg:'"'+i.arg+'"'):"")+(i.modifiers?",modifiers:"+JSON.
stringify(i.modifiers):"")+"},")}return l?s.slice(0,-1)+"]":void 0}}(e,t);r&&(n+=r+","),e.key&&(n+="key:"+e.
key+","),e.ref&&(n+="ref:"+e.ref+","),e.refInFor&&(n+="refInFor:true,"),e.pre&&(n+="pre:true,"),e.component&
&(n+='tag:"'+e.tag+'",');for(var o=0;o<t.dataGenFns.length;o++)n+=t.dataGenFns[o](e);if(e.attrs&&(n+="attrs:
"+Ba(e.attrs)+","),e.props&&(n+="domProps:"+Ba(e.props)+","),e.events&&(n+=Sa(e.events,!1)+","),e.nativeEven
ts&&(n+=Sa(e.nativeEvents,!0)+","),e.slotTarget&&!e.slotScope&&(n+="slot:"+e.slotTarget+","),e.scopedSlots&&
(n+=function(e,t,n){var r=e.for||Object.keys(t).some((function(e){var n=t[e];return n.slotTargetDynamic||n.i
f||n.for||La(n)})),o=!!e.if;if(!r)for(var i=e.parent;i;){if(i.slotScope&&i.slotScope!==ra||i.for){r=!0;break
}i.if&&(o=!0),i=i.parent}var a=Object.keys(t).map((function(e){return Ra(t[e],n)})).join(",");return"scopedS
lots:_u(["+a+"]"+(r?",null,true":"")+(!r&&o?",null,false,"+function(e){for(var t=5381,n=e.length;n;)t=33*t^e
.charCodeAt(--n);return t>>>0}(a):"")+")"}(e,e.scopedSlots,t)+","),e.model&&(n+="model:{value:"+e.model.valu
e+",callback:"+e.model.callback+",expression:"+e.model.expression+"},"),e.inlineTemplate){var i=function(e,t
){var n=e.children[0];if(n&&1===n.type){var r=Ta(n,t.options);return"inlineTemplate:{render:function(){"+r.r
ender+"},staticRenderFns:["+r.staticRenderFns.map((function(e){return"function(){"+e+"}"})).join(",")+"]}"}}
(e,t);i&&(n+=i+",")}return n=n.replace(/,$/,"")+"}",e.dynamicAttrs&&(n="_b("+n+',"'+e.tag+'",'+Ba(e.dynamicA
ttrs)+")"),e.wrapData&&(n=e.wrapData(n)),e.wrapListeners&&(n=e.wrapListeners(n)),n}function La(e){return 1==
=e.type&&("slot"===e.tag||e.children.some(La))}function Ra(e,t){var n=e.attrsMap["slot-scope"];if(e.if&&!e.i
fProcessed&&!n)return Na(e,t,Ra,"null");if(e.for&&!e.forProcessed)return Ia(e,t,Ra);var r=e.slotScope===ra?"
":String(e.slotScope),o="function("+r+"){return "+("template"===e.tag?e.if&&n?"("+e.if+")?"+(Ua(e,t)||"undef
ined")+":undefined":Ua(e,t)||"undefined":Oa(e,t))+"}",i=r?"":",proxy:true";return"{key:"+(e.slotTarget||'"de
fault"')+",fn:"+o+i+"}"}function Ua(e,t,n,r,o){var i=e.children;if(i.length){var a=i[0];if(1===i.length&&a.f
or&&"template"!==a.tag&&"slot"!==a.tag){var s=n?t.maybeComponent(a)?",1":",0":"";return""+(r||Oa)(a,t)+s}var
 l=n?function(e,t){for(var n=0,r=0;r<e.length;r++){var o=e[r];if(1===o.type){if(Va(o)||o.ifConditions&&o.ifC
onditions.some((function(e){return Va(e.block)}))){n=2;break}(t(o)||o.ifConditions&&o.ifConditions.some((fun
ction(e){return t(e.block)})))&&(n=1)}}return n}(i,t.maybeComponent):0,c=o||za;return"["+i.map((function(e){
return c(e,t)})).join(",")+"]"+(l?","+l:"")}}function Va(e){return void 0!==e.for||"template"===e.tag||"slot
"===e.tag}function za(e,t){return 1===e.type?Oa(e,t):3===e.type&&e.isComment?(r=e,"_e("+JSON.stringify(r.tex
t)+")"):"_v("+(2===(n=e).type?n.expression:qa(JSON.stringify(n.text)))+")";var n,r}function Ba(e){for(var t=
"",n="",r=0;r<e.length;r++){var o=e[r],i=qa(o.value);o.dynamic?n+=o.name+","+i+",":t+='"'+o.name+'":'+i+","}
return t="{"+t.slice(0,-1)+"}",n?"_d("+t+",["+n.slice(0,-1)+"])":t}function qa(e){return e.replace(/\u2028/g
,"\\u2028").replace(/\u2029/g,"\\u2029")}function Wa(e,t){try{return new Function(e)}catch(n){return t.push(
{err:n,code:e}),P}}function Ha(e){var t=Object.create(null);return function(n,r,o){(r=A({},r)).warn,delete r
.warn;var i=r.delimiters?String(r.delimiters)+n:n;if(t[i])return t[i];var a=e(n,r),s={},l=[];return s.render
=Wa(a.render,l),s.staticRenderFns=a.staticRenderFns.map((function(e){return Wa(e,l)})),t[i]=s}}new RegExp("\
\b"+"do,if,for,let,new,try,var,case,else,with,await,break,catch,class,const,super,throw,while,yield,delete,e
xport,import,return,switch,default,extends,finally,continue,debugger,function,arguments".split(",").join("\\
b|\\b")+"\\b");var Ja,Ga,Ka=(Ja=function(e,t){var n=function(e,t){Ii=t.warn||jr,Vi=t.isPreTag||T,zi=t.mustUs
eProp||T,Bi=t.getTagNamespace||T,t.isReservedTag,Li=Sr(t.modules,"transformNode"),Ri=Sr(t.modules,"preTransf
ormNode"),Ui=Sr(t.modules,"postTransformNode"),Ei=t.delimiters;var n,r,o=[],i=!1!==t.preserveWhitespace,a=t.
whitespace,s=!1,l=!1;function c(e){if(d(e),s||e.processed||(e=ia(e,t)),o.length||e===n||n.if&&(e.elseif||e.e
lse)&&sa(n,{exp:e.elseif,block:e}),r&&!e.forbidden)if(e.elseif||e.else)a=e,(c=function(e){for(var t=e.length
;t--;){if(1===e[t].type)return e[t];e.pop()}}(r.children))&&c.if&&sa(c,{exp:a.elseif,block:a});else{if(e.slo
tScope){var i=e.slotTarget||'"default"';(r.scopedSlots||(r.scopedSlots={}))[i]=e}r.children.push(e),e.parent
=r}var a,c;e.children=e.children.filter((function(e){return!e.slotScope})),d(e),e.pre&&(s=!1),Vi(e.tag)&&(l=
!1);for(var u=0;u<Ui.length;u++)Ui[u](e,t)}function d(e){if(!l)for(var t;(t=e.children[e.children.length-1])
&&3===t.type&&" "===t.text;)e.children.pop()}return function(e,t){for(var n,r,o=[],i=t.expectHTML,a=t.isUnar
yTag||T,s=t.canBeLeftOpenTag||T,l=0;e;){if(n=e,r&&Ai(r)){var c=0,d=r.toLowerCase(),u=Mi[d]||(Mi[d]=new RegEx
p("([\\s\\S]*?)(</"+d+"[^>]*>)","i")),p=e.replace(u,(function(e,n,r){return c=r.length,Ai(d)||"noscript"===d
||(n=n.replace(/<!\--([\s\S]*?)-->/g,"$1").replace(/<!\[CDATA\[([\s\S]*?)]]>/g,"$1")),Fi(d,n)&&(n=n.slice(1)
),t.chars&&t.chars(n),""}));l+=e.length-p.length,e=p,S(d,l-c,l)}else{var f=e.indexOf("<");if(0===f){if(Si.te
st(e)){var v=e.indexOf("--\x3e");if(v>=0){t.shouldKeepComment&&t.comment(e.substring(4,v),l,l+v+3),C(v+3);co
ntinue}}if($i.test(e)){var m=e.indexOf("]>");if(m>=0){C(m+2);continue}}var h=e.match(ji);if(h){C(h[0].length
);continue}var g=e.match(ki);if(g){var y=l;C(g[0].length),S(g[1],y,l);continue}var b=k();if(b){j(b),Fi(b.tag
Name,e)&&C(1);continue}}var w=void 0,_=void 0,x=void 0;if(f>=0){for(_=e.slice(f);!(ki.test(_)||xi.test(_)||S
i.test(_)||$i.test(_)||(x=_.indexOf("<",1))<0);)f+=x,_=e.slice(f);w=e.substring(0,f)}f<0&&(w=e),w&&C(w.lengt
h),t.chars&&w&&t.chars(w,l-w.length,l)}if(e===n){t.chars&&t.chars(e);break}}function C(t){l+=t,e=e.substring
(t)}function k(){var t=e.match(xi);if(t){var n,r,o={tagName:t[1],attrs:[],start:l};for(C(t[0].length);!(n=e.
match(Ci))&&(r=e.match(bi)||e.match(yi));)r.start=l,C(r[0].length),r.end=l,o.attrs.push(r);if(n)return o.una
rySlash=n[1],C(n[0].length),o.end=l,o}}function j(e){var n=e.tagName,l=e.unarySlash;i&&("p"===r&&gi(n)&&S(r)
,s(n)&&r===n&&S(n));for(var c=a(n)||!!l,d=e.attrs.length,u=new Array(d),p=0;p<d;p++){var f=e.attrs[p],v=f[3]
||f[4]||f[5]||"",m="a"===n&&"href"===f[1]?t.shouldDecodeNewlinesForHref:t.shouldDecodeNewlines;u[p]={name:f[
1],value:Ni(v,m)}}c||(o.push({tag:n,lowerCasedTag:n.toLowerCase(),attrs:u,start:e.start,end:e.end}),r=n),t.s
tart&&t.start(n,u,c,e.start,e.end)}function S(e,n,i){var a,s;if(null==n&&(n=l),null==i&&(i=l),e)for(s=e.toLo
werCase(),a=o.length-1;a>=0&&o[a].lowerCasedTag!==s;a--);else a=0;if(a>=0){for(var c=o.length-1;c>=a;c--)t.e
nd&&t.end(o[c].tag,n,i);o.length=a,r=a&&o[a-1].tag}else"br"===s?t.start&&t.start(e,[],!0,n,i):"p"===s&&(t.st
art&&t.start(e,[],!1,n,i),t.end&&t.end(e,n,i))}S()}(e,{warn:Ii,expectHTML:t.expectHTML,isUnaryTag:t.isUnaryT
ag,canBeLeftOpenTag:t.canBeLeftOpenTag,shouldDecodeNewlines:t.shouldDecodeNewlines,shouldDecodeNewlinesForHr
ef:t.shouldDecodeNewlinesForHref,shouldKeepComment:t.comments,outputSourceRange:t.outputSourceRange,start:fu
nction(e,i,a,d,u){var p=r&&r.ns||Bi(e);K&&"svg"===p&&(i=function(e){for(var t=[],n=0;n<e.length;n++){var r=e
[n];ua.test(r.name)||(r.name=r.name.replace(pa,""),t.push(r))}return t}(i));var f,v=oa(e,i,r);p&&(v.ns=p),"s
tyle"!==(f=v).tag&&("script"!==f.tag||f.attrsMap.type&&"text/javascript"!==f.attrsMap.type)||re()||(v.forbid
den=!0);for(var m=0;m<Ri.length;m++)v=Ri[m](v,t)||v;s||(function(e){null!=Fr(e,"v-pre")&&(e.pre=!0)}(v),v.pr
e&&(s=!0)),Vi(v.tag)&&(l=!0),s?function(e){var t=e.attrsList,n=t.length;if(n)for(var r=e.attrs=new Array(n),
o=0;o<n;o++)r[o]={name:t[o].name,value:JSON.stringify(t[o].value)},null!=t[o].start&&(r[o].start=t[o].start,
r[o].end=t[o].end);else e.pre||(e.plain=!0)}(v):v.processed||(aa(v),function(e){var t=Fr(e,"v-if");if(t)e.if
=t,sa(e,{exp:t,block:e});else{null!=Fr(e,"v-else")&&(e.else=!0);var n=Fr(e,"v-else-if");n&&(e.elseif=n)}}(v)
,function(e){null!=Fr(e,"v-once")&&(e.once=!0)}(v)),n||(n=v),a?c(v):(r=v,o.push(v))},end:function(e,t,n){var
 i=o[o.length-1];o.length-=1,r=o[o.length-1],c(i)},chars:function(e,t,n){if(r&&(!K||"textarea"!==r.tag||r.at
trsMap.placeholder!==e)){var o,c,d,u=r.children;(e=l||e.trim()?"script"===(o=r).tag||"style"===o.tag?e:na(e)
:u.length?a?"condense"===a&&ea.test(e)?"":" ":i?" ":"":"")&&(l||"condense"!==a||(e=e.replace(ta," ")),!s&&"
"!==e&&(c=function(e,t){var n=t?pi(t):di;if(n.test(e)){for(var r,o,i,a=[],s=[],l=n.lastIndex=0;r=n.exec(e);)
{(o=r.index)>l&&(s.push(i=e.slice(l,o)),a.push(JSON.stringify(i)));var c=Cr(r[1].trim());a.push("_s("+c+")")
,s.push({"@binding":c}),l=o+r[0].length}return l<e.length&&(s.push(i=e.slice(l)),a.push(JSON.stringify(i))),
{expression:a.join("+"),tokens:s}}}(e,Ei))?d={type:2,expression:c.expression,tokens:c.tokens,text:e}:" "===e
&&u.length&&" "===u[u.length-1].text||(d={type:3,text:e}),d&&u.push(d))}},comment:function(e,t,n){if(r){var
o={type:3,text:e,isComment:!0};r.children.push(o)}}}),n}(e.trim(),t);!1!==t.optimize&&function(e,t){e&&(va=y
a(t.staticKeys||""),ma=t.isReservedTag||T,function e(t){if(t.static=function(e){return 2!==e.type&&(3===e.ty
pe||!(!e.pre&&(e.hasBindings||e.if||e.for||m(e.tag)||!ma(e.tag)||function(e){for(;e.parent;){if("template"!=
=(e=e.parent).tag)return!1;if(e.for)return!0}return!1}(e)||!Object.keys(e).every(va))))}(t),1===t.type){if(!
ma(t.tag)&&"slot"!==t.tag&&null==t.attrsMap["inline-template"])return;for(var n=0,r=t.children.length;n<r;n+
+){var o=t.children[n];e(o),o.static||(t.static=!1)}if(t.ifConditions)for(var i=1,a=t.ifConditions.length;i<
a;i++){var s=t.ifConditions[i].block;e(s),s.static||(t.static=!1)}}}(e),function e(t,n){if(1===t.type){if((t
.static||t.once)&&(t.staticInFor=n),t.static&&t.children.length&&(1!==t.children.length||3!==t.children[0].t
ype))return void(t.staticRoot=!0);if(t.staticRoot=!1,t.children)for(var r=0,o=t.children.length;r<o;r++)e(t.
children[r],n||!!t.for);if(t.ifConditions)for(var i=1,a=t.ifConditions.length;i<a;i++)e(t.ifConditions[i].bl
ock,n)}}(e,!1))}(n,t);var r=Ta(n,t);return{ast:n,render:r.render,staticRenderFns:r.staticRenderFns}},functio
n(e){function t(t,n){var r=Object.create(e),o=[],i=[];if(n)for(var a in n.modules&&(r.modules=(e.modules||[]
).concat(n.modules)),n.directives&&(r.directives=A(Object.create(e.directives||null),n.directives)),n)"modul
es"!==a&&"directives"!==a&&(r[a]=n[a]);r.warn=function(e,t,n){(n?i:o).push(e)};var s=Ja(t.trim(),r);return s
.errors=o,s.tips=i,s}return{compile:t,compileToFunctions:Ha(t)}})(ga),Za=(Ka.compile,Ka.compileToFunctions);
function Xa(e){return(Ga=Ga||document.createElement("div")).innerHTML=e?'<a href="\n"/>':'<div a="\n"/>',Ga.
innerHTML.indexOf("&#10;")>0}var Ya=!!W&&Xa(!1),Qa=!!W&&Xa(!0),es=w((function(e){var t=Kn(e);return t&&t.inn
erHTML})),ts=xn.prototype.$mount;return xn.prototype.$mount=function(e,t){if((e=e&&Kn(e))===document.body||e
===document.documentElement)return this;var n=this.$options;if(!n.render){var r=n.template;if(r)if("string"=
=typeof r)"#"===r.charAt(0)&&(r=es(r));else{if(!r.nodeType)return this;r=r.innerHTML}else e&&(r=function(e){
if(e.outerHTML)return e.outerHTML;var t=document.createElement("div");return t.appendChild(e.cloneNode(!0)),
t.innerHTML}(e));if(r){var o=Za(r,{outputSourceRange:!1,shouldDecodeNewlines:Ya,shouldDecodeNewlinesForHref:
Qa,delimiters:n.delimiters,comments:n.comments},this),i=o.render,a=o.staticRenderFns;n.render=i,n.staticRend
erFns=a}}return ts.call(this,e,t)},xn.compile=Za,xn}()}).call(this,n("./node_modules/webpack/buildin/global.
js"),n("./node_modules/timers-browserify/main.js").setImmediate)},"./node_modules/webpack/buildin/global.js"
:function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==t
ypeof window&&(n=window)}e.exports=n},1:function(e,t){}});
\ No newline at end of file
+e.exports=function(){"use strict";var e=Object.freeze({});function r(e){return null==e}function o(e){return
 null!=e}function i(e){return!0===e}function a(e){return"string"==typeof e||"number"==typeof e||"symbol"==ty
peof e||"boolean"==typeof e}function s(e){return null!==e&&"object"==typeof e}var l=Object.prototype.toStrin
g;function c(e){return"[object Object]"===l.call(e)}function d(e){var t=parseFloat(String(e));return t>=0&&M
ath.floor(t)===t&&isFinite(e)}function u(e){return o(e)&&"function"==typeof e.then&&"function"==typeof e.cat
ch}function p(e){return null==e?"":Array.isArray(e)||c(e)&&e.toString===l?JSON.stringify(e,null,2):String(e)
}function f(e){var t=parseFloat(e);return isNaN(t)?e:t}function v(e,t){for(var n=Object.create(null),r=e.spl
it(","),o=0;o<r.length;o++)n[r[o]]=!0;return t?function(e){return n[e.toLowerCase()]}:function(e){return n[e
]}}var m=v("slot,component",!0),h=v("key,ref,slot,slot-scope,is");function g(e,t){if(e.length){var n=e.index
Of(t);if(n>-1)return e.splice(n,1)}}var y=Object.prototype.hasOwnProperty;function b(e,t){return y.call(e,t)
}function _(e){var t=Object.create(null);return function(n){return t[n]||(t[n]=e(n))}}var w=/-(\w)/g,x=_((fu
nction(e){return e.replace(w,(function(e,t){return t?t.toUpperCase():""}))})),C=_((function(e){return e.char
At(0).toUpperCase()+e.slice(1)})),k=/\B([A-Z])/g,j=_((function(e){return e.replace(k,"-$1").toLowerCase()}))
,S=Function.prototype.bind?function(e,t){return e.bind(t)}:function(e,t){function n(n){var r=arguments.lengt
h;return r?r>1?e.apply(t,arguments):e.call(t,n):e.call(t)}return n._length=e.length,n};function $(e,t){t=t||
0;for(var n=e.length-t,r=new Array(n);n--;)r[n]=e[n+t];return r}function A(e,t){for(var n in t)e[n]=t[n];ret
urn e}function M(e){for(var t={},n=0;n<e.length;n++)e[n]&&A(t,e[n]);return t}function P(e,t,n){}var T=functi
on(e,t,n){return!1},O=function(e){return e};function D(e,t){if(e===t)return!0;var n=s(e),r=s(t);if(!n||!r)re
turn!n&&!r&&String(e)===String(t);try{var o=Array.isArray(e),i=Array.isArray(t);if(o&&i)return e.length===t.
length&&e.every((function(e,n){return D(e,t[n])}));if(e instanceof Date&&t instanceof Date)return e.getTime(
)===t.getTime();if(o||i)return!1;var a=Object.keys(e),l=Object.keys(t);return a.length===l.length&&a.every((
function(n){return D(e[n],t[n])}))}catch(e){return!1}}function F(e,t){for(var n=0;n<e.length;n++)if(D(e[n],t
))return n;return-1}function N(e){var t=!1;return function(){t||(t=!0,e.apply(this,arguments))}}var I="data-
server-rendered",L=["component","directive","filter"],R=["beforeCreate","created","beforeMount","mounted","b
eforeUpdate","updated","beforeDestroy","destroyed","activated","deactivated","errorCaptured","serverPrefetch
"],E={optionMergeStrategies:Object.create(null),silent:!1,productionTip:!1,devtools:!1,performance:!1,errorH
andler:null,warnHandler:null,ignoredElements:[],keyCodes:Object.create(null),isReservedTag:T,isReservedAttr:
T,isUnknownElement:T,getTagNamespace:P,parsePlatformTagName:O,mustUseProp:T,async:!0,_lifecycleHooks:R},U=/a
-zA-Z\u00B7\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u037D\u037F-\u1FFF\u200C-\u200D\u203F-\u2040\u2070-\u218F\u2C00
-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD/;function V(e,t,n,r){Object.defineProperty(e,t,{value:n,enume
rable:!!r,writable:!0,configurable:!0})}var z,B=new RegExp("[^"+U.source+".$_\\d]"),q="__proto__"in{},W="und
efined"!=typeof window,J="undefined"!=typeof WXEnvironment&&!!WXEnvironment.platform,H=J&&WXEnvironment.plat
form.toLowerCase(),K=W&&window.navigator.userAgent.toLowerCase(),G=K&&/msie|trident/.test(K),Z=K&&K.indexOf(
"msie 9.0")>0,Y=K&&K.indexOf("edge/")>0,X=(K&&K.indexOf("android"),K&&/iphone|ipad|ipod|ios/.test(K)||"ios"=
==H),Q=(K&&/chrome\/\d+/.test(K),K&&/phantomjs/.test(K),K&&K.match(/firefox\/(\d+)/)),ee={}.watch,te=!1;if(W
)try{var ne={};Object.defineProperty(ne,"passive",{get:function(){te=!0}}),window.addEventListener("test-pas
sive",null,ne)}catch(e){}var re=function(){return void 0===z&&(z=!W&&!J&&void 0!==t&&t.process&&"server"===t
.process.env.VUE_ENV),z},oe=W&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__;function ie(e){return"function"==typeof e
&&/native code/.test(e.toString())}var ae,se="undefined"!=typeof Symbol&&ie(Symbol)&&"undefined"!=typeof Ref
lect&&ie(Reflect.ownKeys);ae="undefined"!=typeof Set&&ie(Set)?Set:function(){function e(){this.set=Object.cr
eate(null)}return e.prototype.has=function(e){return!0===this.set[e]},e.prototype.add=function(e){this.set[e
]=!0},e.prototype.clear=function(){this.set=Object.create(null)},e}();var le=P,ce=0,de=function(){this.id=ce
++,this.subs=[]};de.prototype.addSub=function(e){this.subs.push(e)},de.prototype.removeSub=function(e){g(thi
s.subs,e)},de.prototype.depend=function(){de.target&&de.target.addDep(this)},de.prototype.notify=function(){
for(var e=this.subs.slice(),t=0,n=e.length;t<n;t++)e[t].update()},de.target=null;var ue=[];function pe(e){ue
.push(e),de.target=e}function fe(){ue.pop(),de.target=ue[ue.length-1]}var ve=function(e,t,n,r,o,i,a,s){this.
tag=e,this.data=t,this.children=n,this.text=r,this.elm=o,this.ns=void 0,this.context=i,this.fnContext=void 0
,this.fnOptions=void 0,this.fnScopeId=void 0,this.key=t&&t.key,this.componentOptions=a,this.componentInstanc
e=void 0,this.parent=void 0,this.raw=!1,this.isStatic=!1,this.isRootInsert=!0,this.isComment=!1,this.isClone
d=!1,this.isOnce=!1,this.asyncFactory=s,this.asyncMeta=void 0,this.isAsyncPlaceholder=!1},me={child:{configu
rable:!0}};me.child.get=function(){return this.componentInstance},Object.defineProperties(ve.prototype,me);v
ar he=function(e){void 0===e&&(e="");var t=new ve;return t.text=e,t.isComment=!0,t};function ge(e){return ne
w ve(void 0,void 0,void 0,String(e))}function ye(e){var t=new ve(e.tag,e.data,e.children&&e.children.slice()
,e.text,e.elm,e.context,e.componentOptions,e.asyncFactory);return t.ns=e.ns,t.isStatic=e.isStatic,t.key=e.ke
y,t.isComment=e.isComment,t.fnContext=e.fnContext,t.fnOptions=e.fnOptions,t.fnScopeId=e.fnScopeId,t.asyncMet
a=e.asyncMeta,t.isCloned=!0,t}var be=Array.prototype,_e=Object.create(be);["push","pop","shift","unshift","s
plice","sort","reverse"].forEach((function(e){var t=be[e];V(_e,e,(function(){for(var n=[],r=arguments.length
;r--;)n[r]=arguments[r];var o,i=t.apply(this,n),a=this.__ob__;switch(e){case"push":case"unshift":o=n;break;c
ase"splice":o=n.slice(2)}return o&&a.observeArray(o),a.dep.notify(),i}))}));var we=Object.getOwnPropertyName
s(_e),xe=!0;function Ce(e){xe=e}var ke=function(e){var t;this.value=e,this.dep=new de,this.vmCount=0,V(e,"__
ob__",this),Array.isArray(e)?(q?(t=_e,e.__proto__=t):function(e,t,n){for(var r=0,o=n.length;r<o;r++){var i=n
[r];V(e,i,t[i])}}(e,_e,we),this.observeArray(e)):this.walk(e)};function je(e,t){var n;if(s(e)&&!(e instanceo
f ve))return b(e,"__ob__")&&e.__ob__ instanceof ke?n=e.__ob__:xe&&!re()&&(Array.isArray(e)||c(e))&&Object.is
Extensible(e)&&!e._isVue&&(n=new ke(e)),t&&n&&n.vmCount++,n}function Se(e,t,n,r,o){var i=new de,a=Object.get
OwnPropertyDescriptor(e,t);if(!a||!1!==a.configurable){var s=a&&a.get,l=a&&a.set;s&&!l||2!==arguments.length
||(n=e[t]);var c=!o&&je(n);Object.defineProperty(e,t,{enumerable:!0,configurable:!0,get:function(){var t=s?s
.call(e):n;return de.target&&(i.depend(),c&&(c.dep.depend(),Array.isArray(t)&&function e(t){for(var n=void 0
,r=0,o=t.length;r<o;r++)(n=t[r])&&n.__ob__&&n.__ob__.dep.depend(),Array.isArray(n)&&e(n)}(t))),t},set:functi
on(t){var r=s?s.call(e):n;t===r||t!=t&&r!=r||s&&!l||(l?l.call(e,t):n=t,c=!o&&je(t),i.notify())}})}}function
$e(e,t,n){if(Array.isArray(e)&&d(t))return e.length=Math.max(e.length,t),e.splice(t,1,n),n;if(t in e&&!(t in
 Object.prototype))return e[t]=n,n;var r=e.__ob__;return e._isVue||r&&r.vmCount?n:r?(Se(r.value,t,n),r.dep.n
otify(),n):(e[t]=n,n)}function Ae(e,t){if(Array.isArray(e)&&d(t))e.splice(t,1);else{var n=e.__ob__;e._isVue|
|n&&n.vmCount||b(e,t)&&(delete e[t],n&&n.dep.notify())}}ke.prototype.walk=function(e){for(var t=Object.keys(
e),n=0;n<t.length;n++)Se(e,t[n])},ke.prototype.observeArray=function(e){for(var t=0,n=e.length;t<n;t++)je(e[
t])};var Me=E.optionMergeStrategies;function Pe(e,t){if(!t)return e;for(var n,r,o,i=se?Reflect.ownKeys(t):Ob
ject.keys(t),a=0;a<i.length;a++)"__ob__"!==(n=i[a])&&(r=e[n],o=t[n],b(e,n)?r!==o&&c(r)&&c(o)&&Pe(r,o):$e(e,n
,o));return e}function Te(e,t,n){return n?function(){var r="function"==typeof t?t.call(n,n):t,o="function"==
typeof e?e.call(n,n):e;return r?Pe(r,o):o}:t?e?function(){return Pe("function"==typeof t?t.call(this,this):t
,"function"==typeof e?e.call(this,this):e)}:t:e}function Oe(e,t){var n=t?e?e.concat(t):Array.isArray(t)?t:[t
]:e;return n?function(e){for(var t=[],n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}f
unction De(e,t,n,r){var o=Object.create(e||null);return t?A(o,t):o}Me.data=function(e,t,n){return n?Te(e,t,n
):t&&"function"!=typeof t?e:Te(e,t)},R.forEach((function(e){Me[e]=Oe})),L.forEach((function(e){Me[e+"s"]=De}
)),Me.watch=function(e,t,n,r){if(e===ee&&(e=void 0),t===ee&&(t=void 0),!t)return Object.create(e||null);if(!
e)return t;var o={};for(var i in A(o,e),t){var a=o[i],s=t[i];a&&!Array.isArray(a)&&(a=[a]),o[i]=a?a.concat(s
):Array.isArray(s)?s:[s]}return o},Me.props=Me.methods=Me.inject=Me.computed=function(e,t,n,r){if(!e)return
t;var o=Object.create(null);return A(o,e),t&&A(o,t),o},Me.provide=Te;var Fe=function(e,t){return void 0===t?
e:t};function Ne(e,t,n){if("function"==typeof t&&(t=t.options),function(e,t){var n=e.props;if(n){var r,o,i={
};if(Array.isArray(n))for(r=n.length;r--;)"string"==typeof(o=n[r])&&(i[x(o)]={type:null});else if(c(n))for(v
ar a in n)o=n[a],i[x(a)]=c(o)?o:{type:o};e.props=i}}(t),function(e,t){var n=e.inject;if(n){var r=e.inject={}
;if(Array.isArray(n))for(var o=0;o<n.length;o++)r[n[o]]={from:n[o]};else if(c(n))for(var i in n){var a=n[i];
r[i]=c(a)?A({from:i},a):{from:a}}}}(t),function(e){var t=e.directives;if(t)for(var n in t){var r=t[n];"funct
ion"==typeof r&&(t[n]={bind:r,update:r})}}(t),!t._base&&(t.extends&&(e=Ne(e,t.extends,n)),t.mixins))for(var
r=0,o=t.mixins.length;r<o;r++)e=Ne(e,t.mixins[r],n);var i,a={};for(i in e)s(i);for(i in t)b(e,i)||s(i);funct
ion s(r){var o=Me[r]||Fe;a[r]=o(e[r],t[r],n,r)}return a}function Ie(e,t,n,r){if("string"==typeof n){var o=e[
t];if(b(o,n))return o[n];var i=x(n);if(b(o,i))return o[i];var a=C(i);return b(o,a)?o[a]:o[n]||o[i]||o[a]}}fu
nction Le(e,t,n,r){var o=t[e],i=!b(n,e),a=n[e],s=Ve(Boolean,o.type);if(s>-1)if(i&&!b(o,"default"))a=!1;else
if(""===a||a===j(e)){var l=Ve(String,o.type);(l<0||s<l)&&(a=!0)}if(void 0===a){a=function(e,t,n){if(b(t,"def
ault")){var r=t.default;return e&&e.$options.propsData&&void 0===e.$options.propsData[n]&&void 0!==e._props[
n]?e._props[n]:"function"==typeof r&&"Function"!==Ee(t.type)?r.call(e):r}}(r,o,e);var c=xe;Ce(!0),je(a),Ce(c
)}return a}var Re=/^\s*function (\w+)/;function Ee(e){var t=e&&e.toString().match(Re);return t?t[1]:""}funct
ion Ue(e,t){return Ee(e)===Ee(t)}function Ve(e,t){if(!Array.isArray(t))return Ue(t,e)?0:-1;for(var n=0,r=t.l
ength;n<r;n++)if(Ue(t[n],e))return n;return-1}function ze(e,t,n){pe();try{if(t)for(var r=t;r=r.$parent;){var
 o=r.$options.errorCaptured;if(o)for(var i=0;i<o.length;i++)try{if(!1===o[i].call(r,e,t,n))return}catch(e){q
e(e,r,"errorCaptured hook")}}qe(e,t,n)}finally{fe()}}function Be(e,t,n,r,o){var i;try{(i=n?e.apply(t,n):e.ca
ll(t))&&!i._isVue&&u(i)&&!i._handled&&(i.catch((function(e){return ze(e,r,o+" (Promise/async)")})),i._handle
d=!0)}catch(e){ze(e,r,o)}return i}function qe(e,t,n){if(E.errorHandler)try{return E.errorHandler.call(null,e
,t,n)}catch(t){t!==e&&We(t)}We(e)}function We(e,t,n){if(!W&&!J||"undefined"==typeof console)throw e;console.
error(e)}var Je,He=!1,Ke=[],Ge=!1;function Ze(){Ge=!1;var e=Ke.slice(0);Ke.length=0;for(var t=0;t<e.length;t
++)e[t]()}if("undefined"!=typeof Promise&&ie(Promise)){var Ye=Promise.resolve();Je=function(){Ye.then(Ze),X&
&setTimeout(P)},He=!0}else if(G||"undefined"==typeof MutationObserver||!ie(MutationObserver)&&"[object Mutat
ionObserverConstructor]"!==MutationObserver.toString())Je=void 0!==n&&ie(n)?function(){n(Ze)}:function(){set
Timeout(Ze,0)};else{var Xe=1,Qe=new MutationObserver(Ze),et=document.createTextNode(String(Xe));Qe.observe(e
t,{characterData:!0}),Je=function(){Xe=(Xe+1)%2,et.data=String(Xe)},He=!0}function tt(e,t){var n;if(Ke.push(
(function(){if(e)try{e.call(t)}catch(e){ze(e,t,"nextTick")}else n&&n(t)})),Ge||(Ge=!0,Je()),!e&&"undefined"!
=typeof Promise)return new Promise((function(e){n=e}))}var nt=new ae;function rt(e){!function e(t,n){var r,o
,i=Array.isArray(t);if(!(!i&&!s(t)||Object.isFrozen(t)||t instanceof ve)){if(t.__ob__){var a=t.__ob__.dep.id
;if(n.has(a))return;n.add(a)}if(i)for(r=t.length;r--;)e(t[r],n);else for(r=(o=Object.keys(t)).length;r--;)e(
t[o[r]],n)}}(e,nt),nt.clear()}var ot=_((function(e){var t="&"===e.charAt(0),n="~"===(e=t?e.slice(1):e).charA
t(0),r="!"===(e=n?e.slice(1):e).charAt(0);return{name:e=r?e.slice(1):e,once:n,capture:r,passive:t}}));functi
on it(e,t){function n(){var e=arguments,r=n.fns;if(!Array.isArray(r))return Be(r,null,arguments,t,"v-on hand
ler");for(var o=r.slice(),i=0;i<o.length;i++)Be(o[i],null,e,t,"v-on handler")}return n.fns=e,n}function at(e
,t,n,o,a,s){var l,c,d,u;for(l in e)c=e[l],d=t[l],u=ot(l),r(c)||(r(d)?(r(c.fns)&&(c=e[l]=it(c,s)),i(u.once)&&
(c=e[l]=a(u.name,c,u.capture)),n(u.name,c,u.capture,u.passive,u.params)):c!==d&&(d.fns=c,e[l]=d));for(l in t
)r(e[l])&&o((u=ot(l)).name,t[l],u.capture)}function st(e,t,n){var a;e instanceof ve&&(e=e.data.hook||(e.data
.hook={}));var s=e[t];function l(){n.apply(this,arguments),g(a.fns,l)}r(s)?a=it([l]):o(s.fns)&&i(s.merged)?(
a=s).fns.push(l):a=it([s,l]),a.merged=!0,e[t]=a}function lt(e,t,n,r,i){if(o(t)){if(b(t,n))return e[n]=t[n],i
||delete t[n],!0;if(b(t,r))return e[n]=t[r],i||delete t[r],!0}return!1}function ct(e){return a(e)?[ge(e)]:Ar
ray.isArray(e)?function e(t,n){var s,l,c,d,u=[];for(s=0;s<t.length;s++)r(l=t[s])||"boolean"==typeof l||(d=u[
c=u.length-1],Array.isArray(l)?l.length>0&&(dt((l=e(l,(n||"")+"_"+s))[0])&&dt(d)&&(u[c]=ge(d.text+l[0].text)
,l.shift()),u.push.apply(u,l)):a(l)?dt(d)?u[c]=ge(d.text+l):""!==l&&u.push(ge(l)):dt(l)&&dt(d)?u[c]=ge(d.tex
t+l.text):(i(t._isVList)&&o(l.tag)&&r(l.key)&&o(n)&&(l.key="__vlist"+n+"_"+s+"__"),u.push(l)));return u}(e):
void 0}function dt(e){return o(e)&&o(e.text)&&!1===e.isComment}function ut(e,t){if(e){for(var n=Object.creat
e(null),r=se?Reflect.ownKeys(e):Object.keys(e),o=0;o<r.length;o++){var i=r[o];if("__ob__"!==i){for(var a=e[i
].from,s=t;s;){if(s._provided&&b(s._provided,a)){n[i]=s._provided[a];break}s=s.$parent}if(!s&&"default"in e[
i]){var l=e[i].default;n[i]="function"==typeof l?l.call(t):l}}}return n}}function pt(e,t){if(!e||!e.length)r
eturn{};for(var n={},r=0,o=e.length;r<o;r++){var i=e[r],a=i.data;if(a&&a.attrs&&a.attrs.slot&&delete a.attrs
.slot,i.context!==t&&i.fnContext!==t||!a||null==a.slot)(n.default||(n.default=[])).push(i);else{var s=a.slot
,l=n[s]||(n[s]=[]);"template"===i.tag?l.push.apply(l,i.children||[]):l.push(i)}}for(var c in n)n[c].every(ft
)&&delete n[c];return n}function ft(e){return e.isComment&&!e.asyncFactory||" "===e.text}function vt(e){retu
rn e.isComment&&e.asyncFactory}function mt(t,n,r){var o,i=Object.keys(n).length>0,a=t?!!t.$stable:!i,s=t&&t.
$key;if(t){if(t._normalized)return t._normalized;if(a&&r&&r!==e&&s===r.$key&&!i&&!r.$hasNormal)return r;for(
var l in o={},t)t[l]&&"$"!==l[0]&&(o[l]=ht(n,l,t[l]))}else o={};for(var c in n)c in o||(o[c]=gt(n,c));return
 t&&Object.isExtensible(t)&&(t._normalized=o),V(o,"$stable",a),V(o,"$key",s),V(o,"$hasNormal",i),o}function
ht(e,t,n){var r=function(){var e=arguments.length?n.apply(null,arguments):n({}),t=(e=e&&"object"==typeof e&&
!Array.isArray(e)?[e]:ct(e))&&e[0];return e&&(!t||1===e.length&&t.isComment&&!vt(t))?void 0:e};return n.prox
y&&Object.defineProperty(e,t,{get:r,enumerable:!0,configurable:!0}),r}function gt(e,t){return function(){ret
urn e[t]}}function yt(e,t){var n,r,i,a,l;if(Array.isArray(e)||"string"==typeof e)for(n=new Array(e.length),r
=0,i=e.length;r<i;r++)n[r]=t(e[r],r);else if("number"==typeof e)for(n=new Array(e),r=0;r<e;r++)n[r]=t(r+1,r)
;else if(s(e))if(se&&e[Symbol.iterator]){n=[];for(var c=e[Symbol.iterator](),d=c.next();!d.done;)n.push(t(d.
value,n.length)),d=c.next()}else for(a=Object.keys(e),n=new Array(a.length),r=0,i=a.length;r<i;r++)l=a[r],n[
r]=t(e[l],l,r);return o(n)||(n=[]),n._isVList=!0,n}function bt(e,t,n,r){var o,i=this.$scopedSlots[e];i?(n=n|
|{},r&&(n=A(A({},r),n)),o=i(n)||("function"==typeof t?t():t)):o=this.$slots[e]||("function"==typeof t?t():t)
;var a=n&&n.slot;return a?this.$createElement("template",{slot:a},o):o}function _t(e){return Ie(this.$option
s,"filters",e)||O}function wt(e,t){return Array.isArray(e)?-1===e.indexOf(t):e!==t}function xt(e,t,n,r,o){va
r i=E.keyCodes[t]||n;return o&&r&&!E.keyCodes[t]?wt(o,r):i?wt(i,e):r?j(r)!==t:void 0===e}function Ct(e,t,n,r
,o){if(n&&s(n)){var i;Array.isArray(n)&&(n=M(n));var a=function(a){if("class"===a||"style"===a||h(a))i=e;els
e{var s=e.attrs&&e.attrs.type;i=r||E.mustUseProp(t,s,a)?e.domProps||(e.domProps={}):e.attrs||(e.attrs={})}va
r l=x(a),c=j(a);l in i||c in i||(i[a]=n[a],o&&((e.on||(e.on={}))["update:"+a]=function(e){n[a]=e}))};for(var
 l in n)a(l)}return e}function kt(e,t){var n=this._staticTrees||(this._staticTrees=[]),r=n[e];return r&&!t||
St(r=n[e]=this.$options.staticRenderFns[e].call(this._renderProxy,null,this),"__static__"+e,!1),r}function j
t(e,t,n){return St(e,"__once__"+t+(n?"_"+n:""),!0),e}function St(e,t,n){if(Array.isArray(e))for(var r=0;r<e.
length;r++)e[r]&&"string"!=typeof e[r]&&$t(e[r],t+"_"+r,n);else $t(e,t,n)}function $t(e,t,n){e.isStatic=!0,e
.key=t,e.isOnce=n}function At(e,t){if(t&&c(t)){var n=e.on=e.on?A({},e.on):{};for(var r in t){var o=n[r],i=t[
r];n[r]=o?[].concat(o,i):i}}return e}function Mt(e,t,n,r){t=t||{$stable:!n};for(var o=0;o<e.length;o++){var
i=e[o];Array.isArray(i)?Mt(i,t,n):i&&(i.proxy&&(i.fn.proxy=!0),t[i.key]=i.fn)}return r&&(t.$key=r),t}functio
n Pt(e,t){for(var n=0;n<t.length;n+=2){var r=t[n];"string"==typeof r&&r&&(e[t[n]]=t[n+1])}return e}function
Tt(e,t){return"string"==typeof e?t+e:e}function Ot(e){e._o=jt,e._n=f,e._s=p,e._l=yt,e._t=bt,e._q=D,e._i=F,e.
_m=kt,e._f=_t,e._k=xt,e._b=Ct,e._v=ge,e._e=he,e._u=Mt,e._g=At,e._d=Pt,e._p=Tt}function Dt(t,n,r,o,a){var s,l
=this,c=a.options;b(o,"_uid")?(s=Object.create(o))._original=o:(s=o,o=o._original);var d=i(c._compiled),u=!d
;this.data=t,this.props=n,this.children=r,this.parent=o,this.listeners=t.on||e,this.injections=ut(c.inject,o
),this.slots=function(){return l.$slots||mt(t.scopedSlots,l.$slots=pt(r,o)),l.$slots},Object.defineProperty(
this,"scopedSlots",{enumerable:!0,get:function(){return mt(t.scopedSlots,this.slots())}}),d&&(this.$options=
c,this.$slots=this.slots(),this.$scopedSlots=mt(t.scopedSlots,this.$slots)),c._scopeId?this._c=function(e,t,
n,r){var i=Ut(s,e,t,n,r,u);return i&&!Array.isArray(i)&&(i.fnScopeId=c._scopeId,i.fnContext=o),i}:this._c=fu
nction(e,t,n,r){return Ut(s,e,t,n,r,u)}}function Ft(e,t,n,r,o){var i=ye(e);return i.fnContext=n,i.fnOptions=
r,t.slot&&((i.data||(i.data={})).slot=t.slot),i}function Nt(e,t){for(var n in t)e[x(n)]=t[n]}Ot(Dt.prototype
);var It={init:function(e,t){if(e.componentInstance&&!e.componentInstance._isDestroyed&&e.data.keepAlive){va
r n=e;It.prepatch(n,n)}else(e.componentInstance=function(e,t){var n={_isComponent:!0,_parentVnode:e,parent:t
},r=e.data.inlineTemplate;return o(r)&&(n.render=r.render,n.staticRenderFns=r.staticRenderFns),new e.compone
ntOptions.Ctor(n)}(e,Gt)).$mount(t?e.elm:void 0,t)},prepatch:function(t,n){var r=n.componentOptions;!functio
n(t,n,r,o,i){var a=o.data.scopedSlots,s=t.$scopedSlots,l=!!(a&&!a.$stable||s!==e&&!s.$stable||a&&t.$scopedSl
ots.$key!==a.$key||!a&&t.$scopedSlots.$key),c=!!(i||t.$options._renderChildren||l);if(t.$options._parentVnod
e=o,t.$vnode=o,t._vnode&&(t._vnode.parent=o),t.$options._renderChildren=i,t.$attrs=o.data.attrs||e,t.$listen
ers=r||e,n&&t.$options.props){Ce(!1);for(var d=t._props,u=t.$options._propKeys||[],p=0;p<u.length;p++){var f
=u[p],v=t.$options.props;d[f]=Le(f,v,n,t)}Ce(!0),t.$options.propsData=n}r=r||e;var m=t.$options._parentListe
ners;t.$options._parentListeners=r,Kt(t,r,m),c&&(t.$slots=pt(i,o.context),t.$forceUpdate())}(n.componentInst
ance=t.componentInstance,r.propsData,r.listeners,n,r.children)},insert:function(e){var t,n=e.context,r=e.com
ponentInstance;r._isMounted||(r._isMounted=!0,Qt(r,"mounted")),e.data.keepAlive&&(n._isMounted?((t=r)._inact
ive=!1,tn.push(t)):Xt(r,!0))},destroy:function(e){var t=e.componentInstance;t._isDestroyed||(e.data.keepAliv
e?function e(t,n){if(!(n&&(t._directInactive=!0,Yt(t))||t._inactive)){t._inactive=!0;for(var r=0;r<t.$childr
en.length;r++)e(t.$children[r]);Qt(t,"deactivated")}}(t,!0):t.$destroy())}},Lt=Object.keys(It);function Rt(t
,n,a,l,c){if(!r(t)){var d=a.$options._base;if(s(t)&&(t=d.extend(t)),"function"==typeof t){var p;if(r(t.cid)&
&void 0===(t=function(e,t){if(i(e.error)&&o(e.errorComp))return e.errorComp;if(o(e.resolved))return e.resolv
ed;var n=zt;if(n&&o(e.owners)&&-1===e.owners.indexOf(n)&&e.owners.push(n),i(e.loading)&&o(e.loadingComp))ret
urn e.loadingComp;if(n&&!o(e.owners)){var a=e.owners=[n],l=!0,c=null,d=null;n.$on("hook:destroyed",(function
(){return g(a,n)}));var p=function(e){for(var t=0,n=a.length;t<n;t++)a[t].$forceUpdate();e&&(a.length=0,null
!==c&&(clearTimeout(c),c=null),null!==d&&(clearTimeout(d),d=null))},f=N((function(n){e.resolved=Bt(n,t),l?a.
length=0:p(!0)})),v=N((function(t){o(e.errorComp)&&(e.error=!0,p(!0))})),m=e(f,v);return s(m)&&(u(m)?r(e.res
olved)&&m.then(f,v):u(m.component)&&(m.component.then(f,v),o(m.error)&&(e.errorComp=Bt(m.error,t)),o(m.loadi
ng)&&(e.loadingComp=Bt(m.loading,t),0===m.delay?e.loading=!0:c=setTimeout((function(){c=null,r(e.resolved)&&
r(e.error)&&(e.loading=!0,p(!1))}),m.delay||200)),o(m.timeout)&&(d=setTimeout((function(){d=null,r(e.resolve
d)&&v(null)}),m.timeout)))),l=!1,e.loading?e.loadingComp:e.resolved}}(p=t,d)))return function(e,t,n,r,o){var
 i=he();return i.asyncFactory=e,i.asyncMeta={data:t,context:n,children:r,tag:o},i}(p,n,a,l,c);n=n||{},wn(t),
o(n.model)&&function(e,t){var n=e.model&&e.model.prop||"value",r=e.model&&e.model.event||"input";(t.attrs||(
t.attrs={}))[n]=t.model.value;var i=t.on||(t.on={}),a=i[r],s=t.model.callback;o(a)?(Array.isArray(a)?-1===a.
indexOf(s):a!==s)&&(i[r]=[s].concat(a)):i[r]=s}(t.options,n);var f=function(e,t,n){var i=t.options.props;if(
!r(i)){var a={},s=e.attrs,l=e.props;if(o(s)||o(l))for(var c in i){var d=j(c);lt(a,l,c,d,!0)||lt(a,s,c,d,!1)}
return a}}(n,t);if(i(t.options.functional))return function(t,n,r,i,a){var s=t.options,l={},c=s.props;if(o(c)
)for(var d in c)l[d]=Le(d,c,n||e);else o(r.attrs)&&Nt(l,r.attrs),o(r.props)&&Nt(l,r.props);var u=new Dt(r,l,
a,i,t),p=s.render.call(null,u._c,u);if(p instanceof ve)return Ft(p,r,u.parent,s);if(Array.isArray(p)){for(va
r f=ct(p)||[],v=new Array(f.length),m=0;m<f.length;m++)v[m]=Ft(f[m],r,u.parent,s);return v}}(t,f,n,a,l);var
v=n.on;if(n.on=n.nativeOn,i(t.options.abstract)){var m=n.slot;n={},m&&(n.slot=m)}!function(e){for(var t=e.ho
ok||(e.hook={}),n=0;n<Lt.length;n++){var r=Lt[n],o=t[r],i=It[r];o===i||o&&o._merged||(t[r]=o?Et(i,o):i)}}(n)
;var h=t.options.name||c;return new ve("vue-component-"+t.cid+(h?"-"+h:""),n,void 0,void 0,void 0,a,{Ctor:t,
propsData:f,listeners:v,tag:c,children:l},p)}}}function Et(e,t){var n=function(n,r){e(n,r),t(n,r)};return n.
_merged=!0,n}function Ut(e,t,n,l,c,d){return(Array.isArray(n)||a(n))&&(c=l,l=n,n=void 0),i(d)&&(c=2),functio
n(e,t,n,a,l){return o(n)&&o(n.__ob__)?he():(o(n)&&o(n.is)&&(t=n.is),t?(Array.isArray(a)&&"function"==typeof
a[0]&&((n=n||{}).scopedSlots={default:a[0]},a.length=0),2===l?a=ct(a):1===l&&(a=function(e){for(var t=0;t<e.
length;t++)if(Array.isArray(e[t]))return Array.prototype.concat.apply([],e);return e}(a)),"string"==typeof t
?(d=e.$vnode&&e.$vnode.ns||E.getTagNamespace(t),c=E.isReservedTag(t)?new ve(E.parsePlatformTagName(t),n,a,vo
id 0,void 0,e):n&&n.pre||!o(u=Ie(e.$options,"components",t))?new ve(t,n,a,void 0,void 0,e):Rt(u,n,e,a,t)):c=
Rt(t,n,e,a),Array.isArray(c)?c:o(c)?(o(d)&&function e(t,n,a){if(t.ns=n,"foreignObject"===t.tag&&(n=void 0,a=
!0),o(t.children))for(var s=0,l=t.children.length;s<l;s++){var c=t.children[s];o(c.tag)&&(r(c.ns)||i(a)&&"sv
g"!==c.tag)&&e(c,n,a)}}(c,d),o(n)&&function(e){s(e.style)&&rt(e.style),s(e.class)&&rt(e.class)}(n),c):he()):
he());var c,d,u}(e,t,n,l,c)}var Vt,zt=null;function Bt(e,t){return(e.__esModule||se&&"Module"===e[Symbol.toS
tringTag])&&(e=e.default),s(e)?t.extend(e):e}function qt(e){if(Array.isArray(e))for(var t=0;t<e.length;t++){
var n=e[t];if(o(n)&&(o(n.componentOptions)||vt(n)))return n}}function Wt(e,t){Vt.$on(e,t)}function Jt(e,t){V
t.$off(e,t)}function Ht(e,t){var n=Vt;return function r(){null!==t.apply(null,arguments)&&n.$off(e,r)}}funct
ion Kt(e,t,n){Vt=e,at(t,n||{},Wt,Jt,Ht,e),Vt=void 0}var Gt=null;function Zt(e){var t=Gt;return Gt=e,function
(){Gt=t}}function Yt(e){for(;e&&(e=e.$parent);)if(e._inactive)return!0;return!1}function Xt(e,t){if(t){if(e.
_directInactive=!1,Yt(e))return}else if(e._directInactive)return;if(e._inactive||null===e._inactive){e._inac
tive=!1;for(var n=0;n<e.$children.length;n++)Xt(e.$children[n]);Qt(e,"activated")}}function Qt(e,t){pe();var
 n=e.$options[t],r=t+" hook";if(n)for(var o=0,i=n.length;o<i;o++)Be(n[o],e,null,e,r);e._hasHookEvent&&e.$emi
t("hook:"+t),fe()}var en=[],tn=[],nn={},rn=!1,on=!1,an=0,sn=0,ln=Date.now;if(W&&!G){var cn=window.performanc
e;cn&&"function"==typeof cn.now&&ln()>document.createEvent("Event").timeStamp&&(ln=function(){return cn.now(
)})}function dn(){var e,t;for(sn=ln(),on=!0,en.sort((function(e,t){return e.id-t.id})),an=0;an<en.length;an+
+)(e=en[an]).before&&e.before(),t=e.id,nn[t]=null,e.run();var n=tn.slice(),r=en.slice();an=en.length=tn.leng
th=0,nn={},rn=on=!1,function(e){for(var t=0;t<e.length;t++)e[t]._inactive=!0,Xt(e[t],!0)}(n),function(e){for
(var t=e.length;t--;){var n=e[t],r=n.vm;r._watcher===n&&r._isMounted&&!r._isDestroyed&&Qt(r,"updated")}}(r),
oe&&E.devtools&&oe.emit("flush")}var un=0,pn=function(e,t,n,r,o){this.vm=e,o&&(e._watcher=this),e._watchers.
push(this),r?(this.deep=!!r.deep,this.user=!!r.user,this.lazy=!!r.lazy,this.sync=!!r.sync,this.before=r.befo
re):this.deep=this.user=this.lazy=this.sync=!1,this.cb=n,this.id=++un,this.active=!0,this.dirty=this.lazy,th
is.deps=[],this.newDeps=[],this.depIds=new ae,this.newDepIds=new ae,this.expression="","function"==typeof t?
this.getter=t:(this.getter=function(e){if(!B.test(e)){var t=e.split(".");return function(e){for(var n=0;n<t.
length;n++){if(!e)return;e=e[t[n]]}return e}}}(t),this.getter||(this.getter=P)),this.value=this.lazy?void 0:
this.get()};pn.prototype.get=function(){var e;pe(this);var t=this.vm;try{e=this.getter.call(t,t)}catch(e){if
(!this.user)throw e;ze(e,t,'getter for watcher "'+this.expression+'"')}finally{this.deep&&rt(e),fe(),this.cl
eanupDeps()}return e},pn.prototype.addDep=function(e){var t=e.id;this.newDepIds.has(t)||(this.newDepIds.add(
t),this.newDeps.push(e),this.depIds.has(t)||e.addSub(this))},pn.prototype.cleanupDeps=function(){for(var e=t
his.deps.length;e--;){var t=this.deps[e];this.newDepIds.has(t.id)||t.removeSub(this)}var n=this.depIds;this.
depIds=this.newDepIds,this.newDepIds=n,this.newDepIds.clear(),n=this.deps,this.deps=this.newDeps,this.newDep
s=n,this.newDeps.length=0},pn.prototype.update=function(){this.lazy?this.dirty=!0:this.sync?this.run():funct
ion(e){var t=e.id;if(null==nn[t]){if(nn[t]=!0,on){for(var n=en.length-1;n>an&&en[n].id>e.id;)n--;en.splice(n
+1,0,e)}else en.push(e);rn||(rn=!0,tt(dn))}}(this)},pn.prototype.run=function(){if(this.active){var e=this.g
et();if(e!==this.value||s(e)||this.deep){var t=this.value;if(this.value=e,this.user){var n='callback for wat
cher "'+this.expression+'"';Be(this.cb,this.vm,[e,t],this.vm,n)}else this.cb.call(this.vm,e,t)}}},pn.prototy
pe.evaluate=function(){this.value=this.get(),this.dirty=!1},pn.prototype.depend=function(){for(var e=this.de
ps.length;e--;)this.deps[e].depend()},pn.prototype.teardown=function(){if(this.active){this.vm._isBeingDestr
oyed||g(this.vm._watchers,this);for(var e=this.deps.length;e--;)this.deps[e].removeSub(this);this.active=!1}
};var fn={enumerable:!0,configurable:!0,get:P,set:P};function vn(e,t,n){fn.get=function(){return this[t][n]}
,fn.set=function(e){this[t][n]=e},Object.defineProperty(e,n,fn)}var mn={lazy:!0};function hn(e,t,n){var r=!r
e();"function"==typeof n?(fn.get=r?gn(t):yn(n),fn.set=P):(fn.get=n.get?r&&!1!==n.cache?gn(t):yn(n.get):P,fn.
set=n.set||P),Object.defineProperty(e,t,fn)}function gn(e){return function(){var t=this._computedWatchers&&t
his._computedWatchers[e];if(t)return t.dirty&&t.evaluate(),de.target&&t.depend(),t.value}}function yn(e){ret
urn function(){return e.call(this,this)}}function bn(e,t,n,r){return c(n)&&(r=n,n=n.handler),"string"==typeo
f n&&(n=e[n]),e.$watch(t,n,r)}var _n=0;function wn(e){var t=e.options;if(e.super){var n=wn(e.super);if(n!==e
.superOptions){e.superOptions=n;var r=function(e){var t,n=e.options,r=e.sealedOptions;for(var o in n)n[o]!==
r[o]&&(t||(t={}),t[o]=n[o]);return t}(e);r&&A(e.extendOptions,r),(t=e.options=Ne(n,e.extendOptions)).name&&(
t.components[t.name]=e)}}return t}function xn(e){this._init(e)}function Cn(e){return e&&(e.Ctor.options.name
||e.tag)}function kn(e,t){return Array.isArray(e)?e.indexOf(t)>-1:"string"==typeof e?e.split(",").indexOf(t)
>-1:(n=e,"[object RegExp]"===l.call(n)&&e.test(t));var n}function jn(e,t){var n=e.cache,r=e.keys,o=e._vnode;
for(var i in n){var a=n[i];if(a){var s=a.name;s&&!t(s)&&Sn(n,i,r,o)}}}function Sn(e,t,n,r){var o=e[t];!o||r&
&o.tag===r.tag||o.componentInstance.$destroy(),e[t]=null,g(n,t)}!function(t){t.prototype._init=function(t){v
ar n=this;n._uid=_n++,n._isVue=!0,t&&t._isComponent?function(e,t){var n=e.$options=Object.create(e.construct
or.options),r=t._parentVnode;n.parent=t.parent,n._parentVnode=r;var o=r.componentOptions;n.propsData=o.props
Data,n._parentListeners=o.listeners,n._renderChildren=o.children,n._componentTag=o.tag,t.render&&(n.render=t
.render,n.staticRenderFns=t.staticRenderFns)}(n,t):n.$options=Ne(wn(n.constructor),t||{},n),n._renderProxy=n
,n._self=n,function(e){var t=e.$options,n=t.parent;if(n&&!t.abstract){for(;n.$options.abstract&&n.$parent;)n
=n.$parent;n.$children.push(e)}e.$parent=n,e.$root=n?n.$root:e,e.$children=[],e.$refs={},e._watcher=null,e._
inactive=null,e._directInactive=!1,e._isMounted=!1,e._isDestroyed=!1,e._isBeingDestroyed=!1}(n),function(e){
e._events=Object.create(null),e._hasHookEvent=!1;var t=e.$options._parentListeners;t&&Kt(e,t)}(n),function(t
){t._vnode=null,t._staticTrees=null;var n=t.$options,r=t.$vnode=n._parentVnode,o=r&&r.context;t.$slots=pt(n.
_renderChildren,o),t.$scopedSlots=e,t._c=function(e,n,r,o){return Ut(t,e,n,r,o,!1)},t.$createElement=functio
n(e,n,r,o){return Ut(t,e,n,r,o,!0)};var i=r&&r.data;Se(t,"$attrs",i&&i.attrs||e,null,!0),Se(t,"$listeners",n
._parentListeners||e,null,!0)}(n),Qt(n,"beforeCreate"),function(e){var t=ut(e.$options.inject,e);t&&(Ce(!1),
Object.keys(t).forEach((function(n){Se(e,n,t[n])})),Ce(!0))}(n),function(e){e._watchers=[];var t=e.$options;
t.props&&function(e,t){var n=e.$options.propsData||{},r=e._props={},o=e.$options._propKeys=[];e.$parent&&Ce(
!1);var i=function(i){o.push(i);var a=Le(i,t,n,e);Se(r,i,a),i in e||vn(e,"_props",i)};for(var a in t)i(a);Ce
(!0)}(e,t.props),t.methods&&function(e,t){for(var n in e.$options.props,t)e[n]="function"!=typeof t[n]?P:S(t
[n],e)}(e,t.methods),t.data?function(e){var t=e.$options.data;c(t=e._data="function"==typeof t?function(e,t)
{pe();try{return e.call(t,t)}catch(e){return ze(e,t,"data()"),{}}finally{fe()}}(t,e):t||{})||(t={});for(var
n,r=Object.keys(t),o=e.$options.props,i=(e.$options.methods,r.length);i--;){var a=r[i];o&&b(o,a)||36!==(n=(a
+"").charCodeAt(0))&&95!==n&&vn(e,"_data",a)}je(t,!0)}(e):je(e._data={},!0),t.computed&&function(e,t){var n=
e._computedWatchers=Object.create(null),r=re();for(var o in t){var i=t[o],a="function"==typeof i?i:i.get;r||
(n[o]=new pn(e,a||P,P,mn)),o in e||hn(e,o,i)}}(e,t.computed),t.watch&&t.watch!==ee&&function(e,t){for(var n
in t){var r=t[n];if(Array.isArray(r))for(var o=0;o<r.length;o++)bn(e,n,r[o]);else bn(e,n,r)}}(e,t.watch)}(n)
,function(e){var t=e.$options.provide;t&&(e._provided="function"==typeof t?t.call(e):t)}(n),Qt(n,"created"),
n.$options.el&&n.$mount(n.$options.el)}}(xn),function(e){Object.defineProperty(e.prototype,"$data",{get:func
tion(){return this._data}}),Object.defineProperty(e.prototype,"$props",{get:function(){return this._props}})
,e.prototype.$set=$e,e.prototype.$delete=Ae,e.prototype.$watch=function(e,t,n){if(c(t))return bn(this,e,t,n)
;(n=n||{}).user=!0;var r=new pn(this,e,t,n);if(n.immediate){var o='callback for immediate watcher "'+r.expre
ssion+'"';pe(),Be(t,this,[r.value],this,o),fe()}return function(){r.teardown()}}}(xn),function(e){var t=/^ho
ok:/;e.prototype.$on=function(e,n){var r=this;if(Array.isArray(e))for(var o=0,i=e.length;o<i;o++)r.$on(e[o],
n);else(r._events[e]||(r._events[e]=[])).push(n),t.test(e)&&(r._hasHookEvent=!0);return r},e.prototype.$once
=function(e,t){var n=this;function r(){n.$off(e,r),t.apply(n,arguments)}return r.fn=t,n.$on(e,r),n},e.protot
ype.$off=function(e,t){var n=this;if(!arguments.length)return n._events=Object.create(null),n;if(Array.isArr
ay(e)){for(var r=0,o=e.length;r<o;r++)n.$off(e[r],t);return n}var i,a=n._events[e];if(!a)return n;if(!t)retu
rn n._events[e]=null,n;for(var s=a.length;s--;)if((i=a[s])===t||i.fn===t){a.splice(s,1);break}return n},e.pr
ototype.$emit=function(e){var t=this._events[e];if(t){t=t.length>1?$(t):t;for(var n=$(arguments,1),r='event
handler for "'+e+'"',o=0,i=t.length;o<i;o++)Be(t[o],this,n,this,r)}return this}}(xn),function(e){e.prototype
._update=function(e,t){var n=this,r=n.$el,o=n._vnode,i=Zt(n);n._vnode=e,n.$el=o?n.__patch__(o,e):n.__patch__
(n.$el,e,t,!1),i(),r&&(r.__vue__=null),n.$el&&(n.$el.__vue__=n),n.$vnode&&n.$parent&&n.$vnode===n.$parent._v
node&&(n.$parent.$el=n.$el)},e.prototype.$forceUpdate=function(){this._watcher&&this._watcher.update()},e.pr
ototype.$destroy=function(){var e=this;if(!e._isBeingDestroyed){Qt(e,"beforeDestroy"),e._isBeingDestroyed=!0
;var t=e.$parent;!t||t._isBeingDestroyed||e.$options.abstract||g(t.$children,e),e._watcher&&e._watcher.teard
own();for(var n=e._watchers.length;n--;)e._watchers[n].teardown();e._data.__ob__&&e._data.__ob__.vmCount--,e
._isDestroyed=!0,e.__patch__(e._vnode,null),Qt(e,"destroyed"),e.$off(),e.$el&&(e.$el.__vue__=null),e.$vnode&
&(e.$vnode.parent=null)}}}(xn),function(e){Ot(e.prototype),e.prototype.$nextTick=function(e){return tt(e,thi
s)},e.prototype._render=function(){var e,t=this,n=t.$options,r=n.render,o=n._parentVnode;o&&(t.$scopedSlots=
mt(o.data.scopedSlots,t.$slots,t.$scopedSlots)),t.$vnode=o;try{zt=t,e=r.call(t._renderProxy,t.$createElement
)}catch(n){ze(n,t,"render"),e=t._vnode}finally{zt=null}return Array.isArray(e)&&1===e.length&&(e=e[0]),e ins
tanceof ve||(e=he()),e.parent=o,e}}(xn);var $n=[String,RegExp,Array],An={KeepAlive:{name:"keep-alive",abstra
ct:!0,props:{include:$n,exclude:$n,max:[String,Number]},methods:{cacheVNode:function(){var e=this.cache,t=th
is.keys,n=this.vnodeToCache,r=this.keyToCache;if(n){var o=n.tag,i=n.componentInstance,a=n.componentOptions;e
[r]={name:Cn(a),tag:o,componentInstance:i},t.push(r),this.max&&t.length>parseInt(this.max)&&Sn(e,t[0],t,this
._vnode),this.vnodeToCache=null}}},created:function(){this.cache=Object.create(null),this.keys=[]},destroyed
:function(){for(var e in this.cache)Sn(this.cache,e,this.keys)},mounted:function(){var e=this;this.cacheVNod
e(),this.$watch("include",(function(t){jn(e,(function(e){return kn(t,e)}))})),this.$watch("exclude",(functio
n(t){jn(e,(function(e){return!kn(t,e)}))}))},updated:function(){this.cacheVNode()},render:function(){var e=t
his.$slots.default,t=qt(e),n=t&&t.componentOptions;if(n){var r=Cn(n),o=this.include,i=this.exclude;if(o&&(!r
||!kn(o,r))||i&&r&&kn(i,r))return t;var a=this.cache,s=this.keys,l=null==t.key?n.Ctor.cid+(n.tag?"::"+n.tag:
""):t.key;a[l]?(t.componentInstance=a[l].componentInstance,g(s,l),s.push(l)):(this.vnodeToCache=t,this.keyTo
Cache=l),t.data.keepAlive=!0}return t||e&&e[0]}}};!function(e){var t={get:function(){return E}};Object.defin
eProperty(e,"config",t),e.util={warn:le,extend:A,mergeOptions:Ne,defineReactive:Se},e.set=$e,e.delete=Ae,e.n
extTick=tt,e.observable=function(e){return je(e),e},e.options=Object.create(null),L.forEach((function(t){e.o
ptions[t+"s"]=Object.create(null)})),e.options._base=e,A(e.options.components,An),function(e){e.use=function
(e){var t=this._installedPlugins||(this._installedPlugins=[]);if(t.indexOf(e)>-1)return this;var n=$(argumen
ts,1);return n.unshift(this),"function"==typeof e.install?e.install.apply(e,n):"function"==typeof e&&e.apply
(null,n),t.push(e),this}}(e),function(e){e.mixin=function(e){return this.options=Ne(this.options,e),this}}(e
),function(e){e.cid=0;var t=1;e.extend=function(e){e=e||{};var n=this,r=n.cid,o=e._Ctor||(e._Ctor={});if(o[r
])return o[r];var i=e.name||n.options.name,a=function(e){this._init(e)};return(a.prototype=Object.create(n.p
rototype)).constructor=a,a.cid=t++,a.options=Ne(n.options,e),a.super=n,a.options.props&&function(e){var t=e.
options.props;for(var n in t)vn(e.prototype,"_props",n)}(a),a.options.computed&&function(e){var t=e.options.
computed;for(var n in t)hn(e.prototype,n,t[n])}(a),a.extend=n.extend,a.mixin=n.mixin,a.use=n.use,L.forEach((
function(e){a[e]=n[e]})),i&&(a.options.components[i]=a),a.superOptions=n.options,a.extendOptions=e,a.sealedO
ptions=A({},a.options),o[r]=a,a}}(e),function(e){L.forEach((function(t){e[t]=function(e,n){return n?("compon
ent"===t&&c(n)&&(n.name=n.name||e,n=this.options._base.extend(n)),"directive"===t&&"function"==typeof n&&(n=
{bind:n,update:n}),this.options[t+"s"][e]=n,n):this.options[t+"s"][e]}}))}(e)}(xn),Object.defineProperty(xn.
prototype,"$isServer",{get:re}),Object.defineProperty(xn.prototype,"$ssrContext",{get:function(){return this
.$vnode&&this.$vnode.ssrContext}}),Object.defineProperty(xn,"FunctionalRenderContext",{value:Dt}),xn.version
="2.6.14";var Mn=v("style,class"),Pn=v("input,textarea,option,select,progress"),Tn=function(e,t,n){return"va
lue"===n&&Pn(e)&&"button"!==t||"selected"===n&&"option"===e||"checked"===n&&"input"===e||"muted"===n&&"video
"===e},On=v("contenteditable,draggable,spellcheck"),Dn=v("events,caret,typing,plaintext-only"),Fn=v("allowfu
llscreen,async,autofocus,autoplay,checked,compact,controls,declare,default,defaultchecked,defaultmuted,defau
ltselected,defer,disabled,enabled,formnovalidate,hidden,indeterminate,inert,ismap,itemscope,loop,multiple,mu
ted,nohref,noresize,noshade,novalidate,nowrap,open,pauseonexit,readonly,required,reversed,scoped,seamless,se
lected,sortable,truespeed,typemustmatch,visible"),Nn="http://www.w3.org/1999/xlink",In=function(e){return":"
===e.charAt(5)&&"xlink"===e.slice(0,5)},Ln=function(e){return In(e)?e.slice(6,e.length):""},Rn=function(e){r
eturn null==e||!1===e};function En(e,t){return{staticClass:Un(e.staticClass,t.staticClass),class:o(e.class)?
[e.class,t.class]:t.class}}function Un(e,t){return e?t?e+" "+t:e:t||""}function Vn(e){return Array.isArray(e
)?function(e){for(var t,n="",r=0,i=e.length;r<i;r++)o(t=Vn(e[r]))&&""!==t&&(n&&(n+=" "),n+=t);return n}(e):s
(e)?function(e){var t="";for(var n in e)e[n]&&(t&&(t+=" "),t+=n);return t}(e):"string"==typeof e?e:""}var zn
={svg:"http://www.w3.org/2000/svg",math:"http://www.w3.org/1998/Math/MathML"},Bn=v("html,body,base,head,link
,meta,style,title,address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,hgroup,nav,section,div,dd,dl,dt,figc
aption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,r
t,rtc,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,sou
rce,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,
form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,menuitem,s
ummary,content,element,shadow,template,blockquote,iframe,tfoot"),qn=v("svg,animate,circle,clippath,cursor,de
fs,desc,ellipse,filter,font-face,foreignobject,g,glyph,image,line,marker,mask,missing-glyph,path,pattern,pol
ygon,polyline,rect,switch,symbol,text,textpath,tspan,use,view",!0),Wn=function(e){return Bn(e)||qn(e)};funct
ion Jn(e){return qn(e)?"svg":"math"===e?"math":void 0}var Hn=Object.create(null),Kn=v("text,number,password,
search,email,tel,url");function Gn(e){return"string"==typeof e?document.querySelector(e)||document.createEle
ment("div"):e}var Zn=Object.freeze({createElement:function(e,t){var n=document.createElement(e);return"selec
t"!==e||t.data&&t.data.attrs&&void 0!==t.data.attrs.multiple&&n.setAttribute("multiple","multiple"),n},creat
eElementNS:function(e,t){return document.createElementNS(zn[e],t)},createTextNode:function(e){return documen
t.createTextNode(e)},createComment:function(e){return document.createComment(e)},insertBefore:function(e,t,n
){e.insertBefore(t,n)},removeChild:function(e,t){e.removeChild(t)},appendChild:function(e,t){e.appendChild(t
)},parentNode:function(e){return e.parentNode},nextSibling:function(e){return e.nextSibling},tagName:functio
n(e){return e.tagName},setTextContent:function(e,t){e.textContent=t},setStyleScope:function(e,t){e.setAttrib
ute(t,"")}}),Yn={create:function(e,t){Xn(t)},update:function(e,t){e.data.ref!==t.data.ref&&(Xn(e,!0),Xn(t))}
,destroy:function(e){Xn(e,!0)}};function Xn(e,t){var n=e.data.ref;if(o(n)){var r=e.context,i=e.componentInst
ance||e.elm,a=r.$refs;t?Array.isArray(a[n])?g(a[n],i):a[n]===i&&(a[n]=void 0):e.data.refInFor?Array.isArray(
a[n])?a[n].indexOf(i)<0&&a[n].push(i):a[n]=[i]:a[n]=i}}var Qn=new ve("",{},[]),er=["create","activate","upda
te","remove","destroy"];function tr(e,t){return e.key===t.key&&e.asyncFactory===t.asyncFactory&&(e.tag===t.t
ag&&e.isComment===t.isComment&&o(e.data)===o(t.data)&&function(e,t){if("input"!==e.tag)return!0;var n,r=o(n=
e.data)&&o(n=n.attrs)&&n.type,i=o(n=t.data)&&o(n=n.attrs)&&n.type;return r===i||Kn(r)&&Kn(i)}(e,t)||i(e.isAs
yncPlaceholder)&&r(t.asyncFactory.error))}function nr(e,t,n){var r,i,a={};for(r=t;r<=n;++r)o(i=e[r].key)&&(a
[i]=r);return a}var rr={create:or,update:or,destroy:function(e){or(e,Qn)}};function or(e,t){(e.data.directiv
es||t.data.directives)&&function(e,t){var n,r,o,i=e===Qn,a=t===Qn,s=ar(e.data.directives,e.context),l=ar(t.d
ata.directives,t.context),c=[],d=[];for(n in l)r=s[n],o=l[n],r?(o.oldValue=r.value,o.oldArg=r.arg,lr(o,"upda
te",t,e),o.def&&o.def.componentUpdated&&d.push(o)):(lr(o,"bind",t,e),o.def&&o.def.inserted&&c.push(o));if(c.
length){var u=function(){for(var n=0;n<c.length;n++)lr(c[n],"inserted",t,e)};i?st(t,"insert",u):u()}if(d.len
gth&&st(t,"postpatch",(function(){for(var n=0;n<d.length;n++)lr(d[n],"componentUpdated",t,e)})),!i)for(n in
s)l[n]||lr(s[n],"unbind",e,e,a)}(e,t)}var ir=Object.create(null);function ar(e,t){var n,r,o=Object.create(nu
ll);if(!e)return o;for(n=0;n<e.length;n++)(r=e[n]).modifiers||(r.modifiers=ir),o[sr(r)]=r,r.def=Ie(t.$option
s,"directives",r.name);return o}function sr(e){return e.rawName||e.name+"."+Object.keys(e.modifiers||{}).joi
n(".")}function lr(e,t,n,r,o){var i=e.def&&e.def[t];if(i)try{i(n.elm,e,n,r,o)}catch(r){ze(r,n.context,"direc
tive "+e.name+" "+t+" hook")}}var cr=[Yn,rr];function dr(e,t){var n=t.componentOptions;if(!(o(n)&&!1===n.Cto
r.options.inheritAttrs||r(e.data.attrs)&&r(t.data.attrs))){var i,a,s=t.elm,l=e.data.attrs||{},c=t.data.attrs
||{};for(i in o(c.__ob__)&&(c=t.data.attrs=A({},c)),c)a=c[i],l[i]!==a&&ur(s,i,a,t.data.pre);for(i in(G||Y)&&
c.value!==l.value&&ur(s,"value",c.value),l)r(c[i])&&(In(i)?s.removeAttributeNS(Nn,Ln(i)):On(i)||s.removeAttr
ibute(i))}}function ur(e,t,n,r){r||e.tagName.indexOf("-")>-1?pr(e,t,n):Fn(t)?Rn(n)?e.removeAttribute(t):(n="
allowfullscreen"===t&&"EMBED"===e.tagName?"true":t,e.setAttribute(t,n)):On(t)?e.setAttribute(t,function(e,t)
{return Rn(t)||"false"===t?"false":"contenteditable"===e&&Dn(t)?t:"true"}(t,n)):In(t)?Rn(n)?e.removeAttribut
eNS(Nn,Ln(t)):e.setAttributeNS(Nn,t,n):pr(e,t,n)}function pr(e,t,n){if(Rn(n))e.removeAttribute(t);else{if(G&
&!Z&&"TEXTAREA"===e.tagName&&"placeholder"===t&&""!==n&&!e.__ieph){var r=function(t){t.stopImmediatePropagat
ion(),e.removeEventListener("input",r)};e.addEventListener("input",r),e.__ieph=!0}e.setAttribute(t,n)}}var f
r={create:dr,update:dr};function vr(e,t){var n=t.elm,i=t.data,a=e.data;if(!(r(i.staticClass)&&r(i.class)&&(r
(a)||r(a.staticClass)&&r(a.class)))){var s=function(e){for(var t=e.data,n=e,r=e;o(r.componentInstance);)(r=r
.componentInstance._vnode)&&r.data&&(t=En(r.data,t));for(;o(n=n.parent);)n&&n.data&&(t=En(t,n.data));return
function(e,t){return o(e)||o(t)?Un(e,Vn(t)):""}(t.staticClass,t.class)}(t),l=n._transitionClasses;o(l)&&(s=U
n(s,Vn(l))),s!==n._prevClass&&(n.setAttribute("class",s),n._prevClass=s)}}var mr,hr,gr,yr,br,_r,wr={create:v
r,update:vr},xr=/[\w).+\-_$\]]/;function Cr(e){var t,n,r,o,i,a=!1,s=!1,l=!1,c=!1,d=0,u=0,p=0,f=0;for(r=0;r<e
.length;r++)if(n=t,t=e.charCodeAt(r),a)39===t&&92!==n&&(a=!1);else if(s)34===t&&92!==n&&(s=!1);else if(l)96=
==t&&92!==n&&(l=!1);else if(c)47===t&&92!==n&&(c=!1);else if(124!==t||124===e.charCodeAt(r+1)||124===e.charC
odeAt(r-1)||d||u||p){switch(t){case 34:s=!0;break;case 39:a=!0;break;case 96:l=!0;break;case 40:p++;break;ca
se 41:p--;break;case 91:u++;break;case 93:u--;break;case 123:d++;break;case 125:d--}if(47===t){for(var v=r-1
,m=void 0;v>=0&&" "===(m=e.charAt(v));v--);m&&xr.test(m)||(c=!0)}}else void 0===o?(f=r+1,o=e.slice(0,r).trim
()):h();function h(){(i||(i=[])).push(e.slice(f,r).trim()),f=r+1}if(void 0===o?o=e.slice(0,r).trim():0!==f&&
h(),i)for(r=0;r<i.length;r++)o=kr(o,i[r]);return o}function kr(e,t){var n=t.indexOf("(");if(n<0)return'_f("'
+t+'")('+e+")";var r=t.slice(0,n),o=t.slice(n+1);return'_f("'+r+'")('+e+(")"!==o?","+o:o)}function jr(e,t){c
onsole.error("[Vue compiler]: "+e)}function Sr(e,t){return e?e.map((function(e){return e[t]})).filter((funct
ion(e){return e})):[]}function $r(e,t,n,r,o){(e.props||(e.props=[])).push(Ir({name:t,value:n,dynamic:o},r)),
e.plain=!1}function Ar(e,t,n,r,o){(o?e.dynamicAttrs||(e.dynamicAttrs=[]):e.attrs||(e.attrs=[])).push(Ir({nam
e:t,value:n,dynamic:o},r)),e.plain=!1}function Mr(e,t,n,r){e.attrsMap[t]=n,e.attrsList.push(Ir({name:t,value
:n},r))}function Pr(e,t,n,r,o,i,a,s){(e.directives||(e.directives=[])).push(Ir({name:t,rawName:n,value:r,arg
:o,isDynamicArg:i,modifiers:a},s)),e.plain=!1}function Tr(e,t,n){return n?"_p("+t+',"'+e+'")':e+t}function O
r(t,n,r,o,i,a,s,l){var c;(o=o||e).right?l?n="("+n+")==='click'?'contextmenu':("+n+")":"click"===n&&(n="conte
xtmenu",delete o.right):o.middle&&(l?n="("+n+")==='click'?'mouseup':("+n+")":"click"===n&&(n="mouseup")),o.c
apture&&(delete o.capture,n=Tr("!",n,l)),o.once&&(delete o.once,n=Tr("~",n,l)),o.passive&&(delete o.passive,
n=Tr("&",n,l)),o.native?(delete o.native,c=t.nativeEvents||(t.nativeEvents={})):c=t.events||(t.events={});va
r d=Ir({value:r.trim(),dynamic:l},s);o!==e&&(d.modifiers=o);var u=c[n];Array.isArray(u)?i?u.unshift(d):u.pus
h(d):c[n]=u?i?[d,u]:[u,d]:d,t.plain=!1}function Dr(e,t,n){var r=Fr(e,":"+t)||Fr(e,"v-bind:"+t);if(null!=r)re
turn Cr(r);if(!1!==n){var o=Fr(e,t);if(null!=o)return JSON.stringify(o)}}function Fr(e,t,n){var r;if(null!=(
r=e.attrsMap[t]))for(var o=e.attrsList,i=0,a=o.length;i<a;i++)if(o[i].name===t){o.splice(i,1);break}return n
&&delete e.attrsMap[t],r}function Nr(e,t){for(var n=e.attrsList,r=0,o=n.length;r<o;r++){var i=n[r];if(t.test
(i.name))return n.splice(r,1),i}}function Ir(e,t){return t&&(null!=t.start&&(e.start=t.start),null!=t.end&&(
e.end=t.end)),e}function Lr(e,t,n){var r=n||{},o=r.number,i="$$v";r.trim&&(i="(typeof $$v === 'string'? $$v.
trim(): $$v)"),o&&(i="_n("+i+")");var a=Rr(t,i);e.model={value:"("+t+")",expression:JSON.stringify(t),callba
ck:"function ($$v) {"+a+"}"}}function Rr(e,t){var n=function(e){if(e=e.trim(),mr=e.length,e.indexOf("[")<0||
e.lastIndexOf("]")<mr-1)return(yr=e.lastIndexOf("."))>-1?{exp:e.slice(0,yr),key:'"'+e.slice(yr+1)+'"'}:{exp:
e,key:null};for(hr=e,yr=br=_r=0;!Ur();)Vr(gr=Er())?Br(gr):91===gr&&zr(gr);return{exp:e.slice(0,br),key:e.sli
ce(br+1,_r)}}(e);return null===n.key?e+"="+t:"$set("+n.exp+", "+n.key+", "+t+")"}function Er(){return hr.cha
rCodeAt(++yr)}function Ur(){return yr>=mr}function Vr(e){return 34===e||39===e}function zr(e){var t=1;for(br
=yr;!Ur();)if(Vr(e=Er()))Br(e);else if(91===e&&t++,93===e&&t--,0===t){_r=yr;break}}function Br(e){for(var t=
e;!Ur()&&(e=Er())!==t;);}var qr,Wr="__r";function Jr(e,t,n){var r=qr;return function o(){null!==t.apply(null
,arguments)&&Gr(e,o,n,r)}}var Hr=He&&!(Q&&Number(Q[1])<=53);function Kr(e,t,n,r){if(Hr){var o=sn,i=t;t=i._wr
apper=function(e){if(e.target===e.currentTarget||e.timeStamp>=o||e.timeStamp<=0||e.target.ownerDocument!==do
cument)return i.apply(this,arguments)}}qr.addEventListener(e,t,te?{capture:n,passive:r}:n)}function Gr(e,t,n
,r){(r||qr).removeEventListener(e,t._wrapper||t,n)}function Zr(e,t){if(!r(e.data.on)||!r(t.data.on)){var n=t
.data.on||{},i=e.data.on||{};qr=t.elm,function(e){if(o(e.__r)){var t=G?"change":"input";e[t]=[].concat(e.__r
,e[t]||[]),delete e.__r}o(e.__c)&&(e.change=[].concat(e.__c,e.change||[]),delete e.__c)}(n),at(n,i,Kr,Gr,Jr,
t.context),qr=void 0}}var Yr,Xr={create:Zr,update:Zr};function Qr(e,t){if(!r(e.data.domProps)||!r(t.data.dom
Props)){var n,i,a=t.elm,s=e.data.domProps||{},l=t.data.domProps||{};for(n in o(l.__ob__)&&(l=t.data.domProps
=A({},l)),s)n in l||(a[n]="");for(n in l){if(i=l[n],"textContent"===n||"innerHTML"===n){if(t.children&&(t.ch
ildren.length=0),i===s[n])continue;1===a.childNodes.length&&a.removeChild(a.childNodes[0])}if("value"===n&&"
PROGRESS"!==a.tagName){a._value=i;var c=r(i)?"":String(i);eo(a,c)&&(a.value=c)}else if("innerHTML"===n&&qn(a
.tagName)&&r(a.innerHTML)){(Yr=Yr||document.createElement("div")).innerHTML="<svg>"+i+"</svg>";for(var d=Yr.
firstChild;a.firstChild;)a.removeChild(a.firstChild);for(;d.firstChild;)a.appendChild(d.firstChild)}else if(
i!==s[n])try{a[n]=i}catch(e){}}}}function eo(e,t){return!e.composing&&("OPTION"===e.tagName||function(e,t){v
ar n=!0;try{n=document.activeElement!==e}catch(e){}return n&&e.value!==t}(e,t)||function(e,t){var n=e.value,
r=e._vModifiers;if(o(r)){if(r.number)return f(n)!==f(t);if(r.trim)return n.trim()!==t.trim()}return n!==t}(e
,t))}var to={create:Qr,update:Qr},no=_((function(e){var t={},n=/:(.+)/;return e.split(/;(?![^(]*\))/g).forEa
ch((function(e){if(e){var r=e.split(n);r.length>1&&(t[r[0].trim()]=r[1].trim())}})),t}));function ro(e){var
t=oo(e.style);return e.staticStyle?A(e.staticStyle,t):t}function oo(e){return Array.isArray(e)?M(e):"string"
==typeof e?no(e):e}var io,ao=/^--/,so=/\s*!important$/,lo=function(e,t,n){if(ao.test(t))e.style.setProperty(
t,n);else if(so.test(n))e.style.setProperty(j(t),n.replace(so,""),"important");else{var r=uo(t);if(Array.isA
rray(n))for(var o=0,i=n.length;o<i;o++)e.style[r]=n[o];else e.style[r]=n}},co=["Webkit","Moz","ms"],uo=_((fu
nction(e){if(io=io||document.createElement("div").style,"filter"!==(e=x(e))&&e in io)return e;for(var t=e.ch
arAt(0).toUpperCase()+e.slice(1),n=0;n<co.length;n++){var r=co[n]+t;if(r in io)return r}}));function po(e,t)
{var n=t.data,i=e.data;if(!(r(n.staticStyle)&&r(n.style)&&r(i.staticStyle)&&r(i.style))){var a,s,l=t.elm,c=i
.staticStyle,d=i.normalizedStyle||i.style||{},u=c||d,p=oo(t.data.style)||{};t.data.normalizedStyle=o(p.__ob_
_)?A({},p):p;var f=function(e,t){for(var n,r={},o=e;o.componentInstance;)(o=o.componentInstance._vnode)&&o.d
ata&&(n=ro(o.data))&&A(r,n);(n=ro(e.data))&&A(r,n);for(var i=e;i=i.parent;)i.data&&(n=ro(i.data))&&A(r,n);re
turn r}(t);for(s in u)r(f[s])&&lo(l,s,"");for(s in f)(a=f[s])!==u[s]&&lo(l,s,null==a?"":a)}}var fo={create:p
o,update:po},vo=/\s+/;function mo(e,t){if(t&&(t=t.trim()))if(e.classList)t.indexOf(" ")>-1?t.split(vo).forEa
ch((function(t){return e.classList.add(t)})):e.classList.add(t);else{var n=" "+(e.getAttribute("class")||"")
+" ";n.indexOf(" "+t+" ")<0&&e.setAttribute("class",(n+t).trim())}}function ho(e,t){if(t&&(t=t.trim()))if(e.
classList)t.indexOf(" ")>-1?t.split(vo).forEach((function(t){return e.classList.remove(t)})):e.classList.rem
ove(t),e.classList.length||e.removeAttribute("class");else{for(var n=" "+(e.getAttribute("class")||"")+" ",r
=" "+t+" ";n.indexOf(r)>=0;)n=n.replace(r," ");(n=n.trim())?e.setAttribute("class",n):e.removeAttribute("cla
ss")}}function go(e){if(e){if("object"==typeof e){var t={};return!1!==e.css&&A(t,yo(e.name||"v")),A(t,e),t}r
eturn"string"==typeof e?yo(e):void 0}}var yo=_((function(e){return{enterClass:e+"-enter",enterToClass:e+"-en
ter-to",enterActiveClass:e+"-enter-active",leaveClass:e+"-leave",leaveToClass:e+"-leave-to",leaveActiveClass
:e+"-leave-active"}})),bo=W&&!Z,_o="transition",wo="animation",xo="transition",Co="transitionend",ko="animat
ion",jo="animationend";bo&&(void 0===window.ontransitionend&&void 0!==window.onwebkittransitionend&&(xo="Web
kitTransition",Co="webkitTransitionEnd"),void 0===window.onanimationend&&void 0!==window.onwebkitanimationen
d&&(ko="WebkitAnimation",jo="webkitAnimationEnd"));var So=W?window.requestAnimationFrame?window.requestAnima
tionFrame.bind(window):setTimeout:function(e){return e()};function $o(e){So((function(){So(e)}))}function Ao
(e,t){var n=e._transitionClasses||(e._transitionClasses=[]);n.indexOf(t)<0&&(n.push(t),mo(e,t))}function Mo(
e,t){e._transitionClasses&&g(e._transitionClasses,t),ho(e,t)}function Po(e,t,n){var r=Oo(e,t),o=r.type,i=r.t
imeout,a=r.propCount;if(!o)return n();var s=o===_o?Co:jo,l=0,c=function(){e.removeEventListener(s,d),n()},d=
function(t){t.target===e&&++l>=a&&c()};setTimeout((function(){l<a&&c()}),i+1),e.addEventListener(s,d)}var To
=/\b(transform|all)(,|$)/;function Oo(e,t){var n,r=window.getComputedStyle(e),o=(r[xo+"Delay"]||"").split(",
 "),i=(r[xo+"Duration"]||"").split(", "),a=Do(o,i),s=(r[ko+"Delay"]||"").split(", "),l=(r[ko+"Duration"]||""
).split(", "),c=Do(s,l),d=0,u=0;return t===_o?a>0&&(n=_o,d=a,u=i.length):t===wo?c>0&&(n=wo,d=c,u=l.length):u
=(n=(d=Math.max(a,c))>0?a>c?_o:wo:null)?n===_o?i.length:l.length:0,{type:n,timeout:d,propCount:u,hasTransfor
m:n===_o&&To.test(r[xo+"Property"])}}function Do(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max.
apply(null,t.map((function(t,n){return Fo(t)+Fo(e[n])})))}function Fo(e){return 1e3*Number(e.slice(0,-1).rep
lace(",","."))}function No(e,t){var n=e.elm;o(n._leaveCb)&&(n._leaveCb.cancelled=!0,n._leaveCb());var i=go(e
.data.transition);if(!r(i)&&!o(n._enterCb)&&1===n.nodeType){for(var a=i.css,l=i.type,c=i.enterClass,d=i.ente
rToClass,u=i.enterActiveClass,p=i.appearClass,v=i.appearToClass,m=i.appearActiveClass,h=i.beforeEnter,g=i.en
ter,y=i.afterEnter,b=i.enterCancelled,_=i.beforeAppear,w=i.appear,x=i.afterAppear,C=i.appearCancelled,k=i.du
ration,j=Gt,S=Gt.$vnode;S&&S.parent;)j=S.context,S=S.parent;var $=!j._isMounted||!e.isRootInsert;if(!$||w||"
"===w){var A=$&&p?p:c,M=$&&m?m:u,P=$&&v?v:d,T=$&&_||h,O=$&&"function"==typeof w?w:g,D=$&&x||y,F=$&&C||b,I=f(
s(k)?k.enter:k),L=!1!==a&&!Z,R=Ro(O),E=n._enterCb=N((function(){L&&(Mo(n,P),Mo(n,M)),E.cancelled?(L&&Mo(n,A)
,F&&F(n)):D&&D(n),n._enterCb=null}));e.data.show||st(e,"insert",(function(){var t=n.parentNode,r=t&&t._pendi
ng&&t._pending[e.key];r&&r.tag===e.tag&&r.elm._leaveCb&&r.elm._leaveCb(),O&&O(n,E)})),T&&T(n),L&&(Ao(n,A),Ao
(n,M),$o((function(){Mo(n,A),E.cancelled||(Ao(n,P),R||(Lo(I)?setTimeout(E,I):Po(n,l,E)))}))),e.data.show&&(t
&&t(),O&&O(n,E)),L||R||E()}}}function Io(e,t){var n=e.elm;o(n._enterCb)&&(n._enterCb.cancelled=!0,n._enterCb
());var i=go(e.data.transition);if(r(i)||1!==n.nodeType)return t();if(!o(n._leaveCb)){var a=i.css,l=i.type,c
=i.leaveClass,d=i.leaveToClass,u=i.leaveActiveClass,p=i.beforeLeave,v=i.leave,m=i.afterLeave,h=i.leaveCancel
led,g=i.delayLeave,y=i.duration,b=!1!==a&&!Z,_=Ro(v),w=f(s(y)?y.leave:y),x=n._leaveCb=N((function(){n.parent
Node&&n.parentNode._pending&&(n.parentNode._pending[e.key]=null),b&&(Mo(n,d),Mo(n,u)),x.cancelled?(b&&Mo(n,c
),h&&h(n)):(t(),m&&m(n)),n._leaveCb=null}));g?g(C):C()}function C(){x.cancelled||(!e.data.show&&n.parentNode
&&((n.parentNode._pending||(n.parentNode._pending={}))[e.key]=e),p&&p(n),b&&(Ao(n,c),Ao(n,u),$o((function(){
Mo(n,c),x.cancelled||(Ao(n,d),_||(Lo(w)?setTimeout(x,w):Po(n,l,x)))}))),v&&v(n,x),b||_||x())}}function Lo(e)
{return"number"==typeof e&&!isNaN(e)}function Ro(e){if(r(e))return!1;var t=e.fns;return o(t)?Ro(Array.isArra
y(t)?t[0]:t):(e._length||e.length)>1}function Eo(e,t){!0!==t.data.show&&No(t)}var Uo=function(e){var t,n,s={
},l=e.modules,c=e.nodeOps;for(t=0;t<er.length;++t)for(s[er[t]]=[],n=0;n<l.length;++n)o(l[n][er[t]])&&s[er[t]
].push(l[n][er[t]]);function d(e){var t=c.parentNode(e);o(t)&&c.removeChild(t,e)}function u(e,t,n,r,a,l,d){i
f(o(e.elm)&&o(l)&&(e=l[d]=ye(e)),e.isRootInsert=!a,!function(e,t,n,r){var a=e.data;if(o(a)){var l=o(e.compon
entInstance)&&a.keepAlive;if(o(a=a.hook)&&o(a=a.init)&&a(e,!1),o(e.componentInstance))return p(e,t),f(n,e.el
m,r),i(l)&&function(e,t,n,r){for(var i,a=e;a.componentInstance;)if(o(i=(a=a.componentInstance._vnode).data)&
&o(i=i.transition)){for(i=0;i<s.activate.length;++i)s.activate[i](Qn,a);t.push(a);break}f(n,e.elm,r)}(e,t,n,
r),!0}}(e,t,n,r)){var u=e.data,v=e.children,h=e.tag;o(h)?(e.elm=e.ns?c.createElementNS(e.ns,h):c.createEleme
nt(h,e),y(e),m(e,v,t),o(u)&&g(e,t),f(n,e.elm,r)):i(e.isComment)?(e.elm=c.createComment(e.text),f(n,e.elm,r))
:(e.elm=c.createTextNode(e.text),f(n,e.elm,r))}}function p(e,t){o(e.data.pendingInsert)&&(t.push.apply(t,e.d
ata.pendingInsert),e.data.pendingInsert=null),e.elm=e.componentInstance.$el,h(e)?(g(e,t),y(e)):(Xn(e),t.push
(e))}function f(e,t,n){o(e)&&(o(n)?c.parentNode(n)===e&&c.insertBefore(e,t,n):c.appendChild(e,t))}function m
(e,t,n){if(Array.isArray(t))for(var r=0;r<t.length;++r)u(t[r],n,e.elm,null,!0,t,r);else a(e.text)&&c.appendC
hild(e.elm,c.createTextNode(String(e.text)))}function h(e){for(;e.componentInstance;)e=e.componentInstance._
vnode;return o(e.tag)}function g(e,n){for(var r=0;r<s.create.length;++r)s.create[r](Qn,e);o(t=e.data.hook)&&
(o(t.create)&&t.create(Qn,e),o(t.insert)&&n.push(e))}function y(e){var t;if(o(t=e.fnScopeId))c.setStyleScope
(e.elm,t);else for(var n=e;n;)o(t=n.context)&&o(t=t.$options._scopeId)&&c.setStyleScope(e.elm,t),n=n.parent;
o(t=Gt)&&t!==e.context&&t!==e.fnContext&&o(t=t.$options._scopeId)&&c.setStyleScope(e.elm,t)}function b(e,t,n
,r,o,i){for(;r<=o;++r)u(n[r],i,e,t,!1,n,r)}function _(e){var t,n,r=e.data;if(o(r))for(o(t=r.hook)&&o(t=t.des
troy)&&t(e),t=0;t<s.destroy.length;++t)s.destroy[t](e);if(o(t=e.children))for(n=0;n<e.children.length;++n)_(
e.children[n])}function w(e,t,n){for(;t<=n;++t){var r=e[t];o(r)&&(o(r.tag)?(x(r),_(r)):d(r.elm))}}function x
(e,t){if(o(t)||o(e.data)){var n,r=s.remove.length+1;for(o(t)?t.listeners+=r:t=function(e,t){function n(){0==
--n.listeners&&d(e)}return n.listeners=t,n}(e.elm,r),o(n=e.componentInstance)&&o(n=n._vnode)&&o(n.data)&&x(n
,t),n=0;n<s.remove.length;++n)s.remove[n](e,t);o(n=e.data.hook)&&o(n=n.remove)?n(e,t):t()}else d(e.elm)}func
tion C(e,t,n,r){for(var i=n;i<r;i++){var a=t[i];if(o(a)&&tr(e,a))return i}}function k(e,t,n,a,l,d){if(e!==t)
{o(t.elm)&&o(a)&&(t=a[l]=ye(t));var p=t.elm=e.elm;if(i(e.isAsyncPlaceholder))o(t.asyncFactory.resolved)?$(e.
elm,t,n):t.isAsyncPlaceholder=!0;else if(i(t.isStatic)&&i(e.isStatic)&&t.key===e.key&&(i(t.isCloned)||i(t.is
Once)))t.componentInstance=e.componentInstance;else{var f,v=t.data;o(v)&&o(f=v.hook)&&o(f=f.prepatch)&&f(e,t
);var m=e.children,g=t.children;if(o(v)&&h(t)){for(f=0;f<s.update.length;++f)s.update[f](e,t);o(f=v.hook)&&o
(f=f.update)&&f(e,t)}r(t.text)?o(m)&&o(g)?m!==g&&function(e,t,n,i,a){for(var s,l,d,p=0,f=0,v=t.length-1,m=t[
0],h=t[v],g=n.length-1,y=n[0],_=n[g],x=!a;p<=v&&f<=g;)r(m)?m=t[++p]:r(h)?h=t[--v]:tr(m,y)?(k(m,y,i,n,f),m=t[
++p],y=n[++f]):tr(h,_)?(k(h,_,i,n,g),h=t[--v],_=n[--g]):tr(m,_)?(k(m,_,i,n,g),x&&c.insertBefore(e,m.elm,c.ne
xtSibling(h.elm)),m=t[++p],_=n[--g]):tr(h,y)?(k(h,y,i,n,f),x&&c.insertBefore(e,h.elm,m.elm),h=t[--v],y=n[++f
]):(r(s)&&(s=nr(t,p,v)),r(l=o(y.key)?s[y.key]:C(y,t,p,v))?u(y,i,e,m.elm,!1,n,f):tr(d=t[l],y)?(k(d,y,i,n,f),t
[l]=void 0,x&&c.insertBefore(e,d.elm,m.elm)):u(y,i,e,m.elm,!1,n,f),y=n[++f]);p>v?b(e,r(n[g+1])?null:n[g+1].e
lm,n,f,g,i):f>g&&w(t,p,v)}(p,m,g,n,d):o(g)?(o(e.text)&&c.setTextContent(p,""),b(p,null,g,0,g.length-1,n)):o(
m)?w(m,0,m.length-1):o(e.text)&&c.setTextContent(p,""):e.text!==t.text&&c.setTextContent(p,t.text),o(v)&&o(f
=v.hook)&&o(f=f.postpatch)&&f(e,t)}}}function j(e,t,n){if(i(n)&&o(e.parent))e.parent.data.pendingInsert=t;el
se for(var r=0;r<t.length;++r)t[r].data.hook.insert(t[r])}var S=v("attrs,class,staticClass,staticStyle,key")
;function $(e,t,n,r){var a,s=t.tag,l=t.data,c=t.children;if(r=r||l&&l.pre,t.elm=e,i(t.isComment)&&o(t.asyncF
actory))return t.isAsyncPlaceholder=!0,!0;if(o(l)&&(o(a=l.hook)&&o(a=a.init)&&a(t,!0),o(a=t.componentInstanc
e)))return p(t,n),!0;if(o(s)){if(o(c))if(e.hasChildNodes())if(o(a=l)&&o(a=a.domProps)&&o(a=a.innerHTML)){if(
a!==e.innerHTML)return!1}else{for(var d=!0,u=e.firstChild,f=0;f<c.length;f++){if(!u||!$(u,c[f],n,r)){d=!1;br
eak}u=u.nextSibling}if(!d||u)return!1}else m(t,c,n);if(o(l)){var v=!1;for(var h in l)if(!S(h)){v=!0,g(t,n);b
reak}!v&&l.class&&rt(l.class)}}else e.data!==t.text&&(e.data=t.text);return!0}return function(e,t,n,a){if(!r
(t)){var l,d=!1,p=[];if(r(e))d=!0,u(t,p);else{var f=o(e.nodeType);if(!f&&tr(e,t))k(e,t,p,null,null,a);else{i
f(f){if(1===e.nodeType&&e.hasAttribute(I)&&(e.removeAttribute(I),n=!0),i(n)&&$(e,t,p))return j(t,p,!0),e;l=e
,e=new ve(c.tagName(l).toLowerCase(),{},[],void 0,l)}var v=e.elm,m=c.parentNode(v);if(u(t,p,v._leaveCb?null:
m,c.nextSibling(v)),o(t.parent))for(var g=t.parent,y=h(t);g;){for(var b=0;b<s.destroy.length;++b)s.destroy[b
](g);if(g.elm=t.elm,y){for(var x=0;x<s.create.length;++x)s.create[x](Qn,g);var C=g.data.hook.insert;if(C.mer
ged)for(var S=1;S<C.fns.length;S++)C.fns[S]()}else Xn(g);g=g.parent}o(m)?w([e],0,0):o(e.tag)&&_(e)}}return j
(t,p,d),t.elm}o(e)&&_(e)}}({nodeOps:Zn,modules:[fr,wr,Xr,to,fo,W?{create:Eo,activate:Eo,remove:function(e,t)
{!0!==e.data.show?Io(e,t):t()}}:{}].concat(cr)});Z&&document.addEventListener("selectionchange",(function(){
var e=document.activeElement;e&&e.vmodel&&Ko(e,"input")}));var Vo={inserted:function(e,t,n,r){"select"===n.t
ag?(r.elm&&!r.elm._vOptions?st(n,"postpatch",(function(){Vo.componentUpdated(e,t,n)})):zo(e,t,n.context),e._
vOptions=[].map.call(e.options,Wo)):("textarea"===n.tag||Kn(e.type))&&(e._vModifiers=t.modifiers,t.modifiers
.lazy||(e.addEventListener("compositionstart",Jo),e.addEventListener("compositionend",Ho),e.addEventListener
("change",Ho),Z&&(e.vmodel=!0)))},componentUpdated:function(e,t,n){if("select"===n.tag){zo(e,t,n.context);va
r r=e._vOptions,o=e._vOptions=[].map.call(e.options,Wo);o.some((function(e,t){return!D(e,r[t])}))&&(e.multip
le?t.value.some((function(e){return qo(e,o)})):t.value!==t.oldValue&&qo(t.value,o))&&Ko(e,"change")}}};funct
ion zo(e,t,n){Bo(e,t),(G||Y)&&setTimeout((function(){Bo(e,t)}),0)}function Bo(e,t,n){var r=t.value,o=e.multi
ple;if(!o||Array.isArray(r)){for(var i,a,s=0,l=e.options.length;s<l;s++)if(a=e.options[s],o)i=F(r,Wo(a))>-1,
a.selected!==i&&(a.selected=i);else if(D(Wo(a),r))return void(e.selectedIndex!==s&&(e.selectedIndex=s));o||(
e.selectedIndex=-1)}}function qo(e,t){return t.every((function(t){return!D(t,e)}))}function Wo(e){return"_va
lue"in e?e._value:e.value}function Jo(e){e.target.composing=!0}function Ho(e){e.target.composing&&(e.target.
composing=!1,Ko(e.target,"input"))}function Ko(e,t){var n=document.createEvent("HTMLEvents");n.initEvent(t,!
0,!0),e.dispatchEvent(n)}function Go(e){return!e.componentInstance||e.data&&e.data.transition?e:Go(e.compone
ntInstance._vnode)}var Zo={model:Vo,show:{bind:function(e,t,n){var r=t.value,o=(n=Go(n)).data&&n.data.transi
tion,i=e.__vOriginalDisplay="none"===e.style.display?"":e.style.display;r&&o?(n.data.show=!0,No(n,(function(
){e.style.display=i}))):e.style.display=r?i:"none"},update:function(e,t,n){var r=t.value;!r!=!t.oldValue&&((
n=Go(n)).data&&n.data.transition?(n.data.show=!0,r?No(n,(function(){e.style.display=e.__vOriginalDisplay})):
Io(n,(function(){e.style.display="none"}))):e.style.display=r?e.__vOriginalDisplay:"none")},unbind:function(
e,t,n,r,o){o||(e.style.display=e.__vOriginalDisplay)}}},Yo={name:String,appear:Boolean,css:Boolean,mode:Stri
ng,type:String,enterClass:String,leaveClass:String,enterToClass:String,leaveToClass:String,enterActiveClass:
String,leaveActiveClass:String,appearClass:String,appearActiveClass:String,appearToClass:String,duration:[Nu
mber,String,Object]};function Xo(e){var t=e&&e.componentOptions;return t&&t.Ctor.options.abstract?Xo(qt(t.ch
ildren)):e}function Qo(e){var t={},n=e.$options;for(var r in n.propsData)t[r]=e[r];var o=n._parentListeners;
for(var i in o)t[x(i)]=o[i];return t}function ei(e,t){if(/\d-keep-alive$/.test(t.tag))return e("keep-alive",
{props:t.componentOptions.propsData})}var ti=function(e){return e.tag||vt(e)},ni=function(e){return"show"===
e.name},ri={name:"transition",props:Yo,abstract:!0,render:function(e){var t=this,n=this.$slots.default;if(n&
&(n=n.filter(ti)).length){var r=this.mode,o=n[0];if(function(e){for(;e=e.parent;)if(e.data.transition)return
!0}(this.$vnode))return o;var i=Xo(o);if(!i)return o;if(this._leaving)return ei(e,o);var s="__transition-"+t
his._uid+"-";i.key=null==i.key?i.isComment?s+"comment":s+i.tag:a(i.key)?0===String(i.key).indexOf(s)?i.key:s
+i.key:i.key;var l=(i.data||(i.data={})).transition=Qo(this),c=this._vnode,d=Xo(c);if(i.data.directives&&i.d
ata.directives.some(ni)&&(i.data.show=!0),d&&d.data&&!function(e,t){return t.key===e.key&&t.tag===e.tag}(i,d
)&&!vt(d)&&(!d.componentInstance||!d.componentInstance._vnode.isComment)){var u=d.data.transition=A({},l);if
("out-in"===r)return this._leaving=!0,st(u,"afterLeave",(function(){t._leaving=!1,t.$forceUpdate()})),ei(e,o
);if("in-out"===r){if(vt(i))return c;var p,f=function(){p()};st(l,"afterEnter",f),st(l,"enterCancelled",f),s
t(u,"delayLeave",(function(e){p=e}))}}return o}}},oi=A({tag:String,moveClass:String},Yo);function ii(e){e.el
m._moveCb&&e.elm._moveCb(),e.elm._enterCb&&e.elm._enterCb()}function ai(e){e.data.newPos=e.elm.getBoundingCl
ientRect()}function si(e){var t=e.data.pos,n=e.data.newPos,r=t.left-n.left,o=t.top-n.top;if(r||o){e.data.mov
ed=!0;var i=e.elm.style;i.transform=i.WebkitTransform="translate("+r+"px,"+o+"px)",i.transitionDuration="0s"
}}delete oi.mode;var li={Transition:ri,TransitionGroup:{props:oi,beforeMount:function(){var e=this,t=this._u
pdate;this._update=function(n,r){var o=Zt(e);e.__patch__(e._vnode,e.kept,!1,!0),e._vnode=e.kept,o(),t.call(e
,n,r)}},render:function(e){for(var t=this.tag||this.$vnode.data.tag||"span",n=Object.create(null),r=this.pre
vChildren=this.children,o=this.$slots.default||[],i=this.children=[],a=Qo(this),s=0;s<o.length;s++){var l=o[
s];l.tag&&null!=l.key&&0!==String(l.key).indexOf("__vlist")&&(i.push(l),n[l.key]=l,(l.data||(l.data={})).tra
nsition=a)}if(r){for(var c=[],d=[],u=0;u<r.length;u++){var p=r[u];p.data.transition=a,p.data.pos=p.elm.getBo
undingClientRect(),n[p.key]?c.push(p):d.push(p)}this.kept=e(t,null,c),this.removed=d}return e(t,null,i)},upd
ated:function(){var e=this.prevChildren,t=this.moveClass||(this.name||"v")+"-move";e.length&&this.hasMove(e[
0].elm,t)&&(e.forEach(ii),e.forEach(ai),e.forEach(si),this._reflow=document.body.offsetHeight,e.forEach((fun
ction(e){if(e.data.moved){var n=e.elm,r=n.style;Ao(n,t),r.transform=r.WebkitTransform=r.transitionDuration="
",n.addEventListener(Co,n._moveCb=function e(r){r&&r.target!==n||r&&!/transform$/.test(r.propertyName)||(n.r
emoveEventListener(Co,e),n._moveCb=null,Mo(n,t))})}})))},methods:{hasMove:function(e,t){if(!bo)return!1;if(t
his._hasMove)return this._hasMove;var n=e.cloneNode();e._transitionClasses&&e._transitionClasses.forEach((fu
nction(e){ho(n,e)})),mo(n,t),n.style.display="none",this.$el.appendChild(n);var r=Oo(n);return this.$el.remo
veChild(n),this._hasMove=r.hasTransform}}}};xn.config.mustUseProp=Tn,xn.config.isReservedTag=Wn,xn.config.is
ReservedAttr=Mn,xn.config.getTagNamespace=Jn,xn.config.isUnknownElement=function(e){if(!W)return!0;if(Wn(e))
return!1;if(e=e.toLowerCase(),null!=Hn[e])return Hn[e];var t=document.createElement(e);return e.indexOf("-")
>-1?Hn[e]=t.constructor===window.HTMLUnknownElement||t.constructor===window.HTMLElement:Hn[e]=/HTMLUnknownEl
ement/.test(t.toString())},A(xn.options.directives,Zo),A(xn.options.components,li),xn.prototype.__patch__=W?
Uo:P,xn.prototype.$mount=function(e,t){return function(e,t,n){var r;return e.$el=t,e.$options.render||(e.$op
tions.render=he),Qt(e,"beforeMount"),r=function(){e._update(e._render(),n)},new pn(e,r,P,{before:function(){
e._isMounted&&!e._isDestroyed&&Qt(e,"beforeUpdate")}},!0),n=!1,null==e.$vnode&&(e._isMounted=!0,Qt(e,"mounte
d")),e}(this,e=e&&W?Gn(e):void 0,t)},W&&setTimeout((function(){E.devtools&&oe&&oe.emit("init",xn)}),0);var c
i,di=/\{\{((?:.|\r?\n)+?)\}\}/g,ui=/[-.*+?^${}()|[\]\/\\]/g,pi=_((function(e){var t=e[0].replace(ui,"\\$&"),
n=e[1].replace(ui,"\\$&");return new RegExp(t+"((?:.|\\n)+?)"+n,"g")})),fi={staticKeys:["staticClass"],trans
formNode:function(e,t){t.warn;var n=Fr(e,"class");n&&(e.staticClass=JSON.stringify(n));var r=Dr(e,"class",!1
);r&&(e.classBinding=r)},genData:function(e){var t="";return e.staticClass&&(t+="staticClass:"+e.staticClass
+","),e.classBinding&&(t+="class:"+e.classBinding+","),t}},vi={staticKeys:["staticStyle"],transformNode:func
tion(e,t){t.warn;var n=Fr(e,"style");n&&(e.staticStyle=JSON.stringify(no(n)));var r=Dr(e,"style",!1);r&&(e.s
tyleBinding=r)},genData:function(e){var t="";return e.staticStyle&&(t+="staticStyle:"+e.staticStyle+","),e.s
tyleBinding&&(t+="style:("+e.styleBinding+"),"),t}},mi=v("area,base,br,col,embed,frame,hr,img,input,isindex,
keygen,link,meta,param,source,track,wbr"),hi=v("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr,source"),gi
=v("address,article,aside,base,blockquote,body,caption,col,colgroup,dd,details,dialog,div,dl,dt,fieldset,fig
caption,figure,footer,form,h1,h2,h3,h4,h5,h6,head,header,hgroup,hr,html,legend,li,menuitem,meta,optgroup,opt
ion,param,rp,rt,source,style,summary,tbody,td,tfoot,th,thead,title,tr,track"),yi=/^\s*([^\s"'<>\/=]+)(?:\s*(
=)\s*(?:"([^"]*)"+|'([^']*)'+|([^\s"'=<>`]+)))?/,bi=/^\s*((?:v-[\w-]+:|@|:|#)\[[^=]+?\][^\s"'<>\/=]*)(?:\s*(
=)\s*(?:"([^"]*)"+|'([^']*)'+|([^\s"'=<>`]+)))?/,_i="[a-zA-Z_][\\-\\.0-9_a-zA-Z"+U.source+"]*",wi="((?:"+_i+
"\\:)?"+_i+")",xi=new RegExp("^<"+wi),Ci=/^\s*(\/?)>/,ki=new RegExp("^<\\/"+wi+"[^>]*>"),ji=/^<!DOCTYPE [^>]
+>/i,Si=/^<!\--/,$i=/^<!\[/,Ai=v("script,style,textarea",!0),Mi={},Pi={"&lt;":"<","&gt;":">","&quot;":'"',"&
amp;":"&","&#10;":"\n","&#9;":"\t","&#39;":"'"},Ti=/&(?:lt|gt|quot|amp|#39);/g,Oi=/&(?:lt|gt|quot|amp|#39|#1
0|#9);/g,Di=v("pre,textarea",!0),Fi=function(e,t){return e&&Di(e)&&"\n"===t[0]};function Ni(e,t){var n=t?Oi:
Ti;return e.replace(n,(function(e){return Pi[e]}))}var Ii,Li,Ri,Ei,Ui,Vi,zi,Bi,qi=/^@|^v-on:/,Wi=/^v-|^@|^:|
^#/,Ji=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,Hi=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,Ki=/^\(|\)$/g,Gi=/^\[.*\]$/,
Zi=/:(.*)$/,Yi=/^:|^\.|^v-bind:/,Xi=/\.[^.\]]+(?=[^\]]*$)/g,Qi=/^v-slot(:|$)|^#/,ea=/[\r\n]/,ta=/[ \f\t\r\n]
+/g,na=_((function(e){return(ci=ci||document.createElement("div")).innerHTML=e,ci.textContent})),ra="_empty_
";function oa(e,t,n){return{type:1,tag:e,attrsList:t,attrsMap:da(t),rawAttrsMap:{},parent:n,children:[]}}fun
ction ia(e,t){var n,r;(r=Dr(n=e,"key"))&&(n.key=r),e.plain=!e.key&&!e.scopedSlots&&!e.attrsList.length,funct
ion(e){var t=Dr(e,"ref");t&&(e.ref=t,e.refInFor=function(e){for(var t=e;t;){if(void 0!==t.for)return!0;t=t.p
arent}return!1}(e))}(e),function(e){var t;"template"===e.tag?(t=Fr(e,"scope"),e.slotScope=t||Fr(e,"slot-scop
e")):(t=Fr(e,"slot-scope"))&&(e.slotScope=t);var n=Dr(e,"slot");if(n&&(e.slotTarget='""'===n?'"default"':n,e
.slotTargetDynamic=!(!e.attrsMap[":slot"]&&!e.attrsMap["v-bind:slot"]),"template"===e.tag||e.slotScope||Ar(e
,"slot",n,function(e,t){return e.rawAttrsMap[":"+t]||e.rawAttrsMap["v-bind:"+t]||e.rawAttrsMap[t]}(e,"slot")
)),"template"===e.tag){var r=Nr(e,Qi);if(r){var o=la(r),i=o.name,a=o.dynamic;e.slotTarget=i,e.slotTargetDyna
mic=a,e.slotScope=r.value||ra}}else{var s=Nr(e,Qi);if(s){var l=e.scopedSlots||(e.scopedSlots={}),c=la(s),d=c
.name,u=c.dynamic,p=l[d]=oa("template",[],e);p.slotTarget=d,p.slotTargetDynamic=u,p.children=e.children.filt
er((function(e){if(!e.slotScope)return e.parent=p,!0})),p.slotScope=s.value||ra,e.children=[],e.plain=!1}}}(
e),function(e){"slot"===e.tag&&(e.slotName=Dr(e,"name"))}(e),function(e){var t;(t=Dr(e,"is"))&&(e.component=
t),null!=Fr(e,"inline-template")&&(e.inlineTemplate=!0)}(e);for(var o=0;o<Ri.length;o++)e=Ri[o](e,t)||e;retu
rn function(e){var t,n,r,o,i,a,s,l,c=e.attrsList;for(t=0,n=c.length;t<n;t++)if(r=o=c[t].name,i=c[t].value,Wi
.test(r))if(e.hasBindings=!0,(a=ca(r.replace(Wi,"")))&&(r=r.replace(Xi,"")),Yi.test(r))r=r.replace(Yi,""),i=
Cr(i),(l=Gi.test(r))&&(r=r.slice(1,-1)),a&&(a.prop&&!l&&"innerHtml"===(r=x(r))&&(r="innerHTML"),a.camel&&!l&
&(r=x(r)),a.sync&&(s=Rr(i,"$event"),l?Or(e,'"update:"+('+r+")",s,null,!1,0,c[t],!0):(Or(e,"update:"+x(r),s,n
ull,!1,0,c[t]),j(r)!==x(r)&&Or(e,"update:"+j(r),s,null,!1,0,c[t])))),a&&a.prop||!e.component&&zi(e.tag,e.att
rsMap.type,r)?$r(e,r,i,c[t],l):Ar(e,r,i,c[t],l);else if(qi.test(r))r=r.replace(qi,""),(l=Gi.test(r))&&(r=r.s
lice(1,-1)),Or(e,r,i,a,!1,0,c[t],l);else{var d=(r=r.replace(Wi,"")).match(Zi),u=d&&d[1];l=!1,u&&(r=r.slice(0
,-(u.length+1)),Gi.test(u)&&(u=u.slice(1,-1),l=!0)),Pr(e,r,o,i,u,l,a,c[t])}else Ar(e,r,JSON.stringify(i),c[t
]),!e.component&&"muted"===r&&zi(e.tag,e.attrsMap.type,r)&&$r(e,r,"true",c[t])}(e),e}function aa(e){var t;if
(t=Fr(e,"v-for")){var n=function(e){var t=e.match(Ji);if(t){var n={};n.for=t[2].trim();var r=t[1].trim().rep
lace(Ki,""),o=r.match(Hi);return o?(n.alias=r.replace(Hi,"").trim(),n.iterator1=o[1].trim(),o[2]&&(n.iterato
r2=o[2].trim())):n.alias=r,n}}(t);n&&A(e,n)}}function sa(e,t){e.ifConditions||(e.ifConditions=[]),e.ifCondit
ions.push(t)}function la(e){var t=e.name.replace(Qi,"");return t||"#"!==e.name[0]&&(t="default"),Gi.test(t)?
{name:t.slice(1,-1),dynamic:!0}:{name:'"'+t+'"',dynamic:!1}}function ca(e){var t=e.match(Xi);if(t){var n={};
return t.forEach((function(e){n[e.slice(1)]=!0})),n}}function da(e){for(var t={},n=0,r=e.length;n<r;n++)t[e[
n].name]=e[n].value;return t}var ua=/^xmlns:NS\d+/,pa=/^NS\d+:/;function fa(e){return oa(e.tag,e.attrsList.s
lice(),e.parent)}var va,ma,ha=[fi,vi,{preTransformNode:function(e,t){if("input"===e.tag){var n,r=e.attrsMap;
if(!r["v-model"])return;if((r[":type"]||r["v-bind:type"])&&(n=Dr(e,"type")),r.type||n||!r["v-bind"]||(n="("+
r["v-bind"]+").type"),n){var o=Fr(e,"v-if",!0),i=o?"&&("+o+")":"",a=null!=Fr(e,"v-else",!0),s=Fr(e,"v-else-i
f",!0),l=fa(e);aa(l),Mr(l,"type","checkbox"),ia(l,t),l.processed=!0,l.if="("+n+")==='checkbox'"+i,sa(l,{exp:
l.if,block:l});var c=fa(e);Fr(c,"v-for",!0),Mr(c,"type","radio"),ia(c,t),sa(l,{exp:"("+n+")==='radio'"+i,blo
ck:c});var d=fa(e);return Fr(d,"v-for",!0),Mr(d,":type",n),ia(d,t),sa(l,{exp:o,block:d}),a?l.else=!0:s&&(l.e
lseif=s),l}}}}],ga={expectHTML:!0,modules:ha,directives:{model:function(e,t,n){var r=t.value,o=t.modifiers,i
=e.tag,a=e.attrsMap.type;if(e.component)return Lr(e,r,o),!1;if("select"===i)!function(e,t,n){var r='var $$se
lectedVal = Array.prototype.filter.call($event.target.options,function(o){return o.selected}).map(function(o
){var val = "_value" in o ? o._value : o.value;return '+(n&&n.number?"_n(val)":"val")+"});";Or(e,"change",r=
r+" "+Rr(t,"$event.target.multiple ? $$selectedVal : $$selectedVal[0]"),null,!0)}(e,r,o);else if("input"===i
&&"checkbox"===a)!function(e,t,n){var r=n&&n.number,o=Dr(e,"value")||"null",i=Dr(e,"true-value")||"true",a=D
r(e,"false-value")||"false";$r(e,"checked","Array.isArray("+t+")?_i("+t+","+o+")>-1"+("true"===i?":("+t+")":
":_q("+t+","+i+")")),Or(e,"change","var $$a="+t+",$$el=$event.target,$$c=$$el.checked?("+i+"):("+a+");if(Arr
ay.isArray($$a)){var $$v="+(r?"_n("+o+")":o)+",$$i=_i($$a,$$v);if($$el.checked){$$i<0&&("+Rr(t,"$$a.concat([
$$v])")+")}else{$$i>-1&&("+Rr(t,"$$a.slice(0,$$i).concat($$a.slice($$i+1))")+")}}else{"+Rr(t,"$$c")+"}",null
,!0)}(e,r,o);else if("input"===i&&"radio"===a)!function(e,t,n){var r=n&&n.number,o=Dr(e,"value")||"null";$r(
e,"checked","_q("+t+","+(o=r?"_n("+o+")":o)+")"),Or(e,"change",Rr(t,o),null,!0)}(e,r,o);else if("input"===i|
|"textarea"===i)!function(e,t,n){var r=e.attrsMap.type,o=n||{},i=o.lazy,a=o.number,s=o.trim,l=!i&&"range"!==
r,c=i?"change":"range"===r?Wr:"input",d="$event.target.value";s&&(d="$event.target.value.trim()"),a&&(d="_n(
"+d+")");var u=Rr(t,d);l&&(u="if($event.target.composing)return;"+u),$r(e,"value","("+t+")"),Or(e,c,u,null,!
0),(s||a)&&Or(e,"blur","$forceUpdate()")}(e,r,o);else if(!E.isReservedTag(i))return Lr(e,r,o),!1;return!0},t
ext:function(e,t){t.value&&$r(e,"textContent","_s("+t.value+")",t)},html:function(e,t){t.value&&$r(e,"innerH
TML","_s("+t.value+")",t)}},isPreTag:function(e){return"pre"===e},isUnaryTag:mi,mustUseProp:Tn,canBeLeftOpen
Tag:hi,isReservedTag:Wn,getTagNamespace:Jn,staticKeys:function(e){return e.reduce((function(e,t){return e.co
ncat(t.staticKeys||[])}),[]).join(",")}(ha)},ya=_((function(e){return v("type,tag,attrsList,attrsMap,plain,p
arent,children,attrs,start,end,rawAttrsMap"+(e?","+e:""))})),ba=/^([\w$_]+|\([^)]*?\))\s*=>|^function(?:\s+[
\w$]+)?\s*\(/,_a=/\([^)]*?\);*$/,wa=/^[A-Za-z_$][\w$]*(?:\.[A-Za-z_$][\w$]*|\['[^']*?']|\["[^"]*?"]|\[\d+]|\
[[A-Za-z_$][\w$]*])*$/,xa={esc:27,tab:9,enter:13,space:32,up:38,left:37,right:39,down:40,delete:[8,46]},Ca={
esc:["Esc","Escape"],tab:"Tab",enter:"Enter",space:[" ","Spacebar"],up:["Up","ArrowUp"],left:["Left","ArrowL
eft"],right:["Right","ArrowRight"],down:["Down","ArrowDown"],delete:["Backspace","Delete","Del"]},ka=functio
n(e){return"if("+e+")return null;"},ja={stop:"$event.stopPropagation();",prevent:"$event.preventDefault();",
self:ka("$event.target !== $event.currentTarget"),ctrl:ka("!$event.ctrlKey"),shift:ka("!$event.shiftKey"),al
t:ka("!$event.altKey"),meta:ka("!$event.metaKey"),left:ka("'button' in $event && $event.button !== 0"),middl
e:ka("'button' in $event && $event.button !== 1"),right:ka("'button' in $event && $event.button !== 2")};fun
ction Sa(e,t){var n=t?"nativeOn:":"on:",r="",o="";for(var i in e){var a=$a(e[i]);e[i]&&e[i].dynamic?o+=i+","
+a+",":r+='"'+i+'":'+a+","}return r="{"+r.slice(0,-1)+"}",o?n+"_d("+r+",["+o.slice(0,-1)+"])":n+r}function $
a(e){if(!e)return"function(){}";if(Array.isArray(e))return"["+e.map((function(e){return $a(e)})).join(",")+"
]";var t=wa.test(e.value),n=ba.test(e.value),r=wa.test(e.value.replace(_a,""));if(e.modifiers){var o="",i=""
,a=[];for(var s in e.modifiers)if(ja[s])i+=ja[s],xa[s]&&a.push(s);else if("exact"===s){var l=e.modifiers;i+=
ka(["ctrl","shift","alt","meta"].filter((function(e){return!l[e]})).map((function(e){return"$event."+e+"Key"
})).join("||"))}else a.push(s);return a.length&&(o+=function(e){return"if(!$event.type.indexOf('key')&&"+e.m
ap(Aa).join("&&")+")return null;"}(a)),i&&(o+=i),"function($event){"+o+(t?"return "+e.value+".apply(null, ar
guments)":n?"return ("+e.value+").apply(null, arguments)":r?"return "+e.value:e.value)+"}"}return t||n?e.val
ue:"function($event){"+(r?"return "+e.value:e.value)+"}"}function Aa(e){var t=parseInt(e,10);if(t)return"$ev
ent.keyCode!=="+t;var n=xa[e],r=Ca[e];return"_k($event.keyCode,"+JSON.stringify(e)+","+JSON.stringify(n)+",$
event.key,"+JSON.stringify(r)+")"}var Ma={on:function(e,t){e.wrapListeners=function(e){return"_g("+e+","+t.v
alue+")"}},bind:function(e,t){e.wrapData=function(n){return"_b("+n+",'"+e.tag+"',"+t.value+","+(t.modifiers&
&t.modifiers.prop?"true":"false")+(t.modifiers&&t.modifiers.sync?",true":"")+")"}},cloak:P},Pa=function(e){t
his.options=e,this.warn=e.warn||jr,this.transforms=Sr(e.modules,"transformCode"),this.dataGenFns=Sr(e.module
s,"genData"),this.directives=A(A({},Ma),e.directives);var t=e.isReservedTag||T;this.maybeComponent=function(
e){return!!e.component||!t(e.tag)},this.onceId=0,this.staticRenderFns=[],this.pre=!1};function Ta(e,t){var n
=new Pa(t);return{render:"with(this){return "+(e?"script"===e.tag?"null":Oa(e,n):'_c("div")')+"}",staticRend
erFns:n.staticRenderFns}}function Oa(e,t){if(e.parent&&(e.pre=e.pre||e.parent.pre),e.staticRoot&&!e.staticPr
ocessed)return Da(e,t);if(e.once&&!e.onceProcessed)return Fa(e,t);if(e.for&&!e.forProcessed)return Ia(e,t);i
f(e.if&&!e.ifProcessed)return Na(e,t);if("template"!==e.tag||e.slotTarget||t.pre){if("slot"===e.tag)return f
unction(e,t){var n=e.slotName||'"default"',r=Ua(e,t),o="_t("+n+(r?",function(){return "+r+"}":""),i=e.attrs|
|e.dynamicAttrs?Ba((e.attrs||[]).concat(e.dynamicAttrs||[]).map((function(e){return{name:x(e.name),value:e.v
alue,dynamic:e.dynamic}}))):null,a=e.attrsMap["v-bind"];return!i&&!a||r||(o+=",null"),i&&(o+=","+i),a&&(o+=(
i?"":",null")+","+a),o+")"}(e,t);var n;if(e.component)n=function(e,t,n){var r=t.inlineTemplate?null:Ua(t,n,!
0);return"_c("+e+","+La(t,n)+(r?","+r:"")+")"}(e.component,e,t);else{var r;(!e.plain||e.pre&&t.maybeComponen
t(e))&&(r=La(e,t));var o=e.inlineTemplate?null:Ua(e,t,!0);n="_c('"+e.tag+"'"+(r?","+r:"")+(o?","+o:"")+")"}f
or(var i=0;i<t.transforms.length;i++)n=t.transforms[i](e,n);return n}return Ua(e,t)||"void 0"}function Da(e,
t){e.staticProcessed=!0;var n=t.pre;return e.pre&&(t.pre=e.pre),t.staticRenderFns.push("with(this){return "+
Oa(e,t)+"}"),t.pre=n,"_m("+(t.staticRenderFns.length-1)+(e.staticInFor?",true":"")+")"}function Fa(e,t){if(e
.onceProcessed=!0,e.if&&!e.ifProcessed)return Na(e,t);if(e.staticInFor){for(var n="",r=e.parent;r;){if(r.for
){n=r.key;break}r=r.parent}return n?"_o("+Oa(e,t)+","+t.onceId+++","+n+")":Oa(e,t)}return Da(e,t)}function N
a(e,t,n,r){return e.ifProcessed=!0,function e(t,n,r,o){if(!t.length)return o||"_e()";var i=t.shift();return
i.exp?"("+i.exp+")?"+a(i.block)+":"+e(t,n,r,o):""+a(i.block);function a(e){return r?r(e,n):e.once?Fa(e,n):Oa
(e,n)}}(e.ifConditions.slice(),t,n,r)}function Ia(e,t,n,r){var o=e.for,i=e.alias,a=e.iterator1?","+e.iterato
r1:"",s=e.iterator2?","+e.iterator2:"";return e.forProcessed=!0,(r||"_l")+"(("+o+"),function("+i+a+s+"){retu
rn "+(n||Oa)(e,t)+"})"}function La(e,t){var n="{",r=function(e,t){var n=e.directives;if(n){var r,o,i,a,s="di
rectives:[",l=!1;for(r=0,o=n.length;r<o;r++){i=n[r],a=!0;var c=t.directives[i.name];c&&(a=!!c(e,i,t.warn)),a
&&(l=!0,s+='{name:"'+i.name+'",rawName:"'+i.rawName+'"'+(i.value?",value:("+i.value+"),expression:"+JSON.str
ingify(i.value):"")+(i.arg?",arg:"+(i.isDynamicArg?i.arg:'"'+i.arg+'"'):"")+(i.modifiers?",modifiers:"+JSON.
stringify(i.modifiers):"")+"},")}return l?s.slice(0,-1)+"]":void 0}}(e,t);r&&(n+=r+","),e.key&&(n+="key:"+e.
key+","),e.ref&&(n+="ref:"+e.ref+","),e.refInFor&&(n+="refInFor:true,"),e.pre&&(n+="pre:true,"),e.component&
&(n+='tag:"'+e.tag+'",');for(var o=0;o<t.dataGenFns.length;o++)n+=t.dataGenFns[o](e);if(e.attrs&&(n+="attrs:
"+Ba(e.attrs)+","),e.props&&(n+="domProps:"+Ba(e.props)+","),e.events&&(n+=Sa(e.events,!1)+","),e.nativeEven
ts&&(n+=Sa(e.nativeEvents,!0)+","),e.slotTarget&&!e.slotScope&&(n+="slot:"+e.slotTarget+","),e.scopedSlots&&
(n+=function(e,t,n){var r=e.for||Object.keys(t).some((function(e){var n=t[e];return n.slotTargetDynamic||n.i
f||n.for||Ra(n)})),o=!!e.if;if(!r)for(var i=e.parent;i;){if(i.slotScope&&i.slotScope!==ra||i.for){r=!0;break
}i.if&&(o=!0),i=i.parent}var a=Object.keys(t).map((function(e){return Ea(t[e],n)})).join(",");return"scopedS
lots:_u(["+a+"]"+(r?",null,true":"")+(!r&&o?",null,false,"+function(e){for(var t=5381,n=e.length;n;)t=33*t^e
.charCodeAt(--n);return t>>>0}(a):"")+")"}(e,e.scopedSlots,t)+","),e.model&&(n+="model:{value:"+e.model.valu
e+",callback:"+e.model.callback+",expression:"+e.model.expression+"},"),e.inlineTemplate){var i=function(e,t
){var n=e.children[0];if(n&&1===n.type){var r=Ta(n,t.options);return"inlineTemplate:{render:function(){"+r.r
ender+"},staticRenderFns:["+r.staticRenderFns.map((function(e){return"function(){"+e+"}"})).join(",")+"]}"}}
(e,t);i&&(n+=i+",")}return n=n.replace(/,$/,"")+"}",e.dynamicAttrs&&(n="_b("+n+',"'+e.tag+'",'+Ba(e.dynamicA
ttrs)+")"),e.wrapData&&(n=e.wrapData(n)),e.wrapListeners&&(n=e.wrapListeners(n)),n}function Ra(e){return 1==
=e.type&&("slot"===e.tag||e.children.some(Ra))}function Ea(e,t){var n=e.attrsMap["slot-scope"];if(e.if&&!e.i
fProcessed&&!n)return Na(e,t,Ea,"null");if(e.for&&!e.forProcessed)return Ia(e,t,Ea);var r=e.slotScope===ra?"
":String(e.slotScope),o="function("+r+"){return "+("template"===e.tag?e.if&&n?"("+e.if+")?"+(Ua(e,t)||"undef
ined")+":undefined":Ua(e,t)||"undefined":Oa(e,t))+"}",i=r?"":",proxy:true";return"{key:"+(e.slotTarget||'"de
fault"')+",fn:"+o+i+"}"}function Ua(e,t,n,r,o){var i=e.children;if(i.length){var a=i[0];if(1===i.length&&a.f
or&&"template"!==a.tag&&"slot"!==a.tag){var s=n?t.maybeComponent(a)?",1":",0":"";return""+(r||Oa)(a,t)+s}var
 l=n?function(e,t){for(var n=0,r=0;r<e.length;r++){var o=e[r];if(1===o.type){if(Va(o)||o.ifConditions&&o.ifC
onditions.some((function(e){return Va(e.block)}))){n=2;break}(t(o)||o.ifConditions&&o.ifConditions.some((fun
ction(e){return t(e.block)})))&&(n=1)}}return n}(i,t.maybeComponent):0,c=o||za;return"["+i.map((function(e){
return c(e,t)})).join(",")+"]"+(l?","+l:"")}}function Va(e){return void 0!==e.for||"template"===e.tag||"slot
"===e.tag}function za(e,t){return 1===e.type?Oa(e,t):3===e.type&&e.isComment?(r=e,"_e("+JSON.stringify(r.tex
t)+")"):"_v("+(2===(n=e).type?n.expression:qa(JSON.stringify(n.text)))+")";var n,r}function Ba(e){for(var t=
"",n="",r=0;r<e.length;r++){var o=e[r],i=qa(o.value);o.dynamic?n+=o.name+","+i+",":t+='"'+o.name+'":'+i+","}
return t="{"+t.slice(0,-1)+"}",n?"_d("+t+",["+n.slice(0,-1)+"])":t}function qa(e){return e.replace(/\u2028/g
,"\\u2028").replace(/\u2029/g,"\\u2029")}function Wa(e,t){try{return new Function(e)}catch(n){return t.push(
{err:n,code:e}),P}}function Ja(e){var t=Object.create(null);return function(n,r,o){(r=A({},r)).warn,delete r
.warn;var i=r.delimiters?String(r.delimiters)+n:n;if(t[i])return t[i];var a=e(n,r),s={},l=[];return s.render
=Wa(a.render,l),s.staticRenderFns=a.staticRenderFns.map((function(e){return Wa(e,l)})),t[i]=s}}new RegExp("\
\b"+"do,if,for,let,new,try,var,case,else,with,await,break,catch,class,const,super,throw,while,yield,delete,e
xport,import,return,switch,default,extends,finally,continue,debugger,function,arguments".split(",").join("\\
b|\\b")+"\\b");var Ha,Ka,Ga=(Ha=function(e,t){var n=function(e,t){Ii=t.warn||jr,Vi=t.isPreTag||T,zi=t.mustUs
eProp||T,Bi=t.getTagNamespace||T,t.isReservedTag,Ri=Sr(t.modules,"transformNode"),Ei=Sr(t.modules,"preTransf
ormNode"),Ui=Sr(t.modules,"postTransformNode"),Li=t.delimiters;var n,r,o=[],i=!1!==t.preserveWhitespace,a=t.
whitespace,s=!1,l=!1;function c(e){if(d(e),s||e.processed||(e=ia(e,t)),o.length||e===n||n.if&&(e.elseif||e.e
lse)&&sa(n,{exp:e.elseif,block:e}),r&&!e.forbidden)if(e.elseif||e.else)a=e,(c=function(e){for(var t=e.length
;t--;){if(1===e[t].type)return e[t];e.pop()}}(r.children))&&c.if&&sa(c,{exp:a.elseif,block:a});else{if(e.slo
tScope){var i=e.slotTarget||'"default"';(r.scopedSlots||(r.scopedSlots={}))[i]=e}r.children.push(e),e.parent
=r}var a,c;e.children=e.children.filter((function(e){return!e.slotScope})),d(e),e.pre&&(s=!1),Vi(e.tag)&&(l=
!1);for(var u=0;u<Ui.length;u++)Ui[u](e,t)}function d(e){if(!l)for(var t;(t=e.children[e.children.length-1])
&&3===t.type&&" "===t.text;)e.children.pop()}return function(e,t){for(var n,r,o=[],i=t.expectHTML,a=t.isUnar
yTag||T,s=t.canBeLeftOpenTag||T,l=0;e;){if(n=e,r&&Ai(r)){var c=0,d=r.toLowerCase(),u=Mi[d]||(Mi[d]=new RegEx
p("([\\s\\S]*?)(</"+d+"[^>]*>)","i")),p=e.replace(u,(function(e,n,r){return c=r.length,Ai(d)||"noscript"===d
||(n=n.replace(/<!\--([\s\S]*?)-->/g,"$1").replace(/<!\[CDATA\[([\s\S]*?)]]>/g,"$1")),Fi(d,n)&&(n=n.slice(1)
),t.chars&&t.chars(n),""}));l+=e.length-p.length,e=p,S(d,l-c,l)}else{var f=e.indexOf("<");if(0===f){if(Si.te
st(e)){var v=e.indexOf("--\x3e");if(v>=0){t.shouldKeepComment&&t.comment(e.substring(4,v),l,l+v+3),C(v+3);co
ntinue}}if($i.test(e)){var m=e.indexOf("]>");if(m>=0){C(m+2);continue}}var h=e.match(ji);if(h){C(h[0].length
);continue}var g=e.match(ki);if(g){var y=l;C(g[0].length),S(g[1],y,l);continue}var b=k();if(b){j(b),Fi(b.tag
Name,e)&&C(1);continue}}var _=void 0,w=void 0,x=void 0;if(f>=0){for(w=e.slice(f);!(ki.test(w)||xi.test(w)||S
i.test(w)||$i.test(w)||(x=w.indexOf("<",1))<0);)f+=x,w=e.slice(f);_=e.substring(0,f)}f<0&&(_=e),_&&C(_.lengt
h),t.chars&&_&&t.chars(_,l-_.length,l)}if(e===n){t.chars&&t.chars(e);break}}function C(t){l+=t,e=e.substring
(t)}function k(){var t=e.match(xi);if(t){var n,r,o={tagName:t[1],attrs:[],start:l};for(C(t[0].length);!(n=e.
match(Ci))&&(r=e.match(bi)||e.match(yi));)r.start=l,C(r[0].length),r.end=l,o.attrs.push(r);if(n)return o.una
rySlash=n[1],C(n[0].length),o.end=l,o}}function j(e){var n=e.tagName,l=e.unarySlash;i&&("p"===r&&gi(n)&&S(r)
,s(n)&&r===n&&S(n));for(var c=a(n)||!!l,d=e.attrs.length,u=new Array(d),p=0;p<d;p++){var f=e.attrs[p],v=f[3]
||f[4]||f[5]||"",m="a"===n&&"href"===f[1]?t.shouldDecodeNewlinesForHref:t.shouldDecodeNewlines;u[p]={name:f[
1],value:Ni(v,m)}}c||(o.push({tag:n,lowerCasedTag:n.toLowerCase(),attrs:u,start:e.start,end:e.end}),r=n),t.s
tart&&t.start(n,u,c,e.start,e.end)}function S(e,n,i){var a,s;if(null==n&&(n=l),null==i&&(i=l),e)for(s=e.toLo
werCase(),a=o.length-1;a>=0&&o[a].lowerCasedTag!==s;a--);else a=0;if(a>=0){for(var c=o.length-1;c>=a;c--)t.e
nd&&t.end(o[c].tag,n,i);o.length=a,r=a&&o[a-1].tag}else"br"===s?t.start&&t.start(e,[],!0,n,i):"p"===s&&(t.st
art&&t.start(e,[],!1,n,i),t.end&&t.end(e,n,i))}S()}(e,{warn:Ii,expectHTML:t.expectHTML,isUnaryTag:t.isUnaryT
ag,canBeLeftOpenTag:t.canBeLeftOpenTag,shouldDecodeNewlines:t.shouldDecodeNewlines,shouldDecodeNewlinesForHr
ef:t.shouldDecodeNewlinesForHref,shouldKeepComment:t.comments,outputSourceRange:t.outputSourceRange,start:fu
nction(e,i,a,d,u){var p=r&&r.ns||Bi(e);G&&"svg"===p&&(i=function(e){for(var t=[],n=0;n<e.length;n++){var r=e
[n];ua.test(r.name)||(r.name=r.name.replace(pa,""),t.push(r))}return t}(i));var f,v=oa(e,i,r);p&&(v.ns=p),"s
tyle"!==(f=v).tag&&("script"!==f.tag||f.attrsMap.type&&"text/javascript"!==f.attrsMap.type)||re()||(v.forbid
den=!0);for(var m=0;m<Ei.length;m++)v=Ei[m](v,t)||v;s||(function(e){null!=Fr(e,"v-pre")&&(e.pre=!0)}(v),v.pr
e&&(s=!0)),Vi(v.tag)&&(l=!0),s?function(e){var t=e.attrsList,n=t.length;if(n)for(var r=e.attrs=new Array(n),
o=0;o<n;o++)r[o]={name:t[o].name,value:JSON.stringify(t[o].value)},null!=t[o].start&&(r[o].start=t[o].start,
r[o].end=t[o].end);else e.pre||(e.plain=!0)}(v):v.processed||(aa(v),function(e){var t=Fr(e,"v-if");if(t)e.if
=t,sa(e,{exp:t,block:e});else{null!=Fr(e,"v-else")&&(e.else=!0);var n=Fr(e,"v-else-if");n&&(e.elseif=n)}}(v)
,function(e){null!=Fr(e,"v-once")&&(e.once=!0)}(v)),n||(n=v),a?c(v):(r=v,o.push(v))},end:function(e,t,n){var
 i=o[o.length-1];o.length-=1,r=o[o.length-1],c(i)},chars:function(e,t,n){if(r&&(!G||"textarea"!==r.tag||r.at
trsMap.placeholder!==e)){var o,c,d,u=r.children;(e=l||e.trim()?"script"===(o=r).tag||"style"===o.tag?e:na(e)
:u.length?a?"condense"===a&&ea.test(e)?"":" ":i?" ":"":"")&&(l||"condense"!==a||(e=e.replace(ta," ")),!s&&"
"!==e&&(c=function(e,t){var n=t?pi(t):di;if(n.test(e)){for(var r,o,i,a=[],s=[],l=n.lastIndex=0;r=n.exec(e);)
{(o=r.index)>l&&(s.push(i=e.slice(l,o)),a.push(JSON.stringify(i)));var c=Cr(r[1].trim());a.push("_s("+c+")")
,s.push({"@binding":c}),l=o+r[0].length}return l<e.length&&(s.push(i=e.slice(l)),a.push(JSON.stringify(i))),
{expression:a.join("+"),tokens:s}}}(e,Li))?d={type:2,expression:c.expression,tokens:c.tokens,text:e}:" "===e
&&u.length&&" "===u[u.length-1].text||(d={type:3,text:e}),d&&u.push(d))}},comment:function(e,t,n){if(r){var
o={type:3,text:e,isComment:!0};r.children.push(o)}}}),n}(e.trim(),t);!1!==t.optimize&&function(e,t){e&&(va=y
a(t.staticKeys||""),ma=t.isReservedTag||T,function e(t){if(t.static=function(e){return 2!==e.type&&(3===e.ty
pe||!(!e.pre&&(e.hasBindings||e.if||e.for||m(e.tag)||!ma(e.tag)||function(e){for(;e.parent;){if("template"!=
=(e=e.parent).tag)return!1;if(e.for)return!0}return!1}(e)||!Object.keys(e).every(va))))}(t),1===t.type){if(!
ma(t.tag)&&"slot"!==t.tag&&null==t.attrsMap["inline-template"])return;for(var n=0,r=t.children.length;n<r;n+
+){var o=t.children[n];e(o),o.static||(t.static=!1)}if(t.ifConditions)for(var i=1,a=t.ifConditions.length;i<
a;i++){var s=t.ifConditions[i].block;e(s),s.static||(t.static=!1)}}}(e),function e(t,n){if(1===t.type){if((t
.static||t.once)&&(t.staticInFor=n),t.static&&t.children.length&&(1!==t.children.length||3!==t.children[0].t
ype))return void(t.staticRoot=!0);if(t.staticRoot=!1,t.children)for(var r=0,o=t.children.length;r<o;r++)e(t.
children[r],n||!!t.for);if(t.ifConditions)for(var i=1,a=t.ifConditions.length;i<a;i++)e(t.ifConditions[i].bl
ock,n)}}(e,!1))}(n,t);var r=Ta(n,t);return{ast:n,render:r.render,staticRenderFns:r.staticRenderFns}},functio
n(e){function t(t,n){var r=Object.create(e),o=[],i=[];if(n)for(var a in n.modules&&(r.modules=(e.modules||[]
).concat(n.modules)),n.directives&&(r.directives=A(Object.create(e.directives||null),n.directives)),n)"modul
es"!==a&&"directives"!==a&&(r[a]=n[a]);r.warn=function(e,t,n){(n?i:o).push(e)};var s=Ha(t.trim(),r);return s
.errors=o,s.tips=i,s}return{compile:t,compileToFunctions:Ja(t)}})(ga),Za=(Ga.compile,Ga.compileToFunctions);
function Ya(e){return(Ka=Ka||document.createElement("div")).innerHTML=e?'<a href="\n"/>':'<div a="\n"/>',Ka.
innerHTML.indexOf("&#10;")>0}var Xa=!!W&&Ya(!1),Qa=!!W&&Ya(!0),es=_((function(e){var t=Gn(e);return t&&t.inn
erHTML})),ts=xn.prototype.$mount;return xn.prototype.$mount=function(e,t){if((e=e&&Gn(e))===document.body||e
===document.documentElement)return this;var n=this.$options;if(!n.render){var r=n.template;if(r)if("string"=
=typeof r)"#"===r.charAt(0)&&(r=es(r));else{if(!r.nodeType)return this;r=r.innerHTML}else e&&(r=function(e){
if(e.outerHTML)return e.outerHTML;var t=document.createElement("div");return t.appendChild(e.cloneNode(!0)),
t.innerHTML}(e));if(r){var o=Za(r,{outputSourceRange:!1,shouldDecodeNewlines:Xa,shouldDecodeNewlinesForHref:
Qa,delimiters:n.delimiters,comments:n.comments},this),i=o.render,a=o.staticRenderFns;n.render=i,n.staticRend
erFns=a}}return ts.call(this,e,t)},xn.compile=Za,xn}()}).call(this,n("./node_modules/webpack/buildin/global.
js"),n("./node_modules/timers-browserify/main.js").setImmediate)},"./node_modules/webpack/buildin/global.js"
:function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==t
ypeof window&&(n=window)}e.exports=n}});
\ No newline at end of file

```

---

根据对 `appweb/src` 路径下当前分支（jwt_fetch_clean）与 main 分支的详细 diff 检查，结合你的四点要求，现做如下代码 review 及优化建议：

---

### 1. 组件/变量命名规范检查

- **vue-resource-adapter.js** 新增文件，采用了 `vueResourceAdapter` 命名，结构清晰，方法如 `createResource`、`install`、`mapResponse` 等均符合 JS/前端社区通用命名规范，变量名语义明确。
- 各 RMSrv 相关 fetch 方法（如 rmsrv.coffee、rmsrv5web.coffee、rmsrv5wk.coffee）均采用了 `fetch`、`options`、`callback` 等标准命名，参数处理也较为规范。
- 组件/变量命名整体风格统一，未见明显不规范或混乱命名。

**建议：**
- 建议所有新加/重构的函数、对象、参数都补充详细的 JSDoc 注释，便于团队协作和后续维护。
- 变量名如 `ret`、`fd`、`cb` 等可适当补充注释说明其含义，提升可读性。

---

### 2. 重复代码/可抽象为通用组件或函数

- RMSrv.fetch 在 rmsrv.coffee、rmsrv5web.coffee、rmsrv5wk.coffee 三处实现高度相似，均为统一 HTTP 请求封装，支持 Promise/callback 多种调用方式，参数兼容性处理一致。
- wepage.coffee、wepageEdit.coffee、wepageEditNew.coffee 等文件中，原有大量 `$.ajax` 调用已统一替换为 `RMSrv.fetch`，极大提升了代码复用性和一致性。
- stdfn.js 也已将底层请求方式统一为 RMSrv.fetch。

**建议：**
- RMSrv.fetch 的参数兼容、错误处理、Promise/callback 兼容等逻辑建议抽离为 util 工具函数，减少重复代码。
- 建议将 fetch 相关的 JSDoc 注释写在 RMSrv.fetch 及其 util 工具函数上，明确参数、返回值、异常情况。
- 如果后续还有其他 HTTP 封装需求，可考虑进一步抽象为更通用的 HTTP 工具库。

---

### 3. 潜在性能问题或 bug

- **性能：**
  - RMSrv.fetch 内部大量使用 `JSON.stringify`，如 body 为大对象时可能有性能瓶颈，建议在 body 体积较大时做分片或压缩处理。
  - wepageEdit.coffee/wepageEditNew.coffee 等文件中，文件上传后多次 await RMSrv.fetch，建议合并为批量请求或并发 Promise.all，减少等待时间。
- **bug 风险：**
  - fetch 的错误处理部分，部分地方只 catch 并 flashMessage，未做详细日志记录，建议统一加上错误日志上报。
  - RMSrv.fetch 的参数兼容性处理较为复杂，建议补充单元测试，覆盖多种调用场景（如 callback、Promise、options 传参等）。
  - wepageEdit.coffee/wepageEditNew.coffee 文件上传相关逻辑，catch 后依然 await RMSrv.fetch 上报失败，若网络异常可能导致多次失败，建议加重试机制。

---

### 4. 具体优化建议和修改意见

#### 代码结构与复用
- 建议将 RMSrv.fetch 相关的参数兼容、错误处理、Promise/callback 兼容等逻辑抽离为 util 工具函数，减少重复代码。
- fetch 相关的 JSDoc 注释应详细说明参数、返回值、异常情况，便于团队协作。
- 文件上传、批量请求等场景建议用 Promise.all 并发处理，提升性能。

#### 错误处理与健壮性
- fetch 的 catch 逻辑建议统一加上错误日志上报，便于排查问题。
- RMSrv.fetch 建议补充单元测试，覆盖多种调用场景。
- 文件上传 catch 后的失败上报建议加重试机制，提升健壮性。

#### 命名与注释
- 变量名如 ret、fd、cb 等建议补充注释说明其含义。
- 新增/重构函数、对象、参数建议补充详细 JSDoc 注释。

#### 兼容性与扩展性
- RMSrv.fetch 支持多种参数传递方式，建议在文档中详细说明，避免误用。
- vue-resource-adapter.js 建议补充对 interceptors、options 等属性的详细说明和用法示例。

---

### 总结

本次 diff 主要是将原有各处分散的 HTTP 请求统一为 RMSrv.fetch，并新增 vue-resource 兼容适配器，极大提升了代码一致性和可维护性。整体命名规范、结构清晰，未见明显重复代码。建议进一步抽象 fetch 相关工具、补充注释和单元测试、优化批量请求和错误处理，提升健壮性和性能。

如需对某个具体文件或函数做更细致的 review，请告知！

---


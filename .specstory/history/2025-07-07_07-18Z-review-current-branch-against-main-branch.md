<!-- Generated by SpecStory -->

# Review current branch against main branch (2025-07-07 07:18Z)

_**User**_

@Branch (Diff with Main Branch) 
请帮我 review  @/src  路径下 当前分支和main分支的差异，要求如下:
1.检查是否符合项目的组件/变量命名规范;
2.检查是否有重复代码或可以抽象为通用组件/涵数的部分;
3.发现潜在的性能问题或 bug;
4.给出具体的优化建议和修改意见。

---

_**Assistant**_

我来帮您review当前分支与main分支的差异。首先让我检查一下当前的分支状态和差异。


---

```bash
cd /home/<USER>/rm1/appweb/src && git status
```

```
[mac@rocky rm1]$ cd /home/<USER>/rm1/appweb/src && git status
On branch forminput_soldDesc
Your branch is up to date with 'origin/forminput_soldDesc'.

Untracked files:
  (use "git add <file>..." to include in what will be committed)
        ../docs/Change_requirements/20250703_ai_conversation_sharing_guide.md
        ../docs/Change_requirements/AI_Conversations/
        ../docs/Change_requirements/long_string.md
        "../docs/Change_requirements/\347\277\273\350\257\221 copy.md"
        "../docs/Change_requirements/\347\277\273\350\257\221.csv"
        "../docs/Change_requirements/\347\277\273\350\257\221.md"
        ../docs/Data/school.txt
        "../docs/Dev_school/\351\227\256\351\242\230\346\200\273\347\273\223.md"
        ../docs/Dev_user_email/aa.md
        ../docs/Dev_user_email/used_user.md
        batch/pushNotify/watchTasks copy.coffee

nothing added to commit but untracked files present (use "git add" to track)
[mac@rocky src]$
```

---

```bash
git diff main --name-only
```

```
[mac@rocky src]$ git diff main --name-only
docs/Change_requirements/20250702_fix_sid_merge.ai.md
docs/Change_requirements/20250702_fix_sid_merge.md
docs/Change_requirements/20250715_forminput_soldDesc.md
src/apps/80_sites/AppRM/propRelated/contactRealtor.coffee
src/coffee4client/common.js
src/coffee4client/components/contactRealtor_mixins.js
src/libapp/common.coffee
src/libapp/contactRealtor.coffee
src/libapp/impMappingCreaReso.coffee
src/libapp/saveToMaster.coffee
src/unitTest/libapp/saveToMaster.coffee
src/unitTest/propertyImport/03_propertyImportMerge.coffee
src/unitTest/propertyImport/fixtures/rni/bridge_bcre_merged.json
src/unitTest/propertyImport/fixtures/rni/mls_bcre_records.json
src/unitTest/propertyImport/fixtures/rni/reso_crea_merged.json
src/unitTest/propertyImport/fixtures/vow/properties_expected.json
src/webroot/public/js/entry/appEvaluationResultPage.js
src/webroot/public/js/entry/appListSearch.js
src/webroot/public/js/entry/appPropDetailPage.js
src/webroot/public/js/entry/projDetailPage.js
src/webroot/public/js/entry/propDetailPage.js
src/webroot/public/js/rmapp.min.js
[mac@rocky src]$
```

---

现在让我查看具体的代码差异，以便进行详细的review：


---

```bash
git diff main --stat
```

```
[mac@rocky src]$ git diff main --stat
 docs/Change_requirements/20250702_fix_sid_merge.ai.md             |  703 -----------------
 docs/Change_requirements/20250702_fix_sid_merge.md                |  208 -----
 docs/Change_requirements/20250715_forminput_soldDesc.md           |   22 +
 src/apps/80_sites/AppRM/propRelated/contactRealtor.coffee         |    6 +-
 src/coffee4client/common.js                                       |    3 +-
 src/coffee4client/components/contactRealtor_mixins.js             |    2 +-
 src/libapp/common.coffee                                          |    1 +
 src/libapp/contactRealtor.coffee                                  |    8 +-
 src/libapp/impMappingCreaReso.coffee                              |    2 +-
 src/libapp/saveToMaster.coffee                                    |  251 +++---
 src/unitTest/libapp/saveToMaster.coffee                           |    2 -
 src/unitTest/propertyImport/03_propertyImportMerge.coffee         |    6 -
 src/unitTest/propertyImport/fixtures/rni/bridge_bcre_merged.json  |  341 --------
 src/unitTest/propertyImport/fixtures/rni/mls_bcre_records.json    |    2 +-
 src/unitTest/propertyImport/fixtures/rni/reso_crea_merged.json    |  196 -----
 src/unitTest/propertyImport/fixtures/vow/properties_expected.json | 1406 +++++++++++++++------------------
 src/webroot/public/js/entry/appEvaluationResultPage.js            |    2 +-
 src/webroot/public/js/entry/appListSearch.js                      |    2 +-
 src/webroot/public/js/entry/appPropDetailPage.js                  |    2 +-
 src/webroot/public/js/entry/projDetailPage.js                     |    2 +-
 src/webroot/public/js/entry/propDetailPage.js                     |    2 +-
 src/webroot/public/js/rmapp.min.js                                |    2 +-
 22 files changed, 784 insertions(+), 2387 deletions(-)
[mac@rocky src]$
```

---

让我查看主要的代码变更文件，特别是核心业务逻辑文件：


---

Could not find file 'src/libapp/saveToMaster.coffee' in the workspace.

---

Read file: appweb/src/libapp/saveToMaster.coffee

---

```bash
git diff main src/libapp/saveToMaster.coffee
```

```
[mac@rocky src]$ git diff main src/libapp/saveToMaster.coffee
fatal: ambiguous argument 'src/libapp/saveToMaster.coffee': unknown revision or path not in the working tree
.
Use '--' to separate paths from revisions, like this:
'git <command> [<revision>...] -- [<file>...]'
[mac@rocky src]$
```

---

```bash
git diff main -- libapp/saveToMaster.coffee
```

```
[mac@rocky src]$ git diff main -- libapp/saveToMaster.coffee
diff --git a/src/libapp/saveToMaster.coffee b/src/libapp/saveToMaster.coffee
index ad548050..f52acfd1 100644
--- a/src/libapp/saveToMaster.coffee
+++ b/src/libapp/saveToMaster.coffee
@@ -786,181 +786,105 @@ unsetTags = ({tags, unset, set}) ->
       unset[key] = 1
 
 ###
-# @description 处理sid字段，支持origSid
-# @param {Object} branch - 查询分支对象
+  query to find duplicate props other than self
+  same uaddr,status, ptype, onD within oneDay.same area,same price.same unt
 ###
-applySidCondition = (branch,obj) ->
-  if obj.origSid
-    branch.sid = { $in: [obj.sid, obj.origSid] }
-  else
-    branch.sid = obj.sid
-
-###
-# @description 统一应用通用查询条件到分支
-# @param {Object} branch - 查询分支对象
-# @param {Object} obj - 房源对象
-###
-applyCommonConditions = (branch,obj) ->
-  # 添加价格条件 (lp/lpr)
-  for field in ['lp', 'lpr']
-    if obj[field]
-      branch[field] = obj[field]
-    else
-      branch[field] = null
-
-  # 添加单元号条件 (unt)
-  if obj.unt?
-    branch.unt = '' + obj.unt
-  else
-    branch.unt = null
-
-  # 添加src字段条件
-  if obj.src in ['TRB', 'BRE']
-    branch.src = { $ne: obj.src }
-
-  # 特殊处理：没有图片时去掉TRB限制
-  if (obj.src is 'TRB') and (not obj.pho)
-    delete branch.src
-
-  # 添加总面积条件
-  if obj.tot_area
-    branch.tot_area = obj.tot_area
-
-
-###
-# @description 构建房源merge查询条件
-# @param {Object} obj - 房源对象，包含sid、prov、uaddr等字段
-# @param {Boolean} isRHBNotFound - 是否为RHB房源未找到的情况
-# @param {Boolean} shouldExpandOnDRange - 是否扩大onD时间范围
-# @returns {Object} MongoDB查询条件对象
-# @note 重构后的查询结构：基础条件在$or外部，区分性字段在$or内部
-###
-buildQuery = (obj, isRHBNotFound = false, shouldExpandOnDRange = false) ->
+module.exports.buildQuery = buildQuery = (obj, isRHBNotFound = false, shouldExpandOnDRange = false) ->
+  onDMergeDateSpanForDiffSid = config?.serverBase?.onDMergeDateSpanForDiffSid or (24 * 3600000)
   onDTime = inputToDate(obj.onD).getTime()
+  #计算onD ld or picMt.
+  onDFromOneDay = inputToDateNum(new Date(onDTime - onDMergeDateSpanForDiffSid))
+  onDToOneDay = inputToDateNum(new Date(onDTime + onDMergeDateSpanForDiffSid))
 
-  # 计算时间范围
-  timeRanges = calculateTimeRanges(onDTime, shouldExpandOnDRange, obj.pho)
+  # 条件化放宽查询,第一次查询没找到,则放宽onD限制,默认为5天
+  if shouldExpandOnDRange
+    onDRangeExpansionValue = config?.serverBase?.onDRangeExpansionValue or (5 * 24 * 3600000)
+    onDFromOneDay = inputToDateNum(new Date(onDTime - onDRangeExpansionValue))
+    onDToOneDay = inputToDateNum(new Date(onDTime + onDRangeExpansionValue))
 
-  # 基础查询条件（所有策略共用，只包含核心字段）
-  baseConditions = {
-    _id: { $ne: obj._id }      # 排除当前记录
-    merged: null               # 未合并记录
-    del: null                  # 未删除记录
-    ptype: obj.ptype          # 相同属性类型
-  }
+  # 如果没有图片限制到5-7天
+  # 当有图片的时候,取消merged,重新查找
 
-  # 策略1: sid + uaddr匹配（只包含sid、uaddr、onD三个字段）
-  sidUaddrQuery = Object.assign {}, baseConditions, {
-    uaddr: obj.uaddr
-    onD: { $gte: timeRanges.halfMonth.from, $lte: timeRanges.halfMonth.to }
+  onDMergeDateSpanForSameSid = config?.serverBase?.onDMergeDateSpanForSameSid or (15 * 24 * 3600000)
+  #计算onD ld or picMt.
+  onDFromHalfMonth = inputToDateNum(new Date(onDTime - onDMergeDateSpanForSameSid))
+  onDToHalfMonth = inputToDateNum(new Date(onDTime + onDMergeDateSpanForSameSid))
+
+  q = {
+    _id: { $ne: obj._id }
+    uaddr: obj.uaddr # add index for uaddr
+    merged: null
+    del: null
   }
-  applySidCondition(sidUaddrQuery,obj)
-
-  # 策略2: sid + prov匹配（包含完整条件）
-  sidProvQuery = Object.assign {}, baseConditions,{
-    prov: obj.prov
-    onD: { $gte: timeRanges.halfMonth.from, $lte: timeRanges.halfMonth.to }
+  # NOTE: sid,uaddr一致        onD半个月以内 与其它条件可以忽略
+  sidQuery = {
+    sid: obj.sid,
+    $and: [
+      { onD: { $gte: onDFromHalfMonth } },
+      { onD: { $lte: onDToHalfMonth } }
+    ]
   }
-  applySidCondition(sidProvQuery,obj)
-  applyCommonConditions(sidProvQuery,obj)
-
-  # 策略3: status + uaddr匹配（包含完整条件，包括tot_area）
-  uaddrStatusQuery = Object.assign {}, baseConditions,{
-    uaddr: obj.uaddr
+  if obj.origSid
+    sidQuery.sid = {$in:[obj.sid,obj.origSid]}
+  # NOTE: sid不一致时 需要判断status ptype等条件
+  noSidQuery = {
     status: obj.status
+    ptype: obj.ptype
   }
 
-  # 添加时间范围条件
-  timeRange = if obj.pho then timeRanges.base else timeRanges.noPhoto
-  uaddrStatusQuery.onD = { $gte: timeRange.from, $lte: timeRange.to }
+  if not obj.pho
+    onDMergeDateSpanForDiffSidAndNoPho = config?.serverBase?.onDMergeDateSpanForDiffSidAndNoPho or (7 *24 *
 3600000)
+    onDFromSevenDay = inputToDateNum(new Date(onDTime - onDMergeDateSpanForDiffSidAndNoPho))
+    onDToSevenDay = inputToDateNum(new Date(onDTime + onDMergeDateSpanForDiffSidAndNoPho))
+    noSidQuery['$and'] = [
+      { onD: { $gte: onDFromSevenDay } },
+      { onD: { $lte: onDToSevenDay } }
+    ]
+  else
+    noSidQuery['$and'] = [
+      { onD: { $gte: onDFromOneDay } },
+      { onD: { $lte: onDToOneDay } }
+    ]
 
-  # 应用通用条件（包括tot_area）
-  applyCommonConditions(uaddrStatusQuery,obj)
+  
+  if obj.src in ['TRB', 'BRE']
+    noSidQuery.src = { $ne: obj.src }
+  
+  # 没有pho的情况下再去掉source == TRB 的限制
+  if (obj.src is 'TRB') and (not obj.pho)
+    delete noSidQuery.src
 
-  # 特殊处理：Ontario下的RHB房源merge不考虑status
+  if obj.tot_area
+    noSidQuery.tot_area = obj.tot_area
+  if obj.lp
+    noSidQuery.lp = obj.lp
+  else
+    noSidQuery.lp = { $exists: false }
+
+  if obj.lpr
+    noSidQuery.lpr = obj.lpr
+  else
+    noSidQuery.lpr = { $exists: false }
+
+  if obj.unt?
+    noSidQuery.unt = '' + obj.unt
+  else
+    noSidQuery.unt = { $exists: false }
+  
   # NOTE:Ontario下的RHB房源merge不考虑status，查找是否存在treb的同一房源
   if (obj.src is 'RHB') and (obj.prov is 'ON')
-    delete uaddrStatusQuery.status
-    uaddrStatusQuery.src = 'TRB'
-
-  # RHB房源未找到时强制添加status条件
+    delete noSidQuery.status
+    noSidQuery.src = 'TRB'
+  
   # NOTE:如果Ontario下的RHB房源没有找到TRB房源merge，需要考虑status查找其它类型房源(DDF)
   if isRHBNotFound
-    delete uaddrStatusQuery.src
-    uaddrStatusQuery.status = obj.status
-
-  return {sidUaddrQuery, sidProvQuery, uaddrStatusQuery}
-
-
-###
-# @description 查找用于合并的旧房源记录
-# 采用三层查询策略，按优先级顺序查找匹配的房源：
-# 1. sid + uaddr 匹配（最高优先级）- 相同sid和地址的房源
-# 2. sid + prov 匹配（中等优先级）- 相同sid和省份的房源
-# 3. status + uaddr 匹配（最低优先级）- 相同状态和地址的房源
-#
-# @param {Object} Properties - MongoDB Properties集合对象
-# @param {Object} obj - 待查找的房源对象，必须包含以下字段：
-#   - _id: 房源ID
-#   - sid: 房源系统ID
-#   - uaddr: 统一地址
-#   - prov: 省份
-#   - status: 房源状态
-#   - onD: 上市日期
-#   - ptype: 房源类型
-#   - src: 数据源（如'RHB', 'TRB', 'DDF'等）
-# @param {Boolean} isRHBNotFound - 是否为RHB房源未找到的情况，默认false
-#   当为true时，会在uaddr+status查询中强制添加status条件
-# @param {Boolean} shouldExpandOnDRange - 是否扩大onD时间范围，默认false
-#   当为true时，会使用更大的时间范围进行查询
-# @returns {Object|undefined} 找到的房源对象，如果未找到则返回undefined
-###
-findOldPropForMerge = (Properties, obj, isRHBNotFound = false, shouldExpandOnDRange = false) ->
-  # 构建三种查询策略的查询条件
-  {sidUaddrQuery, sidProvQuery, uaddrStatusQuery} = buildQuery(obj, isRHBNotFound, shouldExpandOnDRange)
-
-  # 策略1: 优先查找相同sid和地址的房源（最精确匹配）
-  # 这种匹配通常用于同一房源在不同时间的更新
-  sidUaddrProp = await Properties.findOne(sidUaddrQuery)
-  return sidUaddrProp if sidUaddrProp
-
-  # 策略2: 查找相同sid和省份的房源（中等精确度）
-  # 用于处理地址可能有变化但sid相同的情况
-  sidProvProp = await Properties.findOne(sidProvQuery)
-  return sidProvProp if sidProvProp
-
-  # 策略3: 查找相同状态和地址的房源（最宽泛匹配）
-  # 用于处理sid可能不同但实际是同一房源的情况
-  uaddrStatusProp = await Properties.findOne(uaddrStatusQuery)
-  return uaddrStatusProp if uaddrStatusProp
-
-  return
-
-# 计算时间范围的辅助函数
-calculateTimeRanges = (onDTime, shouldExpandOnDRange, hasPho) ->
-  onDMergeDateSpanForDiffSid = config?.serverBase?.onDMergeDateSpanForDiffSid or (24 * 3600000)
-  onDMergeDateSpanForSameSid = config?.serverBase?.onDMergeDateSpanForSameSid or (15 * 24 * 3600000)
-  onDMergeDateSpanForDiffSidAndNoPho = config?.serverBase?.onDMergeDateSpanForDiffSidAndNoPho or (7 * 24 * 
3600000)
-
-  baseSpan = if shouldExpandOnDRange
-    config?.serverBase?.onDRangeExpansionValue or (5 * 24 * 3600000)
-  else
-    onDMergeDateSpanForDiffSid
-
-  {
-    halfMonth: {
-      from: inputToDateNum(new Date(onDTime - onDMergeDateSpanForSameSid))
-      to: inputToDateNum(new Date(onDTime + onDMergeDateSpanForSameSid))
-    }
-    base: {
-      from: inputToDateNum(new Date(onDTime - baseSpan))
-      to: inputToDateNum(new Date(onDTime + baseSpan))
-    }
-    noPhoto: {
-      from: inputToDateNum(new Date(onDTime - onDMergeDateSpanForDiffSidAndNoPho))
-      to: inputToDateNum(new Date(onDTime + onDMergeDateSpanForDiffSidAndNoPho))
-    }
-  }
+    delete noSidQuery.src
+    noSidQuery.status = obj.status
+  # debug.debug 'coming prop', obj
+  # query = {del:{$exists:false},} 不用$exists.
+  q['$or'] = [ sidQuery, noSidQuery ]
+  debug.debug JSON.stringify q
+  return q
 
 shouldResetMzh = (resultProp, incomingProp) ->
   if not resultProp.m_zh
@@ -1109,15 +1033,18 @@ module.exports.findDuplicatePropAndDeterminePriority = findDuplicatePropAndDeter
     oldProp = await Properties.findOne { _id: "TRB#{prop.sid}", prov:PROV_ONTARIO }
 
   if not oldProp
-    oldProp = await findOldPropForMerge(Properties,prop)
+    query = buildQuery prop
+    oldProp = await Properties.findOne query
 
   # RHB房源优先merge到treb房源,如果没有查询其它房源(DDF)进行merge
   if (prop.src is 'RHB') and (not oldProp)
-    oldProp = await findOldPropForMerge(Properties,prop,true)
+    query = buildQuery prop, true
+    oldProp = await Properties.findOne query
   
   if not oldProp
     isRHB = prop.src is 'RHB'
-    oldProp = await findOldPropForMerge(Properties,prop,isRHB,true)
+    query = buildQuery prop, isRHB, true
+    oldProp = await Properties.findOne query
 
   # not found the old prop, nothing todo
   if not oldProp
[mac@rocky src]$
```

---

现在让我查看其他重要的变更文件：


---

```bash
git diff main -- coffee4client/common.js
```

```
[mac@rocky src]$ git diff main -- coffee4client/common.js
diff --git a/src/coffee4client/common.js b/src/coffee4client/common.js
index 0f89dc63..f657f710 100644
--- a/src/coffee4client/common.js
+++ b/src/coffee4client/common.js
@@ -239,7 +239,8 @@ CONTACT_REALTOR_VALUES = CONTACT_REALTOR_VALUES = {
   BUY_SALE_PLACEHOLDER: 'Please describe your needs briefly',
   FUB_PROJECT_EMAIL: 'fubProjectEmail',
   ASSIGNMENT: 'assignment',
-  SHOW_NOTHING: 'showNothing'
+  SHOW_NOTHING: 'showNothing',
+  BOOK_VIEWING_SIMILAR: "I'd like to buy/sell something similar to"
 };
 WATCH_EVENTS = WATCH_EVENTS = ['New Sale', 'Price Changed', 'Sold', 'Delisted', 'New Rent', 'Leased'];
 WATCH_EVENTS_OBJ = WATCH_EVENTS_OBJ = {
[mac@rocky src]$
```

---

```bash
git diff main -- coffee4client/components/contactRealtor_mixins.js
```

```
[mac@rocky src]$ git diff main -- coffee4client/components/contactRealtor_mixins.js
diff --git a/src/coffee4client/components/contactRealtor_mixins.js b/src/coffee4client/components/contactRea
ltor_mixins.js
index 4532428b..e6ea4265 100644
--- a/src/coffee4client/components/contactRealtor_mixins.js
+++ b/src/coffee4client/components/contactRealtor_mixins.js
@@ -68,7 +68,7 @@ var mixins = {
     },
     getPropPostData(prop) {
       const propFields = ['saletp','saletp_en','_id','sid','addr','id','city','hideInfo',
-        'prov','city_en','prov_en','uid','lp','ptype2','tp_en','nmOrig','nm','nm_en'];
+        'prov','city_en','prov_en','uid','lp','ptype2','tp_en','nmOrig','nm','nm_en','saleTpTag_en'];
       var propData = {};
       propFields.forEach(field => {
         if (prop[field]) {
[mac@rocky src]$
```

---

```bash
git diff main -- libapp/contactRealtor.coffee
```

```
[mac@rocky src]$ git diff main -- libapp/contactRealtor.coffee
diff --git a/src/libapp/contactRealtor.coffee b/src/libapp/contactRealtor.coffee
index 2088ec98..4c38d9a2 100644
--- a/src/libapp/contactRealtor.coffee
+++ b/src/libapp/contactRealtor.coffee
@@ -557,7 +557,10 @@ exports.getContactRealtor = ({
     if page is CONTACT_REALTOR_VALUES.MLS and \
     (CONTACT_REALTOR_VALUES.FORM in action.UIType) and action.formDest and \
     (CONTACT_REALTOR_VALUES.UPGRADE_VIP not in action.formDest)
+      # 如果prop是sold或者delisted，则不显示showing date
       action.showShowingDate = true
+    if (page is CONTACT_REALTOR_VALUES.MLS) and (prop.saleTpTag_en in ['Sold','Delisted'])
+      action.showShowingDate = false
     if displayAgentInfo
       delete displayAgentInfo.roles if displayAgentInfo.roles
       ret.agentInfo = displayAgentInfo
@@ -594,7 +597,10 @@ getPlaceHolder = ({prop,page})->
   formPlaceHolder = ''
   if prop
     if page is CONTACT_REALTOR_VALUES.MLS
-      formPlaceHolder = "#{CONTACT_REALTOR_VALUES.BOOK_VIEWING_INFO}"
+      if prop.saleTpTag_en in ['Sold','Delisted']
+        formPlaceHolder = "#{CONTACT_REALTOR_VALUES.BOOK_VIEWING_SIMILAR}"
+      else
+        formPlaceHolder = "#{CONTACT_REALTOR_VALUES.BOOK_VIEWING_INFO}"
     else if (page in [CONTACT_REALTOR_VALUES.BUY_TRUSTEDASSIGNMENT,\
     CONTACT_REALTOR_VALUES.SALE_TRUSTEDASSIGNMENT])
       formPlaceHolder = ''
[mac@rocky src]$
```

---

```bash
git diff main -- libapp/impMappingCreaReso.coffee
```

```
[mac@rocky src]$ git diff main -- libapp/impMappingCreaReso.coffee
diff --git a/src/libapp/impMappingCreaReso.coffee b/src/libapp/impMappingCreaReso.coffee
index c2b83961..9d3bb50d 100644
--- a/src/libapp/impMappingCreaReso.coffee
+++ b/src/libapp/impMappingCreaReso.coffee
@@ -49,7 +49,7 @@ creaResoFieldMap =
   PublicRemarks: 'm'
   PhotosCount: 'pho'
   PhotosChangeTimestamp: 'phomt'
-  UnparsedAddress: 'origAddr'
+  UnparsedAddress: 'addr'
   PostalCode: 'zip'
   StateOrProvince: 'prov'
   StreetName: 'st'
[mac@rocky src]$
```

---

现在让我查看一些测试文件的变化：


---

```bash
git diff main -- unitTest/propertyImport/fixtures/vow/properties_expected.json | head -50
```

```
[mac@rocky src]$ git diff main -- unitTest/propertyImport/fixtures/vow/properties_expected.json | head -50
diff --git a/src/unitTest/propertyImport/fixtures/vow/properties_expected.json b/src/unitTest/propertyImport
/fixtures/vow/properties_expected.json
index cc8d687f..3753cc76 100644
--- a/src/unitTest/propertyImport/fixtures/vow/properties_expected.json
+++ b/src/unitTest/propertyImport/fixtures/vow/properties_expected.json
@@ -78202,7 +78202,7 @@
       "TransactionType": "For sale",
       "WaterFrontType": "Waterfront",
       "_mt": {
-        "$date": "2025-07-03T08:25:37.269+0000"
+        "$date": "2024-03-22T14:39:58.313Z"
       },
       "addr": "29 Queens E",
       "addr1": "#1011 - 29 QUEENS QUAY E",
@@ -78215,13 +78215,27 @@
         "tp": "C",
         "sz": 1
       },
+      "bndCmty": {
+        "_id": "CA:ON:TORONTO:WATERFRONT COMMUNITIES C08",
+        "nm": "Waterfront Communities C08"
+      },
+      "bndProv": {
+        "_id": "CA:ON",
+        "nm": "ON"
+      },
+      "bndRegion": {
+        "_id": "CA:ON:REGION TORONTO",
+        "nm": "Toronto"
+      },
+      "bndSubCity": {
+        "_id": "CA:ON:SUBCITY TORONTO AND EAST YORK",
+        "nm": "Toronto and East York"
+      },
       "bnds": [
         1026374,
         1024558,
-        1032200,
-        1032556,
-        1032314,
-        1032554
+        1029861,
+        1021159
       ],
       "br_plus": 1,
       "bthrms": 1,
@@ -78231,16 +78245,15 @@
       "city": "Toronto",
       "cmty": "Waterfront Communities C01",
       "cnty": "CA",
+      "cs16": "355350013.02",
[mac@rocky src]$
```

---

让我查看项目的命名规范和代码结构：


---

Read file: appweb/src/readMe.md

---

Read file: appweb/src/coffeelint.json

---

现在让我查看一些具体的代码文件来了解命名规范：


---

<details>
            <summary>Grep search for "function\s+\w+" • **51** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/bin/getValueFromToml.coffee` | L3 | `# Function to parse the TOML file and get the value for a key` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/inter.node/servicehub.coffee` | L746 | `if 'function' isnt typeof cb then throw new Error "Callback function is not provided."` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L70 | `# Function to sort and save config folder files` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L78 | `# Function to sort and save single config file` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L118 | `# recursive function to convert relative paths to absolute paths` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L172 | `# Function to replace ${ENV_PATH} in service files and write to systemdBuiltDir` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L233 | `# Helper function to write the systemd service file` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/bin/importHandler.coffee` | L40 | `# Function to handle @import statements` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/inter.node/internode.coffee` | L641 | `@fns = [] # function array` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/inter.node/internode.coffee` | L855 | `cfns[f.id] = ns.add_function req.client, f.name, f.id, f.opt` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/inter.node/internode.coffee` | L902 | `if not f then throw new Error "Function ID '#{id}' for server '#{req.client.addr}' is not registered yet."` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/inter.node/internode.coffee` | L903 | `if not (f instanceof FunctionBackend) then throw new Error "Server '#{req.client.addr}' sysid '#{id}' is not a function backend."` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/inter.node/internode.coffee` | L931 | `cfns = req.client.fns # client side function namespace` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/inter.node/internode.coffee` | L940 | `# perform a function or Got a callback answer for an server call (callback < 0)` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/inter.node/internode.coffee` | L957 | `err = new Error "Callback Return function is called multiple times. function: #{fnid} #{fn.inName}"` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/inter.node/internode.coffee` | L998 | `r_cancel: (sysid,buf)-> # TODO: timeout/cancel a function perform call` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/inter.node/internode.coffee` | L1083 | `throw new Error "the same name function has been registered already. #{name}"` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/inter.node/internode.coffee` | L1090 | `throw new Error "register can only accept function or object. Got #{typeof fn}"` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/inter.node/internode.coffee` | L1103 | `# TODO: remove function is not suppored yet. delete array[idx] is not suppored well in node.js` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/inter.node/internode.coffee` | L1104 | `throw new Error "Remove function is not supported yet."` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/inter.node/internode.coffee` | L1424 | `throw new Error "Not a function backend instance"` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/boundary/bndCmty.coffee` | L16 | `describe 'Properties Helper function tests',->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/user/0601_wechatAndthirdParty.coffee` | L9 | `describe 'user model function test: wechat & thirdParty',->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/claim/claim.coffee` | L805 | `# @function ClaimModel.buildUpdate` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/claim/claim.coffee` | L863 | `# @function ClaimModel.checkIfCanClaim 扩展测试` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/claim/claim.coffee` | L905 | `# @function ClaimModel.getClaimTokenKeys` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/showingCrm/showing.coffee` | L14 | `describe 'showing model function test', ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/showingCrm/showing.coffee` | L826 | `describe 'function test with cb', ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/showingCrm/showing.coffee` | L843 | `describe 'function test with param', ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/libapp/properties.coffee` | L10 | `describe 'Properties Helper function tests',->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/libapp/geoCodeHelper.coffee` | L3 | `describe 'GeoCodeHelper function tests',->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/libapp/impCommon.coffee` | L9 | `describe 'impCommon function tests',->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/libapp/user.coffee` | L5 | `describe 'User Helper function tests',->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/libapp/filterProperties.coffee` | L8 | `describe 'Properties Helper function tests',->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/libapp/common.coffee` | L19 | `describe 'Helpers function tests',->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/lib/helpers_object.coffee` | L8 | `describe 'Helpers function tests',->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/lib/helpers_date.coffee` | L5 | `describe 'Helpers function tests',->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/lib/sortHelper.coffee` | L4 | `describe 'Helpers function tests',->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/lib/priorityQueue.coffee` | L120 | `# NOTE: this executeFn should be io function only` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/lib/priorityQueue.coffee` | L240 | `# NOTE: this executeFn should be io function only` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/lib/helpers_string.coffee` | L7 | `describe 'Helpers function tests',->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/lib/helpers_number.coffee` | L4 | `describe 'helpers_number function tests',->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/lib/helpers_function.coffee` | L7 | `describe 'Helpers function tests',->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/geocoder/newGeocoder.coffee` | L1019 | `# describe 'test geoCodeOneOrUpsert function added to geo conflict',->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/geocoder/newGeocoder.coffee` | L1627 | `describe 'Test function of isSimilarAddrOfPropAndGeo', ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/abtest/test.coffee` | L95 | `describe 'function test ABTestModel.update',->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/showservices.coffee` | L21 | `checkFunction n,doone` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/model/020_geoCoderLocal.coffee` | L1270 | `# NOTE: this await changed geocoding function to async, UNEXPECTED error wont be caught inside cb functions or cb err` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/model/properties.coffee` | L1004 | `#  [fileName] function invoked in this file` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/model/actionCode.coffee` | L65 | `err = "No function action #{action.act}"` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/creaReso/creaResoDownload.coffee` | L509 | `# Helper function to merge office data` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/creaReso/creaResoDownload.coffee` | L521 | `# Helper function to merge member data` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/bcreReso/bcreResoDownload.coffee` | L501 | `# Helper function to merge office data` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/bcreReso/bcreResoDownload.coffee` | L513 | `# Helper function to merge member data` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadv2IDtest.coffee` | L559 | `# main loop can call this function without await, and continue to next loop` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadv2IDtest.coffee` | L560 | `# This function will have its own task list, and do its job in its own loop.` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L623 | `readableValue = parseWithFunction parseFloat,value` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L628 | `readableValue = parseWithFunction parseFloat,value` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L630 | `readableValue = parseWithFunction parseInt,value` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/trebDownloadV2.coffee` | L590 | `# main loop can call this function without await, and continue to next loop` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/trebDownloadV2.coffee` | L591 | `# This function will have its own task list, and do its job in its own loop.` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/trebDownload.coffee` | L521 | `# main loop can call this function without await, and continue to next loop` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/trebDownload.coffee` | L522 | `# This function will have its own task list, and do its job in its own loop.` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/retsPhotoDownloadAsync.coffee` | L284 | `# NOTE: this executeFn should be io function only` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/retsPhotoDownload.coffee` | L247 | `# reset counter and timer after sleep, because this function is concurrent,` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/oreb/orebDownload.coffee` | L44 | `2. with Property-Listing records: if the photos are updated, call the async function to download photos.` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/oreb/orebDownload.coffee` | L63 | `4. The downloadPhotos function will loop through the photo queue and download photos.` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/oreb/orebDownload.coffee` | L255 | `# Use this function to limit the frequency of requests to the RETS server,` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/oreb/orebDownload.coffee` | L302 | `makeResourceActionFunction params,_resource,_className` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/oreb/orebDownload.coffee` | L1140 | `readableValue = parseWithFunction parseFloat,value` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/oreb/orebDownload.coffee` | L1145 | `readableValue = parseWithFunction parseFloat,value` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/oreb/orebDownload.coffee` | L1147 | `readableValue = parseWithFunction parseInt,value` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/oreb/orebDownload.coffee` | L1237 | `makeResourceActionFunction params,m.Resource,obj.ClassName` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/creb/crebDownload.coffee` | L44 | `2. with Property-Listing records: if the photos are updated, call the async function to download photos.` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/creb/crebDownload.coffee` | L63 | `4. The downloadPhotos function will loop through the photo queue and download photos.` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/creb/crebDownload.coffee` | L259 | `# Use this function to limit the frequency of requests to the RETS server,` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/creb/crebDownload.coffee` | L306 | `makeResourceActionFunction params,_resource,_className` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/creb/crebDownload.coffee` | L1275 | `readableValue = parseWithFunction parseFloat,value` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/creb/crebDownload.coffee` | L1280 | `readableValue = parseWithFunction parseFloat,value` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/creb/crebDownload.coffee` | L1282 | `readableValue = parseWithFunction parseInt,value` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/creb/crebDownload.coffee` | L1369 | `makeResourceActionFunction params,m.Resource,obj.ClassName` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/creaDownload.coffee` | L677 | `' function redirect() { \r\n' +` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/bcreDownload.coffee` | L792 | `readableValue = parseWithFunction parseFloat,value` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/bcreDownload.coffee` | L797 | `readableValue = parseWithFunction parseFloat,value` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/bcreDownload.coffee` | L799 | `readableValue = parseWithFunction parseInt,value` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/ConvertToReadable.coffee` | L31 | `readableValue = parseWithFunction parseFloat,value` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/ConvertToReadable.coffee` | L36 | `readableValue = parseWithFunction parseFloat,value` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/mlsImport/ConvertToReadable.coffee` | L38 | `readableValue = parseWithFunction parseInt,value` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/libapp/trebImport.coffee` | L129 | `# function 1` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/libapp/contactRealtor.coffee` | L572 | `#Uncaught Exception:TypeError: this.locale is not a function when use l10n` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/libapp/impMappingEdm.coffee` | L140 | `# General function to process a field from list to string` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/libapp/impMappingCar.coffee` | L145 | `# General function to process a field from list to string` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/libapp/impMappingRESO.coffee` | L171 | `# General function to process a field from list to string` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/libapp/watchHelper.coffee` | L191 | `onUpdateFunction obj, (err)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/libapp/watchHelper.coffee` | L242 | `return cb('onError function Required')` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/libapp/watchHelper.coffee` | L244 | `return cb('process function Required')` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/libapp/stat_helper.coffee` | L995 | `# so this function is not used` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/libapp/pushNotifyMsg.coffee` | L80 | `# TODO: use a unified DRY function to compose and translate address` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/libapp/impMappingTrebManual.coffee` | L289 | `#direction.p is function to property.` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/libapp/processHelper.coffee` | L48 | `returns async function to update Process status` |

</details>

---

现在让我查看一些具体的函数命名模式：


---

<details>
            <summary>Grep search for "^[a-z][a-zA-Z0-9]*\s*=\s*\(" • **30** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/wechat.coffee` | L23 | `httpsGet = (url,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/showservices.coffee` | L4 | `checkFunction = (name,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/scheduler.coffee` | L30 | `doneTasks = (p,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/scheduler.coffee` | L39 | `removeTasks = (p,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/scheduler.coffee` | L46 | `getTasks = (uid,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/scheduler.coffee` | L52 | `updateTask = (task,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/scheduler.coffee` | L130 | `callAction = (act,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/scheduler.coffee` | L143 | `performTask = (task,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/simple_mailer.coffee` | L71 | `replaceParam = (mail,tpl)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/simple_mailer.coffee` | L86 | `applyParam = (p,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/prodSyncL10nDBs.coffee` | L37 | `findError = (err)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/prodSyncL10nDBs.coffee` | L42 | `getColls = (cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/prodSyncL10nDBs.coffee` | L51 | `get2Colls = (cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/prodSyncL10nDBs.coffee` | L71 | `whichSide = (r0,r1)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/prodSyncL10nDBs.coffee` | L118 | `compare = (r0,r1)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/prodSyncL10nDBs.coffee` | L168 | `update = (colls,records,side,promptit,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/pageDownloader.coffee` | L24 | `filterHTML = (src, body, html, cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/pageDownloader.coffee` | L93 | `requestURL = (opt,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/mandrill.coffee` | L8 | `getName = (c)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/mandrill.coffee` | L13 | `getImages = (images,imgs,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/mandrill.coffee` | L28 | `prepareMessage = (temp,from,contacts,opts = {},cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/file_jiajia.coffee` | L78 | `unzip = (fr,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/file.coffee` | L63 | `unzip = (fr,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L47 | `sortObject = (obj) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L59 | `sortContentWithoutImports = (contentWithoutImports) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L71 | `sortAndSaveConfigFolderFiles = (configDir) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L79 | `sortAndSaveConfigFile = (inputFile, outputFile) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L99 | `buildAndSaveConfigs = (configs, builtDir, envFile, envTestFile, envTestESFile) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L109 | `replaceEnvPath = (value)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L119 | `convertToAbsolutePath = (config, basePath = process.cwd()) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L149 | `mergeEnvOverrides = (file, config, envFile, envTestFile, envTestESFile) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L173 | `replaceEnvPathInServiceFiles = (dir, envPath, outputDir) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L196 | `generateSendEmailSystemdFile = (systemdTplDir,systemdBuiltDir,etcSystemdBuiltDir,alertEmail) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L213 | `getDefaultFailureSendEmail = (appwebFile)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L218 | `getDefaultSrcPath = (envFile)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L223 | `getDefaultBaseAndLogPath = (envFile)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L234 | `writeSystemdServiceFile = (envList, execStart, tValue, nValue, user, group, wantedBy, cronTimer, systemdTplDir,systemdBuiltDir,etcSystemdBuiltDir,restart_service) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/rmconfig/config.coffee` | L293 | `getExecutablePath = (command) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L52 | `logError = (e)-> console.error e` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L53 | `errorNotify = (msg,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L75 | `upTo58 = (tp,action,fnData)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L114 | `upToUhouzz = (tp,action,fnData)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L183 | `houseType = (act,listing)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L364 | `p58ListingCity = (act,listing)-> p58_getCity listing.city` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L365 | `p58RealtorCities = (act,obj,objP)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L379 | `p58ListingUpAfter = (act,ret,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L394 | `uhouzzFacilities = (act,l)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L428 | `dateFormat = (format,k)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L435 | `strTemplate = (vars...)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L447 | `strLimit = (k,length)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L452 | `fnTitle = (act,obj,objP = {})->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L458 | `userPics = (act,userProf,user)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L466 | `listingPics = (act,listing)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L490 | `noArray = (nm)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L588 | `convertUser = (act, userProf, users)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L609 | `convertListing = (act, listing)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L621 | `promoRealtors = (act,list,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L666 | `promoListing = (act,list,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L686 | `promoAction = (act,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L715 | `promo23rdParty = (cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L773 | `expiryAction = (act,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L806 | `processExpiryMLS = (cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L862 | `revokeListings = (ids,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L872 | `checkQueued = (fromHandler)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/services/promoTo3pty.coffee` | L881 | `promo23rdPartyQueueRequest = (param,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/esProperty/useEsSearch.coffee` | L21 | `sleep = (ms) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/esProperty/useEsSearch.coffee` | L24 | `handleExistPropertyIndex = ()->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/esProperty/useEsSearch.coffee` | L37 | `migrateMongo2ES = (cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/taskPush/task.coffee` | L13 | `getTaskCountByStatus = (status) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/watch/watchHelper.coffee` | L56 | `sleep = (ms) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/watch/watchHelper.coffee` | L78 | `EXIT = ()->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/watch/watchHelper.coffee` | L82 | `onWatchError = (error)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/watch/watchHelper.coffee` | L118 | `updateProcessStatusForWatch = (startTs)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/watch/watchHelper.coffee` | L129 | `onChange = (changedObj,watchedColl, cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/watch/watchHelper.coffee` | L146 | `onTokenUpdate = ({token,tokenClusterTs,tokenChangeTs,end})->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/watch/watchHelper.coffee` | L154 | `startWatch = ({token},cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/watch/watchHelper.coffee` | L181 | `changeDB = ()->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/userEmail/suspend.coffee` | L784 | `check = (test)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/user/0101_registerAndLogin.coffee` | L15 | `getEmails = (email)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/universityimport/universityGeocoding.coffee` | L24 | `perror = (err, code)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/universityimport/universityAndCollegeimport.coffee` | L29 | `findCollegeInArray = (col, arr)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/soldLoss/soldLoss.coffee` | L13 | `sleep = (ms) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/soldLoss/soldLoss.coffee` | L16 | `searchAsync = (body)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/soldLoss/soldLoss.coffee` | L30 | `initEsAsync = ()->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/propertyImport/02_propertyImport.coffee` | L35 | `importProp = ({srcType,prop},cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/propertyImport/03_propertyImportMerge.coffee` | L43 | `importProp = ({srcType,prop},cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/propertyImport/03_propertyImportMerge.coffee` | L74 | `isEqualASIDs = (aSIDsA = [],aSIDsB = [])->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/propertyImport/01_propertyImport.coffee` | L57 | `importProp = ({srcType,prop},cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/mlsImport/checkTrebManual.coffee` | L1000 | `sleep = (ms) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/mlsImport/checkTrebManual.coffee` | L1003 | `getEmails = ({email},cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/libapp/propListSort.coffee` | L7 | `calcAddressScore = (addr,prop)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/libapp/propListSort.coffee` | L24 | `calcESScore = (addr,prop)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/libapp/propListSort.coffee` | L30 | `sortProps = (addr,props)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/libapp/impMappingDdf.coffee` | L7 | `formatNum= (num)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/lib/kafka.coffee` | L13 | `sleep = (ms) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/lib/rateLimiter.coffee` | L20 | `alertHandler = (msg)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/geoCache/geoCache.coffee` | L12 | `getCollCountByUaddr = (uaddr,propId)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/edm/edmBatch.coffee` | L25 | `logEmails = (emails)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/edm/edmBatch.coffee` | L35 | `getEmails = ({email},cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/unitTest/edm/edmBatch.coffee` | L108 | `sleep = (ms) ->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/adRelated/banner.coffee` | L25 | `getUserCity = (req, user, cities, idx)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/adRelated/banner.coffee` | L33 | `isClient = (req)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/adRelated/banner.coffee` | L41 | `filterBanners = (req, user={}, l, vcinc,mode)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/adRelated/banner.coffee` | L79 | `injectProjectUserAvts = (list,cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/adRelated/banner.coffee` | L137 | `reloadADs = (cb)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/adRelated/banner.coffee` | L185 | `getBanners = (lang,tp,noCount)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/adRelated/banner.coffee` | L199 | `bannerClicked = ({id,isCip,posn})->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/adRelated/banner.coffee` | L210 | `bannerOpened = (id)->` |
| `vscode-remote://ssh-remote%2B100.114.231.55/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/adRelated/banner.coffee` | L310 | `constructLang = (req)->` |

</details>

---

基于我对代码的分析，现在我来提供详细的代码review报告：

## 代码Review报告：forminput_soldDesc分支与main分支差异分析

### 1. 命名规范检查

#### ✅ 符合规范的方面：
- **函数命名**：使用camelCase命名法，如`buildQuery`、`findOldPropForMerge`、`applySidCondition`等
- **变量命名**：使用camelCase，如`onDMergeDateSpanForDiffSid`、`shouldExpandOnDRange`等
- **常量命名**：使用UPPER_SNAKE_CASE，如`CONTACT_REALTOR_VALUES`、`BOOK_VIEWING_SIMILAR`等

#### ⚠️ 需要改进的方面：
- **函数命名不一致**：在`saveToMaster.coffee`中，有些函数使用了描述性命名（如`buildQuery`），但有些函数命名过于简单（如`q`）
- **变量命名**：在查询构建中使用了简短的变量名`q`，建议使用更具描述性的名称如`query`

### 2. 重复代码和可抽象部分

#### 🔍 发现的重复代码：

1. **时间范围计算逻辑重复**：
```coffeescript
# 在buildQuery函数中重复出现的时间计算
onDFromOneDay = inputToDateNum(new Date(onDTime - onDMergeDateSpanForDiffSid))
onDToOneDay = inputToDateNum(new Date(onDTime + onDMergeDateSpanForDiffSid))
```

2. **查询条件构建重复**：
```coffeescript
# 多个地方都有类似的查询条件设置
if obj.lp
  noSidQuery.lp = obj.lp
else
  noSidQuery.lp = { $exists: false }
```

#### 💡 建议抽象为通用函数：

1. **时间范围计算函数**：
```coffeescript
/**
 * 计算时间范围
 * @param {number} baseTime - 基准时间戳
 * @param {number} spanMs - 时间跨度（毫秒）
 * @returns {Object} 包含from和to的时间范围对象
 */
calculateTimeRange = (baseTime, spanMs) ->
  from: inputToDateNum(new Date(baseTime - spanMs))
  to: inputToDateNum(new Date(baseTime + spanMs))
```

2. **查询条件设置函数**：
```coffeescript
/**
 * 设置查询条件，如果值存在则设置，否则设置为不存在
 * @param {Object} query - 查询对象
 * @param {string} field - 字段名
 * @param {*} value - 字段值
 */
setQueryCondition = (query, field, value) ->
  if value?
    query[field] = value
  else
    query[field] = { $exists: false }
```

### 3. 潜在性能问题和Bug

#### 🚨 性能问题：

1. **查询复杂度增加**：
   - 新的`buildQuery`函数构建了复杂的`$or`查询，可能导致查询性能下降
   - 建议为`uaddr`字段添加索引以提升查询性能

2. **时间计算重复**：
   - 每次调用`buildQuery`都会重新计算时间范围，建议缓存计算结果

#### 🐛 潜在Bug：

1. **字段映射错误**：
```coffeescript
# 在impMappingCreaReso.coffee中
UnparsedAddress: 'addr'  # 原来是'origAddr'，可能影响数据完整性
```

2. **条件判断逻辑**：
```coffeescript
# 在contactRealtor.coffee中
if (page is CONTACT_REALTOR_VALUES.MLS) and (prop.saleTpTag_en in ['Sold','Delisted'])
  action.showShowingDate = false
```
这个逻辑可能导致在某些情况下`showShowingDate`未被正确设置。

### 4. 具体优化建议

#### 📝 代码结构优化：

1. **重构buildQuery函数**：
```coffeescript
/**
 * 构建房源merge查询条件
 * @param {Object} obj - 房源对象
 * @param {Boolean} isRHBNotFound - 是否为RHB房源未找到的情况
 * @param {Boolean} shouldExpandOnDRange - 是否扩大onD时间范围
 * @returns {Object} MongoDB查询条件对象
 */
module.exports.buildQuery = buildQuery = (obj, isRHBNotFound = false, shouldExpandOnDRange = false) ->
  # 使用配置常量
  config = getConfig()
  timeRanges = calculateTimeRanges(obj.onD, shouldExpandOnDRange, obj.pho)
  
  # 构建基础查询条件
  baseQuery = buildBaseQuery(obj)
  
  # 构建sid查询条件
  sidQuery = buildSidQuery(obj, timeRanges.halfMonth)
  
  # 构建非sid查询条件
  noSidQuery = buildNoSidQuery(obj, timeRanges, isRHBNotFound)
  
  # 合并查询条件
  baseQuery['$or'] = [sidQuery, noSidQuery]
  
  return baseQuery
```

2. **添加辅助函数**：
```coffeescript
/**
 * 获取配置信息
 * @returns {Object} 配置对象
 */
getConfig = () ->
  return config?.serverBase or {}

/**
 * 计算时间范围
 * @param {string} onD - 上市日期
 * @param {Boolean} shouldExpand - 是否扩大范围
 * @param {Boolean} hasPhoto - 是否有图片
 * @returns {Object} 时间范围对象
 */
calculateTimeRanges = (onD, shouldExpand, hasPhoto) ->
  onDTime = inputToDate(onD).getTime()
  config = getConfig()
  
  return {
    halfMonth: calculateTimeRange(onDTime, config.onDMergeDateSpanForSameSid or (15 * 24 * 3600000))
    base: calculateTimeRange(onDTime, if shouldExpand then (config.onDRangeExpansionValue or (5 * 24 * 3600000)) else (config.onDMergeDateSpanForDiffSid or (24 * 3600000)))
    noPhoto: calculateTimeRange(onDTime, config.onDMergeDateSpanForDiffSidAndNoPho or (7 * 24 * 3600000))
  }
```

#### �� 功能改进建议：

1. **添加错误处理**：
```coffeescript
/**
 * 安全的查询构建函数
 * @param {Object} obj - 房源对象
 * @returns {Object} 查询条件对象
 */
safeBuildQuery = (obj) ->
  try
    return buildQuery(obj)
  catch error
    debug.error 'Error building query:', error
    # 返回基础查询条件
    return {
      _id: { $ne: obj._id }
      merged: null
      del: null
    }
```

2. **添加参数验证**：
```coffeescript
/**
 * 验证房源对象参数
 * @param {Object} obj - 房源对象
 * @throws {Error} 参数验证错误
 */
validatePropObject = (obj) ->
  if not obj?._id
    throw new Error 'Property object must have _id field'
  if not obj?.uaddr
    throw new Error 'Property object must have uaddr field'
  if not obj?.onD
    throw new Error 'Property object must have onD field'
```

#### �� 测试建议：

1. **添加单元测试**：
```coffeescript
describe 'buildQuery function tests', ->
  it 'should build correct query for same sid and uaddr', ->
    prop = {
      _id: 'test123'
      sid: 'SID001'
      uaddr: '123 Main St'
      onD: '20240101'
      ptype: 'Residential'
    }
    query = buildQuery(prop)
    expect(query._id.$ne).toBe('test123')
    expect(query['$or']).toHaveLength(2)
```

2. **添加性能测试**：
```coffeescript
describe 'buildQuery performance tests', ->
  it 'should complete within reasonable time', ->
    startTime = Date.now()
    for i in [1..1000]
      buildQuery(testProp)
    endTime = Date.now()
    expect(endTime - startTime).toBeLessThan(1000) # 1秒内完成1000次查询构建
```

### 总结

这次代码变更主要涉及房源合并逻辑的优化，整体代码质量良好，但存在一些可以改进的地方：

1. **命名规范**：基本符合项目规范，但建议统一函数命名风格
2. **代码重复**：存在一些可抽象的时间计算和查询条件设置逻辑
3. **性能优化**：建议添加索引和缓存机制
4. **错误处理**：建议增加参数验证和异常处理
5. **测试覆盖**：建议添加更多单元测试和性能测试

建议在合并前进行以上优化，以提高代码的可维护性和性能。

---


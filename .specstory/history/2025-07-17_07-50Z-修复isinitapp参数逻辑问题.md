<!-- Generated by SpecStory -->

# 修复isInitApp参数逻辑问题 (2025-07-17 07:50Z)

_**User**_

 整理这部分逻辑，看是否满足我的想法，isInitApp有两种情况下没有值，1。旧版本，2.新下载的app。旧版本之前已经init过了，我需要将 isInitApp参数设置为 1并通过 self.addToStorage('initApp',status)保存到storage中，新版本强制走完init程序。现在出现了一个问题，第二种情况下，我通过 setLang 设置语言之后，isInitApp 会判断为 true，但实际上初始init还没有结束。这个问题怎么修复？

---

_**Assistant**_

你的需求和现有逻辑分析如下：

### 你的需求
- **isInitApp 没有值的两种情况**：
  1. 旧版本：用户之前已经初始化过（cookie 里有 locale），直接设置 isInitApp=1 并保存到 storage。
  2. 新下载的 app：没有初始化过，必须强制走完初始化流程。

- **问题**  
  在新下载的 app（即 isInitApp==null 且没有 locale cookie），你希望用户必须完整走完初始化流程。但现在如果用户在流程中途（比如只设置了语言 setLang）后，isInitApp 会被判断为 true，导致流程提前结束。

---

## 现有代码逻辑梳理

```js
if(isInitApp == null){
  if(self.readCookie('locale') ){
    // 之前已经init过，添加参数
    status = 1
    self.addToStorage('notificationPermission',new Date())
  }else if(self.isAndroid){
    // 没有init过的安卓版本添加notification已验证，因为安卓默认会给通知权限
    self.addToStorage('notificationPermission',new Date())
  }
  self.addToStorage('initApp',status)
}else{
  status = isInitApp;
}
self.isInitApp = status == 1?false:true;
```

- 旧版本（有 locale cookie）：status=1，isInitApp=false，直接跳过初始化。
- 新版本（无 locale cookie）：status=0，isInitApp=true，必须走完初始化流程。

**问题点**  
在初始化流程未完成时（比如只设置了语言），如果 setLang 里有写 localStorage.lang，可能会导致后续判断 isInitApp 为 true，但实际上流程未走完。

---

## 你的问题本质

- **setLang 只是设置了语言，但没有完成整个初始化流程。**
- 但 isInitApp 的判断只依赖于 storage 里的 status 字段（0/1），而不是流程是否真正走完。
- 只要 storage 里 status 不是 1，isInitApp 就会被判为 true，流程会继续。
- 但如果 setLang 或其他操作提前把 status 设成 1，就会导致流程提前结束。

---

## 解决思路

1. **确保只有在整个初始化流程完成后，才把 `initApp` 设为 1。**
2. **setLang 只负责设置语言，不应该影响 `initApp` 的状态。**
3. **流程最后一步（done 方法）才调用 `self.addToStorage('initApp', 1)`。**

### 具体修复建议

- 检查 setLang 及其它流程中，是否有提前设置 `initApp=1` 的地方，**只允许在 done 方法里设置**。
- 在 showCover 的逻辑里，只有检测到 storage 里 `initApp==1`，才认为初始化完成。
- 其它地方（如 setLang）不要动 `initApp`。

---

## 代码修正建议

**1. 检查 setLang 方法，确保没有设置 initApp。**

```js
setLang (lang) {
  var self = this;
  localStorage.lang = lang;
  this.locale = lang;
  self.addToStorage('curStep',self.curStep)
  setLoaderVisibility('block');
  var d = '/1.5/index?src=setting';
  var url = "/1.5/settings/lang?l=" + lang + "&d=";
  url = url+encodeURIComponent(d);
  window.location = url;
}
```
**→ 这里没有设置 initApp，没问题。**

**2. done 方法才设置 initApp=1**

```js
done () {
  // ...
  if (self.isInitApp) {
    // 首次初始化，等待 initApp 写入成功后再跳转
    self.addToStorage('initApp', 1, function() {
      setTimeout(() => {
        self.handleRedirect(userLang, true);
      }, 500);
    });
  } else {
    setTimeout(() => {
      self.handleRedirect(userLang, false);
    }, 500);
  }
}
```
**→ 这里是唯一设置 initApp=1 的地方，没问题。**

**3. showCover 逻辑没问题，只要 status==1 才认为初始化完成。**

---

## 总结

- 你的代码整体逻辑是对的，**只要保证 setLang 及其它流程中不提前设置 initApp=1**，就不会出现你说的问题。
- **只有 done 方法里设置 initApp=1，才能确保流程完整走完。**
- 如果你发现 setLang 或其它地方有设置 initApp=1，删掉即可。

---

## 代码注释建议

你可以在 done 方法和 setLang 方法加上注释，明确说明：

```js
/**
 * 只有在初始化流程全部完成时，才设置 initApp=1
 * 千万不要在 setLang 或其它流程中提前设置
 */
done () {
  // ...如上
}

/**
 * 设置语言，不影响 initApp 状态
 */
setLang (lang) {
  // ...如上
}
```

---

## 结论

**只要保证 `initApp=1` 只在 done 方法里设置，流程就不会提前结束。setLang 只设置语言，不要动 initApp。这样就能满足你的需求。**

如需进一步代码检查或有其它疑问，请补充说明！

---


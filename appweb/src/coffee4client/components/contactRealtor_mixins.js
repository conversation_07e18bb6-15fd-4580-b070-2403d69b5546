var mixins = {
  created :  function () {
  },
  data() {
    return {
      contactRealtorInfo:null,
      formStatus:{}
    }
  },
  methods : {
    setSignupModalHeight() {
      var backdrop = document.getElementById('backdrop');
      if (backdrop) {
        var {height} = document.getElementById('SignupModal').getBoundingClientRect();
        backdrop.style.backgroundColor = 'transparent';
        backdrop.style.display = 'block';
        backdrop.style.height = height+'px';
        backdrop.style.top = 'auto';
      }
      //set signupModal height to let background to be scrollable
      return
      setTimeout(()=>{
        var signUpFormContainerHeight = document.querySelector('#signUpFormContainer').offsetHeight;
        var signUpModal = document.querySelector('#SignupModal');
        signUpModal.style.height = signUpFormContainerHeight+'px';
        signUpModal.style.minHeight = signUpFormContainerHeight+'px';
      },1000);
    },
    getFormInputProps(eml) {
      if (!eml) {
        return;
      }
      var body = {eml};
      var self = this;
      fetchData('/1.5/contactRealtor/getContactRealtorUserStatus',{body},(err,ret)=>{
        var errMsg = err || ret.e;
        if (err || ret.e) {
          RMSrv.dialogAlert(errMsg.toString());
        }
        if (ret.ok) {
          self.formStatus = ret;
        }
      });
    },
    getContactRealtorPage(prop) {
      var page = '';
      if(vars && vars.formPage && vars.formPage !== 'undefined' && FORM_PAGE.includes(vars.formPage)) {
        return vars.formPage;
      }
      if (prop.src != 'RM') {
        page = CONTACT_REALTOR_VALUES.MLS;
      } else {
        if (prop.ltp == 'rent' && !prop.cmstn) {
          page = CONTACT_REALTOR_VALUES.LANDLORD_RENT;
        } else if(prop.marketRmProp){
          page = CONTACT_REALTOR_VALUES.TRUSTED_ASSIGNMENT;
        } else if(prop.ltp == 'assignment'){ // 放在prop.marketRmProp判断之后，因为真楼花的ltp也是assignment
          page = CONTACT_REALTOR_VALUES.ASSIGNMENT;
        } else {
          page = CONTACT_REALTOR_VALUES.RM_LISTING;
        }
      }
      if (prop.spuids) {
        page = CONTACT_REALTOR_VALUES.PROJECT
        // page = prop.spuids.length > 0 ? CONTACT_REALTOR_VALUES.PROJECT_HAS_SPONSOR : CONTACT_REALTOR_VALUES.PROJECT;
      }
      return page;
    },
    getPropPostData(prop) {
      const propFields = ['saletp','saletp_en','_id','sid','addr','id','city','hideInfo',
        'prov','city_en','prov_en','uid','lp','ptype2','tp_en','nmOrig','nm','nm_en','saleTpTag_en'];
      var propData = {};
      propFields.forEach(field => {
        if (prop[field]) {
          if(field == 'addr' && prop.unt){
            propData[field] = prop.unt+' '+prop[field];
          } else {
            propData[field] = prop[field];
          }
        }
      });
      if (vars.mlsid) {
        //get id sid from evaluation result page
        propData._id = vars.mlsid;
        propData.sid = vars.mlsid.substr(3);
      }
      return propData;
    },
    getContactRealtor({page,prop,hasWechat,src}) {
      if (!page || !FORM_PAGE.includes(page)) {
        page = this.getContactRealtorPage(prop);
      }
      // 传入的page不同时需要重新获取数据（主要是针对真楼花页面有3个按钮需要获取3中不同的form）
      if (this.contactRealtorInfo && this.contactRealtorInfo.page == page) {
        this.handleContactRealtorAction();
        return;
      }
      var self = this;
      var body = {prop:this.getPropPostData(prop),hasWechat,src,page};
      fetchData('/1.5/contactRealtor',{body},(err,res)=>{
        if (res && res.ok && res.ret) {
          self.contactRealtorInfo = res.ret;
          self.contactRealtorInfo.page = page;
          self.handleContactRealtorAction();
        } else if (res && res.ok == 0) {
          if (res.needLogin) {
            RMSrv.closeAndRedirectRoot(this.appendDomain('/1.5/user/login#index'));
          }
        }
        if (err) {
          RMSrv.dialogAlert(err.toString());
        }
      });
    },
    contactRealMaster(formInfo={}){
      this.$parent.forceShowSignup = true;
      // overwrite session user fn,ln without emoji
      for (let i of ['fn','ln','nm']){
        if(formInfo[i] != null){
          this.$parent.userForm[i] = formInfo[i]
        }
      }
      // session user fn,ln is emoji, thus nm = ''
      // here reset fn,ln
      if(formInfo.nm == ''){
        this.$parent.userForm.fn = ''
        this.$parent.userForm.ln = ''
      }
      this.$parent.userForm.m = formInfo.formPlaceHolder;
      this.$parent.userForm.page = formInfo.page;
      this.$parent.userForm.src = formInfo.src;
      this.$parent.signupTitle = formInfo.signUpTitle;
      this.$parent.userForm.url = this.$parent.fullUrl || document.URL;
      this.$parent.userForm.showShowingDate = formInfo.showShowingDate;
      this.$parent.userForm.noForm = formInfo.noForm;
      this.$parent.userForm.submitTxt = formInfo.submitTxt;
      this.$parent.userForm.submitLink = formInfo.submitLink;
      this.$parent.userForm.msg = formInfo.msg;
      this.$parent.userForm.buyOrSalePlaceHolder = formInfo.buyOrSalePlaceHolder;
      setTimeout(()=>{
        toggleModal('SignupModal','open');
        this.setSignupModalHeight();
      },100)
    },
    handleContactRealtorAction() {
      var ret = this.contactRealtorInfo;
      var self = this;
      var UIType = ret.action.UIType;
      var hasForm = UIType.indexOf('form') > -1;
      var hasAgentCard = UIType.indexOf('agentCard') > -1;
      var hasMlsAgentPanel = UIType.indexOf('mlsAgentPanel') > -1;
      // var showRealtorYellowPage = UIType.includes('showRealtorYellowPage');
      var formTitle = ret.action.title_tr || ret.action.title;
      if (hasAgentCard) {
        if (ret.agentInfo) {
          self.prop.adrltr = ret.agentInfo;
          if (ret.mailBody) {
            self.prop.adrltr.mailBody = ret.mailBody;
            self.prop.adrltr.mailSubject = ret.mailSubject;
          }
        }
      }
      if (hasForm) {
        var formInfo = ret.formInfo;
        formInfo.signUpTitle = formTitle;
        if(ret.page == CONTACT_REALTOR_VALUES.BUY_TRUSTEDASSIGNMENT || ret.page == CONTACT_REALTOR_VALUES.SALE_TRUSTEDASSIGNMENT){
          formInfo.page = ret.page;
        } else {
          formInfo.page = this.getContactRealtorPage(self.prop);
        }
        formInfo.src = CONTACT_REALTOR_VALUES.APP;
        formInfo.showShowingDate = ret.action.showShowingDate;
        self.contactRealMaster(formInfo);
      }
      else if (hasMlsAgentPanel) {
        self.showBrkgInfo({title:formTitle});
      }
      else if (hasAgentCard) {
        var agentCardOpt = {cardTitle:formTitle};
        if (ret.mailBody) {
          agentCardOpt.mailBody = ret.mailBody;
          agentCardOpt.mailSubject = ret.mailSubject;
        }
        self.showRMBrkgInfo(agentCardOpt);
      }
      // else if (showRealtorYellowPage) {
      //   self.findARealtor();
      // }
    }
  }
}
export default mixins;

// Generated by CoffeeScript 2.7.0
// NOTE: common js for front end and backend
// TODO: redesign schema for search
var CONTACT_REALTOR_VALUES, FORM_PAGE, I18N_CTX, IGNORE_FETCH_ERRORS, NOTE_TIPS, PROP_LIST_SEARCH_MODES, TITLE_STR, WATCH_EVENTS, WATCH_EVENTS_OBJ, WATCH_TYPES, formatPrice, isNewVersion, isValidEmail, listingPicUrlReplace, regex_email;

/*
assignment:{
  exclude:['oh','sold','dom','ptype','ptype2','bdrm','bthrm','mfee']}
prcons:{
  exclude:['oh']
}
*/
// {getGa4Header} = require './googleTags'
I18N_CTX = I18N_CTX = {
  MENU: 'menu',
  PROP: 'prop',
  EVALUATION: 'evaluation',
  SHOWING: 'showing',
  CPM: 'cpm',
  CITY: 'city',
  YELLOWPAGE: 'yellowpage',
  SCHOOL: 'school',
  TOOLS: 'tools',
  FORUM: 'forum',
  FORM: 'form',
  PROJECT: 'project',
  STATE: 'state',
  LOCATION: 'location',
  MONTH: 'month',
  UNIT: 'unit',
  TIME: 'time',
  CENSUS: 'census'
};

// by fields: rms，bsmt,heat, proplst, gatp, fce, type_own1_out, lpunt, bsmt_out, month, age, ac, rm_out, status, rgdr
// Only change the following when modifying the code in the corresponding location
// TODO: condo(rdoeiq8w,floor plans & pricing) => shall not have this context
// TODO: cpm(ajax, barrie, ayton...) => city names use CITY
// TODO: propertylisting(age,bedroom) => PROP
// TODO: project(mississauga,toronto,coquitlam) => CITY; project(freehold,townhouse,condo) => PROP
// TODO: todo(my listings,follower) => MENU
// TODO: municipal => STATE
// TODO: provience(gta,gva,qc,bc) => LOCATION, STATE
// TODO: prediction => EVALUATION
// TODO: discover => MENU
// TODO: indexdrawer => MENU
// TODO: setting => MENU
// TODO: wepage(flyer,listing) => MENU
// TODO: sqft(0-499,...) => drop & replace
// TODO: wesite => drop & replace
// TODO: property => prop & replace
// TODO: notification => drop & replace
// TODO: contactcrm => drop & replace
// TODO: landlord => drop & replace
// TODO: all others that less than 10 reoccurrences => drop/replace/add to the I18N_CTX when needed
// TODO: some translations in fileStorage folders, need to consider how to coordinates each parts
PROP_LIST_SEARCH_MODES = PROP_LIST_SEARCH_MODES = {
  'Sold': {
    k: 'dom',
    v: -90,
    displayVal: 'Off Market 3 months',
    saleDesc: 'Sold'
  },
  'Leased': {
    k: 'dom',
    v: -90,
    displayVal: 'Off Market 3 months',
    saletp: 'lease',
    saleDesc: 'Leased'
  },
  'Open House': {
    k: 'oh',
    v: true
  },
  'Residential': {
    src: 'mls',
    ptype: 'Residential',
    functions: [{
      nm: 'ptypeSelect',
      params: ['Residential', 'Residential']
    }]
  },
  'Commercial': {
    src: 'mls',
    ptype: 'Commercial',
    functions: [{
      nm: 'ptypeSelect',
      params: ['Commercial', 'Commercial']
    }]
  },
  'Other': {
    src: 'mls',
    ptype: 'Other',
    functions: [{
      nm: 'ptypeSelect',
      params: ['Other', 'Other']
    }]
  },
  'Assignment': {
    src: 'rm',
    ltp: 'assignment',
    ptype: 'Assignment',
    ptype2: []
  },
  'Exclusive': {
    src: 'rm',
    ptype: 'Exclusive',
    cmstn: true,
    ptype2: [],
    saletps: {
      sale: {
        ltp: 'exlisting'
      },
      lease: {
        ltp: 'rent',
        cmstn: true
      }
    }
  },
  'Landlord': {
    src: 'rm',
    ltp: 'rent',
    ptype: 'Landlord',
    saletp: 'lease',
    ptype2: []
  },
  'PreCons': {
    src: 'rm',
    ltp: 'projects',
    // type:'projects',
    oh: false,
    ptype: 'Project'
  },
  'Near MTR': {
    k: 'neartype',
    v: 'mtr'
  },
  'Price Off': {
    k: 'lpChg',
    v: 'off'
  },
  'Best School': {
    k: 'sch',
    v: 'best'
  },
  'Sold Fast': {
    k: 'sold',
    v: 'fast',
    saleDesc: 'Sold'
  },
  'Today': {
    k: 'dom',
    v: '0',
    displayVal: 'Today'
  },
  'POS': {
    k: 'isPOS',
    v: 1
  },
  'Estate': {
    k: 'isEstate',
    v: 1
  },
  'Sold At Loss': {
    k: 'soldLoss',
    v: 'loss'
  }
};
TITLE_STR = TITLE_STR = {
  CONTACT: 'Contact',
  UPGRADE_TO_VIP: 'Upgrade To VIP',
  LISTING_AGENTS: 'Listing Agents',
  CONTACT_AGENT: 'Contact Agent',
  CONTACT_LANDLORD: 'Contact Landlord',
  CONTACT_VVIP_AGENT: 'Contact VVIP Agent',
  BOOK_VIEWING: 'Book Viewing',
  REQUEST_INFO: 'Request Info',
  REQUEST_APPRAISAL: 'Request Appraisal',
  BUY_TRUSTEDASSIGN: 'To Buy Assignment',
  SALE_TRUSTEDASSIGN: 'Assign My Condos Or APS'
};
CONTACT_REALTOR_VALUES = CONTACT_REALTOR_VALUES = {
  WEB: 'web',
  APP: 'app',
  WECHAT: 'wechat',
  PROJECT: 'project',
  CRM: 'crm',
  OWNER: 'owner',
  SALESGROUP: 'salesGroup',
  OWNER_NOT_IN_CRM: 'ownerNotInCrm',
  NO_OWNER: 'noOwner',
  OWENR_IN_CRM_EQUAL_TO_FOLLOW_AGENT: 'OwnerInCrmEqualToFollowAgent',
  OWENR_IN_CRM_NOT_EQUAL_TO_FOLLOW_AGENT: 'OwnerInCrmNotEqualToFollowAgent',
  ALL: 'all',
  // PROJECT_HAS_SPONSOR: 'projectHasSponsor'
  PROJECT_SPONSOR: 'projectSponsor',
  LANDLORD_RENT: 'landLordRent',
  RM_LISTING: 'rmListing',
  MLS: 'mls',
  EVALUATION: 'evaluation',
  LOGGEDIN: 'loggedIn',
  NOT_LOGGEDIN: 'notLoggedIn',
  // FOLLOW_NONE_RM_REALTOR:'followNoneRmRealtor'
  // FOLLOW_RM_REALTOR: 'followRmRealtor'
  // NO_FOLLOW_REALTOR:'noFollowRealtor'
  NONE_VIP_REALTOR: 'noneVipRealtor',
  CUSTOMER_FOLLOW_NONE_RM_REALTOR: 'customerFollowNoneRmRealtor',
  CUSTOMER_FOLLOW_RM_REALTOR: 'customerFollowRmRealtor',
  CUSTOMER_FOLLOW_NONE: 'customerFollowNone',
  // REALTOR: 'realtor'
  VIP_REALTOR: 'vipRealtor',
  // VIP_ALLIANCE: 'vipAlliance'
  // OTHER: 'other'
  HAS_WECHAT: 'hasWechat',
  NO_WECHAT: 'noWechat',
  DEFAULT: 'default',
  MLS_AGENT_PANEL: 'mlsAgentPanel',
  FORM: 'form',
  AGENT_CARD: 'agentCard',
  FOLLOWED_NONE_RM_REALTOR: 'followedNoneRmRealtor',
  FOLLOWED_RM_REALTOR: 'followedRmRealtor',
  LISTING_AGENT: 'listingAgent',
  UPGRADE_VIP: 'upgradeVip',
  INQUIRES_INFO: 'I would like more information on',
  BOOK_VIEWING_INFO: 'I want to book an appointment to view',
  PROPERTY_INQUIRY: 'Property Inquiry',
  GTA: 'GTA',
  RM_CONTROL_CITY: 'rmControlCity',
  RM_COOP_CITY: 'rmCoopCity',
  RM_NONE_COOP_CITY: 'rmNoneCoopCity',
  SHOW_REALTOR_YELLOW_PAGE: 'showRealtorYellowPage',
  TOP_LISTING_AGENT: 'topListingAgent',
  RM_LISTING_OWNER: 'rmListingOwner',
  STUDENT_RENTAL: 'student_rental',
  TRUSTED_ASSIGNMENT: 'trustedAssignment',
  FUB_ASSIGNMENT_EMAIL: 'fubAssignmentEmail',
  BUY_TRUSTEDASSIGNMENT: 'buyTrustedAssign',
  SALE_TRUSTEDASSIGNMENT: 'saleTrustedAssign',
  BUY_SALE_PLACEHOLDER: 'Please describe your needs briefly',
  FUB_PROJECT_EMAIL: 'fubProjectEmail',
  ASSIGNMENT: 'assignment',
  SHOW_NOTHING: 'showNothing',
  BOOK_VIEWING_SIMILAR: "I'd like to buy/sell something similar to"
};
WATCH_EVENTS = WATCH_EVENTS = ['New Sale', 'Price Changed', 'Sold', 'Delisted', 'New Rent', 'Leased'];
WATCH_EVENTS_OBJ = WATCH_EVENTS_OBJ = {
  NEW: 'New Sale',
  PRICE: 'Price Changed',
  SOLD: 'Sold',
  DELISTED: 'Delisted',
  LEASE: 'New Rent',
  LEASED: 'Leased'
};
WATCH_TYPES = WATCH_TYPES = ['Detached', 'Semi-Detached', 'Townhouse', 'Apartment'];
formatPrice = formatPrice = function (value) {
  var u, unit;
  if (!value) {
    //913000 -> 913K, 2000000 -> 2M
    return null;
  }
  u = 0;
  while (value >= 1000) {
    value = Math.round(value / 10);
    value = value / 100;
    u++;
  }
  unit = ' KMT'.charAt(u);
  if ('K' === unit) {
    value = parseInt(value);
  }
  return value + unit;
};

//curVer > need
isNewVersion = isNewVersion = function (curVer, need) {
  var curVerObj, i, index, len, needObj, ref;
  if (curVer === 'appDebug') {
    return true;
  }
  if (!(/\d+\.\d+\.\d+/.test(curVer) && /\d+\.\d+\.\d+/.test(need))) {
    return false;
  }
  curVer = curVer.split('.');
  need = need.split('.');
  curVerObj = [];
  needObj = [];
  ref = [0, 1, 2];
  for (i = 0, len = ref.length; i < len; i++) {
    index = ref[i];
    if (curVer[index]) {
      curVerObj[index] = parseInt(curVer[index]);
    }
    if (need[index]) {
      needObj[index] = parseInt(need[index]);
    }
  }
  if (curVerObj[0] > needObj[0]) {
    return true;
  }
  if (curVerObj[0] === needObj[0] && curVerObj[1] > needObj[1]) {
    return true;
  }
  if (curVerObj[0] === needObj[0] && curVerObj[1] === needObj[1] && curVerObj[2] >= needObj[2]) {
    return true;
  }
  return false;
};
IGNORE_FETCH_ERRORS = ['TypeError: cancelled', 'Failed to fetch', 'TypeError: Failed to fetch'];
FORM_PAGE = FORM_PAGE = [CONTACT_REALTOR_VALUES.MLS, CONTACT_REALTOR_VALUES.LANDLORD_RENT, CONTACT_REALTOR_VALUES.TRUSTED_ASSIGNMENT, CONTACT_REALTOR_VALUES.RM_LISTING, CONTACT_REALTOR_VALUES.PROJECT,
// default 发给owner,如果没有owner，显示表格发给crm
// CONTACT_REALTOR_VALUES.PROJECT_HAS_SPONSOR, # default 发给sponsor agent
CONTACT_REALTOR_VALUES.BUY_TRUSTEDASSIGNMENT, CONTACT_REALTOR_VALUES.SALE_TRUSTEDASSIGNMENT, CONTACT_REALTOR_VALUES.EVALUATION, CONTACT_REALTOR_VALUES.STUDENT_RENTAL, CONTACT_REALTOR_VALUES.ASSIGNMENT];
NOTE_TIPS = NOTE_TIPS = {
  NOUADDR: 'You can not creat note without an address!',
  NOPROPID: 'Data error, please return to previous page and try again!'
};
regex_email = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
isValidEmail = isValidEmail = function (eml) {
  return regex_email.test(eml);
};
listingPicUrlReplace = listingPicUrlReplace = function (prop = {}, num) {
  var i, idx, l, len, m, p, re, replaced, ret, str;
  l = prop.picUrls || [];
  // ret = []
  // for p in l
  //   for i in [1..7]
  //     if (p.indexOf('${'+i+'}') > -1) and prop['$'+i]
  //       p = p.split('${'+i+'}').join(prop['$'+i])
  //   ret.push p
  //$0=https://img.realmaster.com/mls/1/791/N4659791.jpg?t=8080980
  //_$1=https://img.realmaster.com/mls/{{1}}/791/N4659791_{{1}}.jpg?
  // TODO: support both _$1/$1
  ret = [];
  for (idx = i = 0, len = l.length; i < len; idx = ++i) {
    p = l[idx];
    if (num && idx > num) {
      break;
    }
    if ((m = p.match(/^(\$[0-9a-zA-Z])(.*)$/)) && (str = prop[m[1]])) {
      re = new RegExp("\\{\\{" + m[1].substr(1) + "\\}\\}", 'gi');
      replaced = str.replace(re, m[2]);
      // NOTE: after bcre update, "https://cdnparap130.paragonrels.com/ParagonImages/Property/p13/BCRES/262856789/{{2}}/640/480/146a6dcbb7aa918ffb774c028942ec8c/16/d00ea0f7bda935b785b101c5e1be77b7/262856789.JPG"
      // no longer works, need to add "-{{2}}" to the end of url
      // TODO: remove this after bcre update or when all old data are gone
      if (idx > 0 && /ParagonImages.*\/(\d|-)+\.(JPG|PNG)$/i.test(replaced)) {
        replaced = replaced.replace(/(\.(JPG|PNG))$/i, `-${idx}$1`);
      }
      ret.push(replaced);
    } else {
      ret.push(p);
    }
  }
  if (ret.length === 0) {
    ret.push('/img/noPic.png');
  }
  return ret;
};
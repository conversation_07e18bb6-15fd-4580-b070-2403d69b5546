helpers = require '../lib/helpers'
geolib = require 'geolib'
debugHelper = require '../lib/debug'
{isSubstring,isSubsetBetweenArray,filterSpecialCase,filterOtherThanTel,unifyAddress} = require '../lib/helpers_string'

{DIRECTION_ABBR,separateUnitFromAddr} = require '../lib/propAddress'
{format2Fixed} = require '../lib/helpers_number'
debug = debugHelper.getDebugger()


# params id. school id, bnds id.
# array [bnds, schs]
# bnds, no ne, sw, sch, loc, url no, imported from api schools has no tp.
###*
# 设置学校类型标记
# @param {Object} sch - 学校对象
# @returns {void} - 直接修改传入的学校对象
###
exports.setUpSchoolType = setUpSchoolType = (sch)->
  if sch.tp
    if sch.tp is 'Catholic'
      sch.catholic = true
    else if sch.tp is 'Private'
      sch.pri = true
    else if sch.tp is 'Charter'
      sch.chtr = true
    else if sch.tp is 'Separate'
      sch.sprt = true
    else if sch.tp is 'Francophone'
      sch.frcp = true
    else if sch.tp is 'Independent'
      sch.idp = true
    else if sch.tp is 'First Nation'
      sch.frna = true
    else if sch.tp is 'Protestant'
      sch.ptst = true

###*
# 检查位置是否在边界内
# @param {Object} opt - 选项对象
# @param {Object} bn - 边界对象 
# @param {Array} bndIds - 边界ID数组
# @returns {boolean} 是否在边界内
###
isInBoundary = (opt, bn, bndIds) ->
  # check for bc helpers.isInBoundary
  unless bndIds?[0]
    bndIds = null
  if bndIds
    return true if bndIds.indexOf(bn.bnid) >= 0
    return false
  if (opt.mode isnt 'bnd') or ((opt.mode is 'bnd') and helpers.isInBoundary bn.bn,opt.loc)
    return true
  return false

###*
# 解析学校结果数据
# @param {Array} l - 学校数据数组
# @param {Object} opt - 选项对象
# @param {boolean} opt.import - 是否为导入模式
# @param {Array} opt.loc - 位置坐标
# @param {boolean} opt.slice - 是否截取边界数据
# @param {boolean} opt.nofi - 是否排除法语浸润学校
# @param {boolean} opt.noSort - 是否不排序
# @returns {Array} [边界数组, 学校映射对象, 非法语浸润学校映射对象]
###
exports.parseSchoolResults = parseSchoolResults = (l, opt={})->
  bnds = []
  schs = {}
  schsNofi = {}
  l ?= []
  if opt.import
    for sch in l
      sch.bns ?= []
      for bn in sch.bns
        if isInBoundary opt, bn, opt.bnds
          bnds.push bn
          schs[sch._id] = sch
    return [bnds, schs]
  for sch in l
    if opt.loc?[0]? and sch.loc?[0]?
      sch.dis = Math.ceil(geolib.getDistance({latitude: sch.loc[0], longitude: sch.loc[1]},{latitude: opt.loc[0], longitude: opt.loc[1]})/100)/10
    if (frs = sch.fraser?[0])?
      sch.firate = frs.firate
      sch.firank = frs.firank + '/' + frs.fitotal
      if frs.filast5rank and frs.filast5total
        sch.filast5rank = frs.filast5rank + '/' + frs.filast5total
    if (eqao = sch.eqao?[0])?
      if eqao.g6rank and eqao.g6total
        sch.eqaorate = eqao.g6rank + '/' + eqao.g6total + " G6"
      else if eqao.g3rank and eqao.g3total
        sch.eqaorate = eqao.g3rank + '/' + eqao.g3total + " G3"
      else if eqao.g9acrank and eqao.g9total
        sch.eqaorate = eqao.g9rank + '/' + eqao.g9total + " G9"
    # TODO: merge school tp and sch.fi/catholic
    # sch.tp may be []??
    setUpSchoolType(sch)
    sch.c = sch.catholic
    delete sch.catholic
    sch.bnsc = sch?.bns?.length or 0
    #TODO:
    if opt?.slice
      sch.bns = if bns = sch.bns then sch.bns = sch.bns.slice(0,1) else sch.bns = []
    sch.bns ?= []
    for bn in sch.bns
      continue unless bn
      if isInBoundary opt, bn, opt.bnds
        bn._id = sch._id
        # ? delete c then add c? delete ne, sw for batch.
        for k in ['bns','bn','ref','catholic','c','fraser','Comments','exbnid','exclude']
          delete bn[k]
        # copy sch to bnd and then delete that from school.
        for k in ['gf','gt','ef','fi','c','ele','hgh','mid'] # need review
          if bn.useSch
            bn[k] = sch[k]
          else
            bn[k] ?= sch[k]
        bnds.push bn
        schs[sch._id] = sch # only school in boundary
        #check bnd fi
        if opt.nofi and (bn.fi or bn.ef)
          continue
        else
          schsNofi[sch._id] = sch
    #用于tag，计算非fi的学校的分数。
    unless opt.nofi
      for k in ['bns','fraser','eqao','gf','gt','ef','fi','c','eqao']
        delete sch[k]
  if not opt.noSort
    bnds.sort(
      compareSchoolFunc
    )
  return [bnds, schs, schsNofi]

###*
# 比较学校排序函数
# @param {Object} a - 第一个学校对象
# @param {Object} b - 第二个学校对象
# @returns {number} 排序值
###
exports.compareSchoolFunc = compareSchoolFunc = (a,b)->
  # catholic
  return 1 if a.c and not b.c
  return -1 if b.c and not a.c
  # french
  return 1 if (a.ef or a.fi) and not (b.ef or b.fi)
  return -1 if (b.ef or b.fi) and not (a.ef or a.fi)
  # grade
  return (parseInt(a.gf) or 0)-(parseInt(b.gf) or 0)

# opt.mode is 'list': find the schools within opt.bbox
# opt.mode is 'bnd', find home schools,
# else find shool by school id
###*
# 获取学校数据
# @param {Object} Schools - 学校数据库集合
# @param {Object} opt - 查询选项
# @param {Function} cb - 回调函数
# @returns {void}
###
exports.getSchools = (Schools, opt, cb)->
  fields = {
    _id:1,addr:1,bns:1,catholic:1,city:1,nm:1,loc:1,
    gf:1,gt:1,fi:1,ef:1,fraser:1,eqao:1,
    ele:1,mid:1,hgh:1,hot:1,sourceId:1
  }
  if opt.import
    fields = {
      bns:1
    }
  if opt.idOnly
    fields = {_id:1, bns:1}
    opt.noSort = true
  if opt.schs and opt.bnds
    # bnds All null, search by school,
    q = {$or:[{_id:{$in:opt.schs}},{sourceId:{$in:opt.schs}}]}
    q['bns.bnid'] = {$in:opt.bnds} if opt.bnds[0]

  else if opt.mode is 'list'
    swLng = opt.bbox[0]
    swLat = opt.bbox[1]
    neLng = opt.bbox[2]
    neLat = opt.bbox[3]
    q = {
      $and:[
        {'lat':{$lt:neLat}},
        {'lat':{$gt:swLat}},
        {'lng':{$lt:neLng}},
        {'lng':{$gt:swLng}}
      ],IsActive:1}
    #db.school.update({catholic:0,$or:[{'fraser.0.firank':{$lt:100}},{'eqao.0.g6rank':{$lt:100}},{'eqao.0.g3rank':{$lt:100}},{'eqao.0.g9rank':{$lt:50}}]},{$set:{hot:1}},{multi:1})
  else if opt.mode is 'bnd'
    # TODO:查到的学校可能有某一个bnd match，需要进一步 计算是那个bns匹配
    # q = {'bns.sw.0':{$lte:opt.loc[0]},'bns.sw.1':{$lte:opt.loc[1]},'bns.ne.0':{$gte:opt.loc[0]},'bns.ne.1':{$gte:opt.loc[1]},IsActive:1}
    q =
      IsActive:1
      'bnds.features.geometry':
        $geoIntersects:
          $geometry:
            type: 'Point' ,
            coordinates:[opt.loc[1],opt.loc[0]]
  else
    q = {$or:[{_id:opt.schid},{sourceId:opt.schid}]}
  # console.log JSON.stringify q
  # beginParse = Date.now()
  Schools.findToArray q,
    {fields:fields,sort:{catholic:1,ef:1,fi:1,gf:1}},
    (err,results)->
      #去掉inactive的。
      # console.log 'query school time'
      # console.log Date.now()-beginParse
      if err
        console.error err
        return cb err.toString()
      if not results?
        return cb 'Not found'
      # beginParse = Date.now()
      [bnds,schs,schsNofi] = parseSchoolResults(results, opt)
      # console.log 'parse school result time'
      # console.log Date.now()-beginParse
      return cb null, {bnds:bnds,schs:schs,schsNofi:schsNofi}

###*
# 生成学校ID
# @param {Object} params - 参数对象
# @param {string} params.province - 省份
# @param {string} params.city - 城市
# @param {string} params.name - 学校名称
# @returns {string|null} 生成的学校ID
###
exports.generateSchoolId = ({province,city,name})->
  return null unless (name and province and city)
  name = name.replace(/School/ig,'').trim()
    .replace(/\s+/g,'-')
    .replace(/Elementary/ig,' E')
    .replace(/Secondary/ig,' S')
    .replace(/Public/ig,'P')
    .replace(/[^A-Z0-9\-]/ig,'')
  province = province.replace(/[^A-Z0-9]/ig,'')
  city = city.replace(/[^A-Z0-9]/ig,'')
  return ("S-#{province}-#{city}-#{name}").toUpperCase()

###*
# 处理法语字符
# @param {string} name - 需要处理的字符串
# @returns {string} 处理后的字符串,将法语特殊字符转换为英文字符
###
exports.dealFrenchCharacter = dealFrenchCharacter = (name) ->
  unless name
    return name
  name = name.replace(/[À|Â|à|â]/g,'a')
    .replace(/[È|É|Ê|Ë|è|é|ê|é|ë]/g,'e')
    .replace(/[Î|Ï|î|ï]/g,'i')
    .replace(/[Ô|Ö|ô|ö]/g,'o')
    .replace(/[Ù|Û|ù|û|ü]/g,'u')
  return name

###*
# 生成标准化的学校名称
# @param {string} name - 原始学校名称
# @returns {string|null} 标准化后的学校名称
###
exports.generateSchoolName = generateSchoolName = (name)->
  return null unless name
  name = dealFrenchCharacter(name)
  name = name.replace(/ Sep/g,' ')#特殊学校
    .replace(/ E & Md /g,' elementary and middle ')
    .replace(/ Jr & Md /g,' junior middle ')
    .replace(/ Jr & Sr /g,' junior and senior ')
    .replace(/ CI & VS/g,' collegiate institute and vocational')
    .replace(/ C & VI/g,' collegiate and vocational institute')
    .replace(/ VI/g,' vocational')
    .replace(/ & /g,' and ')
    .replace(/ C /g,' catholic ')
    .replace(/ Jr/g,' junior')
    .replace(/ Md/g,' middle')
    .replace(/ Sr/g,' senior')
    .replace(/ CI/g,' collegiate institute')
    .replace(/ TS/g,' technical institute')
    .replace(/ PS$/ig,' public school')
    .replace(/ E[S| ]$/ig,' elementary school')
    .replace(/ [S|H]S$/ig,' secondary school')
    .replace(/ MS$/ig,' middle school')
    .replace(/^Msgr /g,'monsignor ')
    .replace(/ Alt./g,' alternative')
    .replace(/ S$/ig,' school')
    .replace(/ [E.|(Elem)] /g,' elementary ')
  return name

# 可忽略的存在差异的词
canIgnoreDiffWord = ['rr','rd','ave','road','street','rr2','w','b','st','est','rue','hwy','dr','old','p.o','of','cres','floor','no','box','r','regional']

# 缩略词和拼写错误的词以及特殊例子
abbrOrErrorWords = {
  road:'rd',
  parkway:'pky',
  pkwy: 'pky'
  francis:'frances',
  highway:'hwy',
  fourth: '4th'
  '5th': '5'
  '22nd': '22'
  '3rd':'3'
  'fifth': '5',
  '2nd': 'second'
  saint: 'st', # 城市
  'mount': 'mt', # 城市
  'whitchurch-stouffville': 'stouffville', # 城市
  'elementaire': 'elementary',
  mclaughin:'mclaughlin'
  '&': 'and'
  '1st': 'first',
  'tsse':'terrasse',
  'junior': 'elementary',
  'central': 'centre',
  'senior': 'sr',
  'elem': 'elementary',
  'msgr': 'monsignor',
  '9th': '9',
  'ninth': '9'
}
# 特殊处理学校
specialEquality = {
  'St Marys D.C. & V. I. High School': 'St Marys District Collegiate And Vocational Instit',
  'Bishop Paul Francis Reding Catholic Secondary School': 'Bishop P. F. Reding Secondary School',
  'Dr. J. Edgar Davey Elementary Public School': 'Dr. J. E. Davey',
  'Kanétskare Elementary School Middle School': 'Kanetskare (formerly Ryerson)',
  'Memorial Elementary School Public School': 'Memorial City',
  'Fisher Park/Summit AS Public School': 'Fisher Park Public School / Summit Alternative School',
  'Ottawa Technical Secondary School': 'Ottawa Technical Learning Centre',
  'Tomken Road Senior Public School': 'Tomken Road Middle School',
  'Father Michael McGivney Catholic High School': 'Father Michael Mcgivney Catholic Academy',
  'St Michael Elementary School': 'St. Michael School, Ottawa',
  'Harrisfield Public School': 'Harris Heights Public School',
  'Beaverton Public School': 'Beaver River P.S.'
}

###*
# 处理特殊字符,去除非字母数字下划线的字符
# @param {string} str - 输入字符串
# @returns {string} 处理后的字符串
###
exports.dealSpecificChat = dealSpecificChat = (str) ->
  unless str
    return str
  str = str.replace(/(’s)|('s)/g,'').trim()
    .replace(/[^\w\s]+/g, ' ').trim()
    .replace(/\s{2,}/g, ' ').trim()
    .replace(/ [ ]+/g, ' ')
  return str

###*
# 删除学校名称中的常见词
# @param {string} str - 学校名称
# @returns {string} 处理后的学校名称
###
exports.delSchoolCommWord = delSchoolCommWord = (str) ->
  unless str
    return str
  str = dealFrenchCharacter(str)
  str = str.replace(/(public)|(school)|(publique)|(schoo)|(scho)|(catholic)|(separate)|(p\.s\.)|(institute)|(elementaire)|(elementary)|(junior)|(secondary)|(intermediate)|(senior)|(middle)|(high)|(collegiate)|(college)/g, '').trim()
  str = dealSpecificChat(str)
  return str

###*
# 处理学校名称信息,用于数据库查找
# @param {string} str - 学校名称
# @returns {string} 处理后的正则表达式字符串
###
exports.dealSchoolNameInfor = dealSchoolNameInfor = (str) ->
  unless str
    return str
  strOrigin = str.replace(/\./g, '\\.').replace(/\)/g, '\\)').replace(/\(/g, '\\(').trim()
  str = str.toLowerCase()
  str = dealFrenchCharacter(str)
  str = str.replace(/paull /g,'paul ')
  strInCludeOrigin = str.replace(/(collegiate and vocational)|(public)|(school)|(publique)|(schoo)|(scho)|(catholic)|(separate)|(p\.s\.)|(institute)|(elementaire)|(elementary)|(junior)|(secondary)|(intermediate)|(senior)|(middle)|(high)|(collegiate)|(college)/g, '').trim()
  strInCludeOrigin = strInCludeOrigin.replace(/^ecole /g, '').replace(/^(st|st\.) /g, '').replace(/\s{2,}/g, ' ').trim()
  str = dealSpecificChat(strInCludeOrigin)
  if str isnt strInCludeOrigin
    strInCludeOrigin = strInCludeOrigin.replace(/\./g, '\\.').replace(/\)/g, '\\)').replace(/\(/g, '\\(').trim()
    return "(#{str})|(#{strInCludeOrigin})|(#{strOrigin})"
  return "(#{str})|(#{strOrigin})"

###*
# 处理街道和城市不同时的学校名称
# @param {string} str - 学校名称
# @returns {string} 处理后的学校名称
###
exports.dealDiffStAndCitySchool = dealDiffStAndCitySchool = (str) ->
  unless str
    return str
  str = str.toLowerCase()
  str = dealFrenchCharacter(str)
  str = str.replace(/(public)|(school)|(publique)|(schoo)|(scho)|(p\.s\.)/g, '').trim()
  str = dealSpecificChat(str)
  return str

###*
# 删除街道中的特定后缀(box/unit/rr)
# @param {string} str - 街道名称
# @returns {string} 处理后的街道名称
###
exports.delStreetSpecificSuff = delStreetSpecificSuff = (str) ->
  unless str
    return str
  str = dealFrenchCharacter(str)
  str = str.replace(/(box \d+)$/g, '').trim()
  str = str.replace(/(unit \d+)$/g, '').trim()
  str = str.replace(/(rr \d+)$/g, '').trim()
  str = dealSpecificChat(str)
  return str

###*
# 判断年级范围是否相同
# @param {Object} additional - 包含年级信息的对象
# @param {string} additional.range - 年级范围,如'JK-6'
# @param {boolean} additional.ele - 是否为小学
# @param {boolean} additional.hgh - 是否为高中
# @param {boolean} additional.mid - 是否为初中
# @param {number} additional.gf - 起始年级
# @param {number} additional.gt - 结束年级
# @returns {string|boolean} 'match'表示完全匹配,'include'表示包含关系,false表示不匹配
###
exports.judgeIsSameGradeRange = (additional) ->
  unless additional
    return false
  {range,ele,hgh,mid,gf,gt} = additional
  if range
    rangeArr = range.split('-')
    if (not isNaN(parseInt(rangeArr[0])))
      rangeArr[0] = [parseInt(rangeArr[0])]
    else
      rangeArr[0] = [-1,0,1]
    if (not isNaN(parseInt(rangeArr[1])))
      rangeArr[1] = parseInt(rangeArr[1])
  else
    return false
  if gf and gt
    #JK-8  1-8  k-8
    if (gf in rangeArr[0]) and (gt is rangeArr[1])
      return 'match'
    else if (rangeArr[1] < gf) # 7-8 9-12
      return false
    else if ((rangeArr[0][0] > gf) and (rangeArr[1] > gt))# 9-12 7-8
      return false
    else if ((not rangeArr[1]) and (rangeArr[0][0] < gf))# 6 9-12
      return false
    else
      return 'include'
  else
    if (range is '9-12') and hgh
      return 'match'
    if hgh and (rangeArr[1] is 12)
      return 'include'
    if (rangeArr[1] <= 8) and (ele or mid)
      return 'match'
    return false

###*
# 判断两个地址是否相同或相似
# @param {string} stringA - 第一个地址
# @param {string} stringB - 第二个地址
# @param {Object} additional - 额外的比较信息
# @returns {boolean} 是否相同或相似
###
exports.judgeIsSameStreet = judgeIsSameStreet = (stringA,stringB,additional) ->
  if ((not stringA) or (not stringB))
    return false
  stringA = stringA.toLowerCase()
  stringB = stringB.toLowerCase()
  if stringA is stringB
    return true
  stringA = delStreetSpecificSuff(stringA)
  stringB = delStreetSpecificSuff(stringB)
  if stringA is stringB
    return true
  arrayA = stringA.split(' ')
  arrayB = stringB.split(' ')
  # 如果街道为aa bbb cc和bbb cc应该这里就会认为是不匹配的，应该继续进行下面的判断
  # if arrayA[0] isnt arrayB[0]
  #   return false
  sameWord = 0
  # 去掉结尾的方向.eg:'92 Gidley Street East','92 Gidley St E'
  if DIRECTION_ABBR[arrayA[arrayA.length-1].toUpperCase()]
    arrayA.splice(arrayA.length - 1, 1)
  if DIRECTION_ABBR[arrayB[arrayB.length-1].toUpperCase()]
    arrayB.splice(arrayB.length - 1, 1)
  # 替换缩略词和错误的词
  for itemA,i in arrayA
    arrayA[i] = abbrOrErrorWords[arrayA[i]] if abbrOrErrorWords[arrayA[i]]
  for itemB,j in arrayB
    arrayB[j] = abbrOrErrorWords[arrayB[j]] if abbrOrErrorWords[arrayB[j]]
  if arrayA.length is arrayB.length
    for item,i in arrayA
      if item is arrayB[i]
        sameWord += 1
      if (sameWord / arrayA.length) > 0.6
        return true
  sameWord = 0
  result = isSubsetBetweenArray(arrayA,arrayB)
  if result.isSubset
    return true
  if (result.diffArray.length is 3) and ("#{result.diffArray[0]}#{result.diffArray[1]}" is result.diffArray[2]) #eg.[ 'grand', 'view', 'grandview' ]
    return true
  else
    if (result.diffArray.length is 2) and isSubstring(result.diffArray[0],result.diffArray[1])
      return true
    for diff in result.diffArray
      if ((diff in canIgnoreDiffWord) or DIRECTION_ABBR[diff.toUpperCase()]) or (not isNaN(parseInt(diff)))
        sameWord += 1
      if ((diff is additional?.city?.toLowerCase()) or (diff is additional?.Municipality?.toLowerCase()) or (diff is additional?.xlsCity?.toLowerCase()))
        sameWord += 1
    if sameWord is result.diffArray.length
      return true
    return false

###*
# 判断两个学校名称是否相同或相似
# @param {string} stringA - 第一个学校名称
# @param {string} stringB - 第二个学校名称
# @returns {boolean} 是否相同或相似
###
exports.judgeIsSameSchool = judgeIsSameSchool = (stringA,stringB) ->
  if ((not stringA) or (not stringB))
    return false
  if (specialEquality[stringA] is stringB) or (specialEquality[stringB] is stringA)
    return true
  stringA = stringA.toLowerCase()
  stringB = stringB.toLowerCase()
  if stringA is stringB
    return true
  stringA = delSchoolCommWord(stringA)
  stringB = delSchoolCommWord(stringB)
  arrayA = stringA.split(' ')
  arrayB = stringB.split(' ')
  for itemA,i in arrayA
    arrayA[i] = abbrOrErrorWords[arrayA[i]] if abbrOrErrorWords[arrayA[i]]
  for itemB,j in arrayB
    arrayB[j] = abbrOrErrorWords[arrayB[j]] if abbrOrErrorWords[arrayB[j]]
  sameWord = 0
  result = isSubsetBetweenArray(arrayA,arrayB)
  if result.isSubset
    return true
  if (result.diffArray.length is 3)
    if ("#{result.diffArray[0]}#{result.diffArray[1]}" is result.diffArray[2])
      return true
  if (result.diffArray.length is 2) and isSubstring(result.diffArray[0],result.diffArray[1])
    return true
  return false

# 判断两个城市是否一致
sameCity =
  'UNIONVILLE': 'MARKHAM'
  'WOODBRIDGE': 'VAUGHAN'
  'KESWICK': 'GEORGINA'
  'BROOKLIN': 'WHITBY'
  'ETOBICOKE': 'TORONTO'
  'NORTH YORK': 'TORONTO'
  'WHITCHURCH-STOUFFVILLE': 'STOUFFVILLE'
###*
# 判断两个城市是否一致
# @param {string} cityA - 第一个城市名称
# @param {string} cityB - 第二个城市名称
# @returns {boolean} 是否一致
###
exports.judgeIsSameCity = judgeIsSameCity = (cityA,cityB)->
  cityA = cityA.toUpperCase()
  cityB = cityB.toUpperCase()
  if sameCity[cityA]
    cityA = sameCity[cityA]
  if sameCity[cityB]
    cityB = sameCity[cityB]
  return cityA is cityB

###*
# 处理教育局名称信息
# @param {string} str - 教育局名称
# @returns {string} 处理后的教育局名称
###
exports.dealBoardNameInfo = dealBoardNameInfo = (str) ->
  str = str.toLowerCase()
  str = str.replace(/( dsb)|(the )|(district)|(school)|(board)/g, '').trim()
  str = dealSpecificChat(str)
  return str


###*
# 对比eqao/fraser年度，更新数据，如果和旧数据有重复字段，在字段后添加craw
# @param {Array} newEqao - 新的eqao/fraser数据数组
# @param {Array} orgEqao - 原有的eqao/fraser数据数组
# @returns {Array} 更新后的数据数组,如果有重复字段会在字段名后添加craw
###
exports.getUpdateEqaoOrFraser = (newEqao,orgEqao)->
  return [] unless Array.isArray(newEqao) and Array.isArray(orgEqao)
  orgEqaoMap={}
  for eqao in orgEqao
    continue unless eqao?.yearmark?.length >= 4
    orgEqaoMap[eqao.yearmark.slice(-4)] = eqao
  for n in newEqao
    newEqaoYear = n.yearmark.slice(-4)
    if not orgEqaoMap[newEqaoYear]
      orgEqaoMap[newEqaoYear] = n
      continue
    matchedEqao = orgEqaoMap[newEqaoYear]
    for newk in Object.keys(n)
      if newk in ['yearmark']
        continue
      if v = matchedEqao[newk]
        if Array.isArray(v)
          if v.join('') is n[newk].join('')
            continue
        else
          if v is n[newk]
            continue
        # 相同字段，值不相等，则给字段名后添加craw
        matchedEqao[newk+'craw'] = n[newk]
    orgEqaoMap[newEqaoYear] = Object.assign {},n,matchedEqao
  return Object.values(orgEqaoMap).reverse()

###*
# 格式化rmrank数据为折线图数据结构
# @param {Object} school - 学校对象
# @returns {void} - 直接在school对象上添加chart字段
# @description 添加chart字段格式:
# {
#   label: [年份数组],
#   dataMap: {
#     G3/G6/G9/pri: {
#       Ranking: [排名数组],
#       Rating: [评分数组]
#     }
#   }
# }
###
exports.formatChart = (school)->
  return unless (school.rmRank?.length > 0) or (school.ivyRank?.length > 0)
  rmrank = school.rmRank?.slice(0,5) or school.ivyRank?.slice(0,5)
  label = rmrank.map (rm)->
    return rm.yearmark
  label.reverse()
  dataMap = {}
  formatRmRankByGrade = (grade)->
    Rating = []
    Ranking = []
    for rm in rmrank
      if rm[grade+'rmScore']
        Rating.unshift format2Fixed(rm[grade+'rmScore']/100,1)
      if rm[grade+'rmRanking']
        Ranking.unshift rm[grade+'rmRanking']
    if Rating.length > 0 or Ranking.length > 0
      return {Rating,Ranking}
    return null
  if school.private
    pri = formatRmRankByGrade('')
    dataMap.pir = pri if pri
  if school.ele
    g3 = formatRmRankByGrade('g3')
    dataMap.G3 = g3 if g3
    g6 = formatRmRankByGrade('g6')
    dataMap.G6 = g6 if g6
  if school.hgh
    g9 = formatRmRankByGrade('g9')
    dataMap.G9 = g9 if g9
  school.chart = {label,dataMap}


###*
# 格式化rmrank数据为表格结构
# @param {Object} school - 学校对象
# @returns {void} - 直接在school对象上添加rankMap字段
# @description 添加rankMap字段格式:
# {
#   ivyMap: {
#     gradSize: { yearmark:value },
#     ivyCount: { yearmark:value },
#     ivyPct: { yearmark:value }
#   },
#   majorsMap: {
#     各专业百分比映射
#   },
#   topSchoolsMap: {
#     各顶尖学校录取率映射
#   }
# }
###
ivy = ['gradSize','ivyCount']
priSchoolMajors = ['artsPct','engPct','busPct','fineArtsPct','healthPct','profPct','otherPct']

exports.formatRmRankPir = (school)->
  return unless school.ivyRank?.length > 0
  rmrank = school.ivyRank.slice(0,5)
  years = rmrank.map (rm)->
    return rm.yearmark
  ivyMap = {}
  majorsMap = {}
  topSchoolsMap = {}

  for rm in rmrank
    for i in ivy
      if rm[i] isnt undefined
        v = rm[i]
        if /count/i.test i
          v = v / 5
        ivyMap[i] ?= {}
        ivyMap[i][rm.yearmark] = v
    for m in priSchoolMajors
      if rm[m] isnt undefined
        v = rm[m]
        if /pct/i.test m
          v += '%'
        majorsMap[m] ?= {}
        majorsMap[m][rm.yearmark] = v
    for t in rm.topSchools
      if t.school
        topSchoolsMap[t.school]?= {}
        topSchoolsMap[t.school][rm.yearmark] = t.pct + '%'
  school.rankMap = {ivyMap,majorsMap,topSchoolsMap,years}


subjects = ['Reading','Writing','Math']
grades = ['g3','g6','g9','OSSLT']
# Lv4:优率,Lv3:良率,P:通过率,Pt:参与率,Score:得分,Ranking:排名
rankTitles = {
  g3: ['Lv4','Lv3','Score','Ranking'],
  g6: ['Lv4','Lv3','Score','Ranking'],
  g9: ['Pt','Lv4','Lv3','Score','Ranking'],
  OSSLT: ['P','Pt','Score','Ranking']
}
###*
# 格式化rmrank数据为表格结构(公立学校)
# @param {Object} school - 学校对象
# @returns {void} - 直接在school对象上添加rankMap字段
# @description 添加rankMap字段格式:
# {
#   g3/g6: {
#     Math/Reading/Writing: {
#       year: [Lv4,Lv3,Ranking,Score]
#     }
#   },
#   g9: {
#     Math: {
#       year: [Pt,Lv4,Lv3,Ranking,Score]
#     }
#   },
#   OSSLT: {
#     OSSLT: {
#       year: [Pt,Ranking,Score]
#     }
#   }
# }
###
exports.formatRmRankPub = (school)->
  return unless school.rmRank?.length > 0
  rankMap = school.rankMap or {}
  rmrank = school.rmRank.slice(0,5)
  for grade in grades
    subMap = {}
    for rm in rmrank
      for sub in subjects
        if grade is 'OSSLT'
          sub = 'OSSLT'
          key = "#{grade}rm"
        else
          key = "#{grade}rm#{sub}"
        showOrder = []
        for t in rankTitles[grade]
          if rm["#{key}#{t}"] > 0
            v = rm["#{key}#{t}"]
            if t is 'Score'
              v = format2Fixed(v/10,1,100)
            else if t isnt 'Ranking'
              v = format2Fixed(v,1,100)
            showOrder.push v
          else if rm["#{key}#{t}"]? and (t is 'Lv4')
            showOrder.push 0
          else
            showOrder.push 'N/A'
        
        if showOrder.filter((item) -> item not in ['N/A']).length is 0
          continue
        #当Score值<=0时，将所有显示值设置为N/A
        if (not rm["#{key}Score"]?) or rm["#{key}Score"] <= 0
          showOrder = showOrder.map -> 'N/A'
        ###*
        # 处理法语浸润学校的G3年级特殊情况
        # 当学校是法语浸润学校(fi=1)且G3年级的阅读和写作成绩均为0时
        # 将相应的显示值设置为特殊标记
        ###
        if (grade is 'g3') and (sub in ['Reading','Writing']) and (school.fi is 1) and (rm.g3rmReadingLv3 <= 0) and (rm.g3rmWritingLv3 <= 0) and (rm.g3rmReadingLv4 <= 0) and (rm.g3rmWritingLv4 <= 0)
          showOrder[0] = '-'
          showOrder[1] = '-' 
          showOrder[3] = 'N/A'
        subMap[sub] ?={}
        subMap[sub][rm.yearmark] = showOrder
    rankMap[grade] = subMap if Object.keys(subMap).length
  school.rankMap = rankMap

KEYFACTS_GRADES = ['g3', 'g6', 'g9']

###
# 处理学校keyfacts展示部分的数据, native端也在用，谨慎修改字段名
# @param {Array|Object} schools - 学校数据，可以是数组或单个学校对象
# @param {Function} l10n - 本地化函数，用于获取翻译文本
# @return {Array|undefined} - 返回处理后的学校数组，如果输入为空则返回原值
###
exports.addKeyFacts = addKeyFacts = (schools, l10n, showCurBnTxt=false) ->
  # 如果schools不存在，直接返回
  return schools unless schools
  # 如果schools不是数组，将其转换为数组
  unless Array.isArray(schools)
    schools = [schools]
  for school in schools
    # 过滤大学和学院类型
    if school.tp is 'university' or school.tp is 'college'
      school.keyFacts = []
      continue

    keyFacts = []
    AIRank = school.rmRankKeyFacts or {}
    # 判断是否为私立学校（包含更完善的判断条件）
    if school.private or (school.type is 'private')
      keyFacts.push(
        { key: l10n('AI Ranking'), val: AIRank.rmRanking or '-', valTotal: AIRank.total or 0, alert: 'AI', diffRank: AIRank.rmRankChange or 0 }
        { key: l10n('AI Rating'), val: AIRank.rmScore or '-', alert: 'AI' }
        { key: l10n('Grade','school'), val: school.grd or '-' }
      )
      school.keyFacts = keyFacts
      continue
    
    # 处理安大略省公立学校的AI排名
    if school.prov_en is 'ON' and AIRank and Object.keys(AIRank).length
      isStyle2 = (AIRank.g3rmRanking and AIRank.g6rmRanking)
      KEYFACTS_GRADES.forEach (grade) ->
        if (not AIRank[grade + 'rmRanking']) then return
        upperGrade = grade.toUpperCase()
        baseFact =
          key: l10n('AI Ranking'),
          grade: upperGrade, 
          alert: 'AI',
          isStyle2: isStyle2,
          val: AIRank[grade + 'rmRanking'] or '-',
          valTotal: AIRank[grade + 'total'] or 0
          diffRank: AIRank[grade + 'rmRankChange'] or 0
        if isStyle2
          keyFacts.push({ ...baseFact, rating: AIRank[grade + 'rmScore'] or '0'})
        else
          keyFacts.push(baseFact)
          keyFacts.push({ key: l10n('AI Rating'), grade: upperGrade, val: AIRank[grade + 'rmScore'] or '0', alert: 'AI', isStyle2 })
    else
      # 使用Fraser排名数据
      keyFacts.push(
        { key: l10n('Rating'), val: school.firate or '-' },
        { key: l10n('Fraser Ranking'), valTotal: school.fitotal, val: school.firank or '-' }
      )
    # 如果keyFacts为空，添加默认AI排名数据，因为非安省的在上面else里有默认值，所以这里默认空数组是安省的
    if keyFacts.length is 0
      keyFacts.push(
        { key: l10n('AI Ranking'), val: '-', alert: 'AI' },
        { key: l10n('AI Rating'), val: '-', alert: 'AI' }
      )
    # 添加年级/Current Boundary年级信息
    if showCurBnTxt and school.cBns?.length
      cbn = school.cBns[0]
      gf = if cbn.gf < 0 then l10n('KinderGarden') else cbn.gf
      fi = if (cbn.fi and cbn.fi != 0) then l10n('French Immersion') else ''
      if gf and cbn.gt
        grade = "#{gf}-#{cbn.gt}"
      else
        grade = '-'
      if fi.length > 0
        grade += " #{fi}"
      keyFacts.push({ key: l10n('Current Boundary','school'), val: grade })
    else
      grade = if (school.gf and school.gt) then "#{school.gf}-#{school.gt}" else '-'
      keyFacts.push({ key: l10n('Grade','school'), val: grade })
    school.keyFacts = keyFacts
  return schools

###*
# 格式化rmrank关键数据
# @param {Object} school - 学校对象
# @returns {void} - 直接修改school对象,添加rmRankLastYear和rmRankKeyFacts字段
###
exports.formatRmRankKeyFacts = (school,type)->
  return unless ((school.rmRank?.length > 0) and (school.prov_en is 'ON')) or ((school.ivyRank?.length > 0) and (school.private))
  if school.private
    keys = school.ivyRank?[0]
  else
    keys = school.rmRank?[0]
  school.rmRankLastYear = keys?.yearmark?.slice(5)
  rmRank = {}
  reg = /(total|rmRanking|rmScore|rmRankChange)$/
  nodateGread = []
  for k,v of keys
    if reg.test k
      if /rmScore/i.test k
        v = format2Fixed(v/100,1)
        if v is 0
          nodateGread.push k.slice(0,2)
        # 如果g9或者osslt中有有任意一个没有分数，总排名显示n/a
        if /g9/.test(k) and (((not keys.OSSLTrmScore?) or (keys.OSSLTrmScore <= 0)) or ((not keys.g9rmMathScore?) or (keys.g9rmMathScore <= 0)))
          nodateGread.push k.slice(0,2)
      rmRank[k] = v
  # 没有分数的年级，排名和分数都显示N/A
  if nodateGread.length > 0
    nodateGread.forEach (rm) ->
      rmRank[rm + 'rmScore'] = 'N/A'
      rmRank[rm + 'rmRanking'] = 'N/A'
  
  # 法语浸润学校g3年级，如果阅读和写作的Lv3和Lv4都为0，则排名显示N/A
  if rmRank['g3rmRanking'] and (school.fi is 1) and (keys.g3rmReadingLv3 <= 0) and (keys.g3rmWritingLv3 <= 0) and (keys.g3rmReadingLv4 <= 0) and (keys.g3rmWritingLv4 <= 0)
    rmRank['g3rmRanking'] = 'N/A'
  
  delete school.rmRank
  delete school.ivyRank
  school.rmRankKeyFacts = rmRank

studentTitles = ['Studnts','FstLangEn','BornInCanada']
###*
# 格式化eqao学生数据为表格结构
# @param {Object} school - 学校对象
# @returns {void} - 直接在school对象上添加studentsMap字段
# @description 添加studentsMap字段格式:
# {
#   g3/g6/g9/OSSLT: {
#     yearmark: [Studnts,FstLangEn/FstLangFr,BornInCanada]
#   }
# }
# FstLangEn/FstLangFr 为互斥字段，只有一个有值
###
exports.formatEqaoStudents = (school)->
  eqao = school.eqao or []
  if rmEqao = school.rmEqao
    eqao = mergeArrayByYearmark(eqao, rmEqao)
  return unless eqao?.length > 0
  # rmrank中有从表格统计来的数据，以表格数据优先
  if school.rmRank
    eqao = mergeArrayByYearmark(eqao, school.rmRank)
  studentsMap = {}
  isFr = false
  eqao = eqao.slice(0,5)

  for grade in grades
    subMap = {}
    for rm in eqao
      showOrder = []
      # 只处理有'#{grade}rmRanking'的年度
      continue unless rm["#{grade}rmRanking"]?
      for t in studentTitles
        if t is 'FstLangEn'
          if rm["#{grade}FstLangFr"] > 0
            showOrder.push rm["#{grade}FstLangFr"]
            isFr = true
          else if rm["#{grade}FstLangEn"] > 0
            showOrder.push rm["#{grade}FstLangEn"]
          else
            showOrder.push 'N/A'
        else
          showOrder.push rm["#{grade}#{t}"] or 'N/A'
        
      if showOrder.filter((item) -> item not in ['-','N/A','N/R','N/D']).length is 0
        continue
      subMap[rm.yearmark] = showOrder
    studentsMap[grade] = subMap if Object.keys(subMap).length
  if isFr
    school.isFr = 1
  school.studentsMap = studentsMap

###*
# 格式化rmRanking和rmScore数据为表格结构
# @param {Object} school - 学校对象
# @return {Object} rankScoreMap - 包含各年级ranking和score数据的映射对象
###
exports.formatRmRankScore = (school)->
  return unless (school.rmRank?.length > 0) or ((school.ivyRank?.length > 0 ) and (school.private))
  rmrank = school.rmRank?.slice(0,5) or school.ivyRank?.slice(0,5)
  
  rankScoreMap = {}
  formatRmRankByGrade = (grade)->
    subMap = {}
    for rm in rmrank
      showOrder = []
      # 添加score数据,需要除以100并保留2位小数
      score = rm["#{grade}rmScore"]
      if not score?
        continue
      score = format2Fixed(score/100, 2)
      showOrder.push(score)

      # 添加ranking数据
      ranking = rm["#{grade}rmRanking"]
      showOrder.push(ranking)

      # 没有分数的年级，排名和分数都显示N/A
      if score is 0
        showOrder = ['N/A', 'N/A']
      # 如果g9或者osslt中有有任意一个没有分数，总排名显示n/a
      if (grade is 'g9') and (((not rm.OSSLTrmScore?) or (rm.OSSLTrmScore <= 0)) or ((not rm.g9rmMathScore?) or (rm.g9rmMathScore <= 0)))
        showOrder = showOrder.map -> 'N/A'

      # 法语浸润学校g3年级，如果阅读和写作的Lv3和Lv4都为0，则排名显示N/A
      if (grade is 'g3') and (school.fi is 1) and (rm.g3rmReadingLv3 <= 0) and (rm.g3rmWritingLv3 <= 0) and (rm.g3rmReadingLv4 <= 0) and (rm.g3rmWritingLv4 <= 0)
        showOrder[1] = 'N/A'
      subMap[rm.yearmark] = showOrder
    return subMap if Object.keys(subMap).length
    return null
  if school.private
    pri = formatRmRankByGrade('')
    rankScoreMap.pir = pri if pri
  else
    g3 = formatRmRankByGrade('g3')
    rankScoreMap.G3 = g3 if g3 and Object.keys(g3).length > 0
    g6 = formatRmRankByGrade('g6')
    rankScoreMap.G6 = g6 if g6 and Object.keys(g6).length > 0
    g9 = formatRmRankByGrade('g9')
    rankScoreMap.G9 = g9 if g9 and Object.keys(g9).length > 0
  
  school.rankScoreMap = rankScoreMap

###
# 格式化Adjustment数据为表格结构
# @param {Object} school - 学校对象
# @returns {void} - 直接在school对象上添加adjMap字段
# @description 处理学校的调整因子数据，将yearlyScores转换为表格友好的格式
# 输出格式为：
# {
#   year: {
#     average: 平均分数,
#     diff: 分数差异,
#     score: 原始分数,
#     ranking: 排名
#   }
# }
###
adjDetailTitles = ['average','score','diff','ranking']
adjSummaryTitles = ['year_range','times_on_list','weighted_score','overall_ranking']
exports.formatAdjRank = (school)->
  adjDetail = school.adjDetail
  return unless adjDetail?.length > 0
  rankMap = school.rankMap or {}
  yearRange = school.adjSummary?.year_range
  summary = school.adjSummary
  summaryMap = []
  for t in adjSummaryTitles
    if t is 'times_on_list' and not summary[t]?
      summaryMap.push '0'
    else if t is 'overall_ranking'
      summaryMap.push summary.side + ' ' + summary[t]
    else
      if t is 'weighted_score' and summary[t]
        summaryMap.push summary[t] / 100
      else
        summaryMap.push summary[t] or 'N/A'
  scoresMap = {}
  
  # 如果存在年份范围，添加年份为key的空对象
  if yearRange
    [startYear, endYear] = yearRange.split('-')
    if startYear and endYear
      startYear = parseInt(startYear)
      endYear = parseInt(endYear)
      for year in [endYear..startYear]
        scoresMap["#{year-1}-#{year}"] = ['N/A','N/A','N/A','N/A']
        
  # 遍历每个年度的分数记录，提取相关数据
  for score in adjDetail
    subMap = []
    for t in adjDetailTitles
      if t is 'ranking'
        if score.ranking and score.side
          subMap.push((score.side + ' ' + score[t]))
        else
          subMap.push('N/A')
      else
        subMap.push(score[t] or 'N/A')
    # 确保年份按照时间从大到小排序
    if Object.keys(subMap).length
      scoresMap["#{score.year-1}-#{score.year}"] = subMap
      # 对scoresMap进行重新排序，使年份从大到小排列
      # scoresMap = Object.values(scoresMap).sort((a,b)-> b.year.(a.year))

  # 将处理后的数据添加到学校对象中
  rankMap.yearRange = yearRange
  rankMap.adjDetail = scoresMap
  rankMap.adjSummary = summaryMap

  delete school.adjDetail
  delete school.adjSummary
  school.rankMap = rankMap


basicTitles = ['smplPct','ivyTotal','90+Pct','85+Pct','80+Pct']

###*
# 格式化ivyRank数据为表格结构
# @param {Object} school - 学校对象
# @returns {void} - 直接在school对象上添加rankMap字段
# @description 将ivyRank数据处理成表格友好的格式
# 最终格式为:
# {
#   basic: {
#     year: [基本数据数组]
#   },
#   universities: {
#     university: {
#       year: pct
#     }
#   },
#   majors: {
#     major: {
#       year: pct
#     }
#   }
#   years: [年份数组]
# }
###
exports.formatIvyRank = (school)->
  ivyRank = school.ivyRank or []
  return unless ivyRank?.length > 0
  rankMap = school.rankMap or {}
  years = ivyRank.map (rm)->
    return rm.yearmark
# 初始化数据结构
  basic = {}
  universities = {}
  majors = {}

  
  # 处理每一条ivyRank记录
  for record in ivyRank
    yearmark = record.yearmark
    # 处理基本数据
    basicData = []
    for title in basicTitles
      value = record[title] or '0'
      if /Pct$/.test(title) or /%$/.test(title)
        value = "#{(value/100).toFixed(2)}" if value isnt '0'
      basicData.push(value)
    
    if basicData.filter((item) -> item isnt '-').length > 0
      basic[yearmark] = basicData
    
    # 处理大学数据
    if record.topSchools?.length > 0
      for school in record.topSchools
        if school.school and school.pct
          universities[school.school] ?= {}
          universities[school.school][yearmark] = "#{(school.pct/100).toFixed(2)}%"
    
    # 处理专业数据
    if record.majors?.length > 0
      for majorObj in record.majors
        if majorObj.major and majorObj.pct
          # 跳过Total开头的汇总项
          continue if /^Total/.test(majorObj.major)
          majors[majorObj.major] ?= {}
          majors[majorObj.major][yearmark] = "#{(majorObj.pct/100).toFixed(2)}%"
  
  # 将处理后的数据添加到学校对象中
  rankMap.basic = basic
  rankMap.universities = universities if Object.keys(universities).length > 0
  rankMap.majors = majors if Object.keys(majors).length > 0
  rankMap.years = years

  delete school.ivyRank
  school.rankMap = rankMap

###*
# 获取教育局的EQAO ID
# @param {string} board - 教育局名称,格式如:"Private Boards - English (99500)"
# @returns {string|null} 教育局ID
###
exports.getBoardIDEqao = getBoardIDEqao = (board) ->   #"board" : "Private Boards - English (99500)"
  boardID = null
  if board
    boardID = board.match(/\((\d+)\)/)
    if boardID
      boardID = boardID[1]
  return boardID


# 比较yearmark首尾年份是否相同,不管数据库中保存的yearmark中间的分隔符是什么，只比较前后数字.eg: 202-2024和2023-2024
###*
# 比较两个yearmark是否表示相同学年
# @param {string} yearmarkParam - 第一个yearmark
# @param {string} yearmark - 第二个yearmark
# @returns {boolean} 是否表示相同学年
# @description 只比较首尾年份数字是否相同,忽略分隔符
# @example
# judgeIsSameYearmark('2023-2024', '2023–2024') // returns true
###
exports.judgeIsSameYearmark = judgeIsSameYearmark = (yearmarkParam,yearmark)->
  unless yearmarkParam
    return false
  regExcNumber = /\d+/g
  matchesParam = yearmarkParam.match(regExcNumber)
  # NOTE: 一个是hyphen一个是dash
  yearmarkSplit = yearmark.split(/–|-/)
  if matchesParam.length isnt yearmarkSplit.length
    return false
  if matchesParam[0] isnt yearmarkSplit[0]
    return false
  if matchesParam[1] isnt yearmarkSplit[1]
    return false
  return true

###*
# @param {Array|Object} arr1 - 第一个数组或对象
# @param {Array|Object} arr2 - 第二个数组或对象
# @returns {Array} 合并后的数组,按yearmark降序排列
# @description 如果存在相同yearmark的数据,会合并对象属性
###
exports.mergeArrayByYearmark = mergeArrayByYearmark = (arr1, arr2) ->
  if (not arr1) and (not arr2)
    return []
    
  if typeof arr1 is 'object' and (not Array.isArray(arr1))
    arr1 = [arr1]
  if typeof arr2 is 'object' and (not Array.isArray(arr2))
    arr2 = [arr2]
  if not arr1
    return arr2
  if not arr2
    return arr1
  # 创建一个map用于存储yearmark对应的数据
  yearmarkMap = {}
  
  # 处理第一个数组
  for item in arr1
    if item.yearmark
      yearmarkMap[item.yearmark.slice(-4)] = item
      
  # 处理第二个数组,合并相同yearmark的数据
  for item in arr2
    if item.yearmark
      if yearmarkMap[item.yearmark.slice(-4)]
        # 如果存在相同yearmark,合并数据
        yearmarkMap[item.yearmark.slice(-4)] = Object.assign {}, yearmarkMap[item.yearmark.slice(-4)], item
      else
        # 如果不存在,直接添加
        yearmarkMap[item.yearmark.slice(-4)] = item
        
  # 将map转换回数组并返回
  return Object.values(yearmarkMap).sort((a,b)-> b.yearmark.localeCompare(a.yearmark))

###*
 * 匹配学校信息
 * @param {Object} school - eqao网站学校对象
 * @param {Object} found - 数据库中找到的学校对象
 * @returns {Object} 返回匹配结果
 * @property {boolean} schoolMatched - 学校信息是否匹配
 * @property {boolean} gradeMatched - 年级是否匹配
 * @property {boolean} nmAddrMatched - 名称和地址是否匹配
###
exports.matchSchoolInfo = matchSchoolInfo = (school,found,isFraser)->
  schoolMatched = false
  gradeMatched = false
  match = 0
  nmAddrMatch = 0
  total = 0
  if school.zip and found.zip
    total++
    if school.zip is found.zip
      match++
  if school.tel and found.tel
    total++
    if school.tel is found.tel
      match++
  if school.fax and found.fax
    total++
    if school.fax is found.fax
      match++

  # 对比地址
  suaddr = filterSpecialCase(school.addr)
  fuaddr = filterSpecialCase(found.addr)
  if suaddr and fuaddr
    isSameStreet = judgeIsSameStreet suaddr,fuaddr,{city:found.city,xlsCity:school.city}
    isSameCity = judgeIsSameCity school.city,found.city
    if isSameStreet
      # 城市一致或者其他信息一致
      if isSameCity or (match/total >= 0.5)
        match = match + 1.5
        nmAddrMatch++
    else
      debug.info("unmatch ~ uaddr:#{suaddr},fuaddr:#{fuaddr},")
    total++

  # 对比学校名字
  usnm = generateSchoolName(school.nm)
  ufnm = generateSchoolName(found.nm)
  if usnm and ufnm
    total++
    if judgeIsSameSchool usnm, ufnm
      match++
      nmAddrMatch++
    #   debug.info ("match ~ unifySchoolName: #{usnm}, unifyfoundSchoolName: #{ufnm}")
    else
      debug.info ("unmatch ~ unifySchoolName: #{usnm}, unifyfoundSchoolName: #{ufnm}")
  nmAddrMatched = nmAddrMatch/2 is 1
  schoolMatched = match/total >= 0.6

  # 如果是大学，校区不匹配不更新数据
  if school.isCollege
    if (ufnm.indexOf school.campus) is -1
      schoolMatched = nmAddrMatched = false

  # 如果其他信息匹配但是学校和地址不匹配，判断学校名和地址是否有关联，没有关系不更新
  if (not nmAddrMatched) and schoolMatched
    schoolNameArray = usnm.toLowerCase().split ' '
    matchedPartName = 0
    ufnm = ufnm.toLowerCase().replace(/ /g,'')
    for partName in schoolNameArray
      if ufnm.indexOf(partName) > -1
        matchedPartName++
    if matchedPartName < (schoolNameArray.length/2)
      schoolMatched = nmAddrMatched = false

  return {schoolMatched,gradeMatched,nmAddrMatched} if (not schoolMatched) and (not nmAddrMatched)
  # 判断年级
  # 直接更新会出现相同名字不同年级的数据问题(Lakefield District PS (402908)和Lakefield District Secondary School)
  if (found.grd is 'Secondary') or (found.hgh is 1) or (found.gt is 12)
    foundIsHigherGrade = true
  else
    foundIsHigherGrade = false
  if isFraser
    schoolIsHigherGrade = school._id.indexOf('SECONDARY') > -1
  else
    unless school?.eqao?[0]
      school.eqao ?= []
      school.eqao[0] ?= {}
    schoolIsHigherGrade = Object.keys((school.eqao[0])).find((i)-> return /g[9|10]/.test(i))?
  return {schoolMatched,gradeMatched:foundIsHigherGrade is schoolIsHigherGrade,nmAddrMatched}

exports.SchMergeFields = SchMergeFields = [
  'city',
  'prov',
  'addr',
  'IsActive',
  'nm',
  'fi',
  'ele',
  'eng',
  'ib',
  'pub',
  'ap',
  'catholic',
  'mid',
  'art',
  'ef',
  'hgh',
  'gif',
  'sport',
  'tel',
  'fax',
  'zip',
  'loc',
  'bns',
  'gf',
  'gt',
  'bnds',
  'lat',
  'lng',
  'source',
  'sourceId'
]

findSchoolFields = [
  'g3Rating',
  'g6Rating',
  'g9Rating'
]

fraserFields = [
  'fraserId',
  'firank',
  'fitotal',
  'firate'
]

eqaoFields = [
  'eqaoId',
  'rmG3Ranking',
  'rmG6Ranking',
  'rmG9Ranking'
]

###*
 * 过滤并合并学校字段
 * @param {Object} school - 来自findschool.ca的学校数据
 * @param {string} type - 数据类型,可选值:'findSchool'或其他
 * @returns {Object} 过滤后的学校对象,只包含必要字段
 * @description 
 * 1. 根据type选择基础字段集
 * 2. 如果有fraserId,添加Fraser相关字段
 * 3. 如果有eqaoId,添加EQAO相关字段
 * 4. 过滤电话和传真号码
 * 5. 处理地址信息
###
exports.filterMergeFields = filterMergeFields = (school, type) ->
  # 初始化结果对象
  result = {}
  
  # 根据类型确定字段集
  fields = if school.source is 'findschool'
    SchMergeFields.concat(findSchoolFields)
  else 
    SchMergeFields

  # 添加额外字段
  fields = fields.concat(fraserFields) if school.fraserId
  fields = fields.concat(eqaoFields) if school.eqaoId

  # 复制存在的字段
  for field in fields when school[field]?
    result[field] = school[field]

  # 处理联系方式
  result.tel = filterOtherThanTel(result.tel) if result.tel
  result.fax = filterOtherThanTel(result.fax) if result.fax

  # 处理地址
  result.addr = separateUnitFromAddr(result.addr).addr
  result.uaddr = unifyAddress(result)

  return result

debugHelper = require '../lib/debug'
propertiesHelper = require './properties'
{ inputToDateNum, formatUTC2EST } = require '../lib/helpers_date'
{ abbrExposure } = require '../lib/propAddress'
{ getCityAreaMap,getAreaType } = require '../libapp/boundaryHelper'
{ doMapping,formatDaddr,statusLstMapping,convertLotSize,formatVturl } = require './impCommon'
{ formatMfee } = require './impFormat'
AREA_TYPE_MAP = getAreaType()
CITY_AREA_MAP = getCityAreaMap()

debug = debugHelper.getDebugger()
METER2SQFT = 10.76391
ACRES2SQFT = 43560
HECTARES2SQFT = 107639.10417
METER2FT = 3.28084
# 定义单位转换映射
LOTSZ_UNIT_MAP = {
  'Acres': ACRES2SQFT
  'Hectares': HECTARES2SQFT
  'Metres': METER2SQFT
  'SqMDivisible': METER2SQFT
  'SquareMeters': METER2SQFT
  'Feet': 1
  'SqFtDivisible': 1
  'SquareFeet': 1
}

# 基础字段映射
RESOFieldMap =
  ApproximateAge: 'age'
  AssociationName: 'condo_corp'
  BalconyType: 'blcny'
  Basement: 'bsmt'
  BoardPropertyType: 'propType'
  Amps: 'amps'
  ApartmentNumber: 'apt_num'
  AssessmentYear: 'ass_year'
  BathroomsTotalInteger: 'tbthrms'
  BedroomsAboveGrade: 'bdrms'
  BedroomsBelowGrade: 'br_plus'
  BedroomsTotal: 'tbdrms'
  BuildingName: 'bldg_name'
  CondoCorpNumber: 'corp_num'
  #closeDate is 未来时间
  CloseDate: 'cldd'
  ClosePrice: 'sp'
  ConstructionMaterials: 'constr'
  Cooling: 'ac'
  Country: 'cnty'
  CrossStreet: 'crsst'
  CityRegion: 'cmty'
  DaysOnMarket: 'dom'
  Directions: 'dir_desc'
  DirectionFaces: 'fce'
  Exposure: 'fce'
  ExpirationDate: 'exp'
  Furnished: 'frnshd'
  FoundationDetails: 'constr'
  HoldoverDays: 'holdover'
  KitchensTotal: 'kch'
  ListingKey: 'sid'
  ListingContractDate: 'ld'
  UnavailableDate: 'unavail_dt'
  # SoldConditionalEntryTimestamp:''
  PurchaseContractDate:'sldd' #this value will be set in setImportDate
  # SoldEntryTimestamp:''
  MajorChangeTimestamp:'origSpcts' #?
  ModificationTimestamp:'lup'
  OriginalEntryTimestamp:'ts'
  # PriceChangeTimestamp:'pcts'
  Latitude: 'lat'
  Longitude: 'lng'
  LivingAreaRange: 'sqft'
  City: 'city'
  # CountyOrParish:'city'# -> region
  ListPrice: 'lp'
  LotDepth: 'depth'
  LotWidth: 'front_ft'
  CoveredSpaces: 'gr'
  ParkingSpaces: 'park_spcs'
  ParkingType1: 'park_desig'
  ParkingType2: 'park_desig_2'
  ParkingTotal: 'tgr'
  GarageType: 'gatp'
  PhotosChangeTimestamp: 'phomt'
  PossessionDate: 'poss_date'
  PossessionDetails: 'psn'
  PossessionType: 'poss_type'
  PostalCode: 'zip'
  PetsAllowed: 'pets'
  PortionPropertyLease: 'ptnLsCmt'
  ParcelNumber: 'parcel_id'
  PublicRemarks: 'm'
  PreviousListPrice: 'origLp'
  PropertyManagementCompany: 'prop_mgmt'
  RentalItems: 'rental_items'
  StateOrProvince: 'prov'
  StreetDirPrefix: 'st_dirpfx'
  StreetName: 'st'
  StreetNumber: 'st_num'
  StreetSuffix: 'st_sfx'
  StreetSuffixCode: 'st_sfx'
  StreetDirSuffix: 'st_dir'
  TaxAnnualAmount: 'tax'
  TaxYear: 'taxyr'
  TaxAssessedValue: 'asmt'
  TaxesExpense: 'tax'
  FractionalOwnershipYN: 'frcOwn'
  UnitNumber: 'unt'
  UnparsedAddress: 'origAddr'
  Zoning: 'zoning'
  PoolFeatures: 'pool'
  CableYNA: 'tv'
  LotSizeDimensions: 'dim'
  LotIrregularities: 'irreg'
  WaterSource: 'water'
  LockerNumber: 'lock_num'
  LockerLevel: 'lock_lvl'
  LockerUnit: 'locker_unit'
  SoldArea: 'sold_area'
  OriginalListPrice: 'olp'
  PrivateRemarks: 'bm'
  OriginatingSystemKey: 'origSid'
  OriginatingSystemName: 'resoSrc'
  # Exclusions:'excl'

NUM_DATE_FIELDS = [
  'poss_date', 'sldd', 'onD', 'offD', 'ld', 'exp', 'picTs',
  'ListingContractDate', 'input_date', 'dt_ter', 'lud', 'unavail_dt', 'cd', 'cldd'
]

###*
 * 维护费用包含项目的映射关系
 * @constant
 * @type {Object}
 * @property {string} CAC Included - 中央空调包含标识,映射为cac_inc
 * @property {string} Common Elements Included - 公共设施包含标识,映射为comel_inc
 * @property {string} Heat Included - 供暖包含标识,映射为heat_inc
 * @property {string} Hydro Included - 水电包含标识,映射为hydro_inc
 * @property {string} Water Included - 供水包含标识,映射为water_inc
 * @property {string} Parking Included - 停车包含标识,映射为prkg_inc
 * @property {string} Cable TV Included - 有线电视包含标识,映射为tv
###
MFEE_INCLUDE_MAP =
  'CAC Included': 'cac_inc'
  'Common Elements Included': 'comel_inc'
  'Heat Included': 'heat_inc'
  'Hydro Included': 'hydro_inc'
  'Water Included': 'water_inc'
  'Parking Included': 'prkg_inc'
  'Cable TV Included': 'tv'
  # 'Building Insurance Included',
  # 'Condo Taxes Included',
  # 'MaintenanceGrounds',
  # 'MaintenanceStructure',
  # 'SnowRemoval',
  # 'Trash',


formatMAndLinkYn = (record)->
  record.m ?= ''
  if record.extras
    record.m += record.extras
    delete record.extras
  if record.PublicRemarksExtras
    record.m += record.PublicRemarksExtras
    delete record.PublicRemarksExtras
  record.link_yn = if record.LinkYN then 'Y' else 'N'
  delete record.LinkYN
  return record

# General function to process a field from list to string
processFieldFromListToString = (oldValueList) ->
  if not Array.isArray(oldValueList)
    return null

  if oldValueList.length > 1
    result = oldValueList.join(',')
  else
    result = oldValueList[0]
  return result

#  format ts, lup used to be timestamp_sql/BridgeModificationTimestamp/['@attributes']?.LastUpdated
formatTimestamp = (record)->
  # field value from orig is string
  record.ts = new Date(record.ts)
  record.lup = new Date(record.lup)
  record.mt ?= record.lup
  for fld in NUM_DATE_FIELDS
    if record[fld]? and ('string' is typeof record[fld])
      record[fld] = inputToDateNum(record[fld])

###
 * 格式化状态和列表状态
 * @param {Object} record - 需要格式化的记录对象
 * @returns {void} - 直接修改传入的记录对象
###
exports.formatStatusAndLst = formatStatusAndLst = (record) ->
  if not (record.MlsStatus or record.ContractStatus)
    debug.error "RESO prop:#{record._id} no MlsStatus and ContractStatus"
    return

  # 优先使用MlsStatus
  if mappingInfo = statusLstMapping[record.MlsStatus]?['TRB']
    record.status = mappingInfo.status
    if mappingInfo.lst
      record.lst = mappingInfo.lst
    if mappingInfo.dllst
      record.dllst = mappingInfo.dllst
      delete record.lst
    delete record.ContractStatus
  # 如果MlsStatus不存在，则使用ContractStatus
  else if record.ContractStatus
    if record.ContractStatus is 'Available'
      record.status = 'A'
      record.lst = 'New'
    else if record.ContractStatus is 'Unavailable'
      record.status = 'U'
      record.lst = 'Unavailable'
    else
      debug.error "RESO prop:#{record._id} formatStatusAndLst error, unknown ContractStatus:#{record.ContractStatus}"
    delete record.ContractStatus
  else
    debug.error "RESO prop:#{record._id} , unknown MlsStatus:#{record.MlsStatus},ContractStatus:#{record.ContractStatus}"


# 格式化saletp
formatSaleType = (record) ->
  if /Sale/i.test(record.TransactionType)
    record.saletp = ['Sale']
  else if /Lease/i.test(record.TransactionType)
    record.saletp = ['Lease']
    record.lpr = record.lp if record.lp? and (not record.lpr?)
    delete record.lp
  else
    debug.error "RESO prop:#{record._id} formatSaleType error, unknown TransactionType:#{record.TransactionType}"
  delete record.TransactionType

PROPERTYSUBTYPE_PTYPE2_MAP = {
  'Att/Row/Townhouse': ['Townhouse','Attached'],
  'Co-Ownership Apartment': ['Apartment','Co-Ownership Apt'],
  'Co-op Apartment': ['Apartment','Co-Op Apt'],
  'Commercial Retail': ['Retail'],
  'Common Element Condo': ['Apartment','Comm Element Condo'],
  'Condo Apartment': ['Apartment'],
  'Condo Townhouse': ['Townhouse','Attached'],
  'Cottage': ['House','Cottage'],
  'Detached': ['House','Detached'],
  'Detached Condo': ['House','Detached'],
  'Duplex': ['House','Duplex'],
  'Farm': ['Farm'],
  'Fourplex': ['House','Fourplex'],
  'Industrial': ['Industrial'],
  'Investment': ['Other','Investment'],
  'Land': ['Land'],
  'Leasehold Condo': ['Apartment'],
  'Link': ['House','Link'],
  'Locker': ['Locker'],
  'Lower Level': ['Other'],
  'MobileTrailer': ['Other','Mobile/Trailer'],
  'Multiplex': ['Other','Multiplex'],
  'Office': ['Office'],
  'Other': ['Other'],
  'Parking Space': ['Parking'],
  'Room': ['Room'],
  'Rural Residential': ['House'],
  'Sale Of Business': ['Business'],
  'Semi-Detached ': ['House','Semi-Detached'],
  'Semi-Detached Condo': ['House','Semi-Detached'],
  'Shared Room': ['Room','Shared Room'],
  'Store W Apt/Office': ['Store','Office','Store With Apt/Office'],
  'Triplex': ['House','Triplex'],
  'Upper Level': ['Other','Upper Level'],
  'Vacant Land': ['Land'],
  'Vacant Land Condo': ['Land']
}

BOARD_PROPERTY_TYPE_PTYPE2_MAP = {
  'Com': 'Commercial',
  'Condo': 'Condo',
  'Free': 'FreeHold'
}

# 格式化房产类型
formatPtype = (record) ->
  record.ptype2 ?= []
  if /Residential/i.test(record.PropertyType)
    record.ptype = 'r'
  else if /Commercial|Business/i.test(record.PropertyType)
    record.ptype = 'b'
  else
    record.ptype = 'o'
    if record.PropertyType
      record.ptype2.push record.PropertyType

  # ArchitecturalStyle = Bungalow, add to ptype2
  if (record.ptype is 'r') and (record.ArchitecturalStyle?.length)
    record.ptype2 = propertiesHelper.trebResidentialStyle2Ptype2 record.ArchitecturalStyle,record.ptype2

  if record.PropertySubType
    if tpArr = PROPERTYSUBTYPE_PTYPE2_MAP[record.PropertySubType]
      record.ptype2 = record.ptype2.concat tpArr
    else
      record.ptype2.push record.PropertySubType

  if (not record.ptype2?.length) and (BOARD_PROPERTY_TYPE_PTYPE2_MAP[record.propType])
    record.ptype2.push BOARD_PROPERTY_TYPE_PTYPE2_MAP[record.propType]
      
  record.ptype2 = Array.from(new Set(record.ptype2))
  
  delete record.PropertySubType
  delete record.PropertyType

#media could be a file, do not show file yet
isFile = (media={})->
  # if /application|pdf|audio|mpeg|word/ig.test media.tp
  #   return true
  # false
  if /image|jpeg/.test media.tp
    return false
  true
#media could be a Deleted
# isActive = (media={})->

#media could be private
# isPrivate = (media={})->
#   permission = media.permission or []
#   if /public/ig.test permission
#     return false
#   true

# media sizeDesc could be Thumbnail，Medium，Large，Largest，LargestNoWatermark


# generate phoUrls from media
generatePhoUrls = (record) ->
  if not record.media
    # delete record.media
    # debug.error 'generatePhoUrls error, no media', record._id
    return
  phoUrls = []
  for item,i in record.media
    # ignore file media
    # TODO: 下版本需要保存到properties,不展示到前端
    if isFile(item) #or isPrivate(item)
      continue
    # if not isActive(item)
    #   continue

    phoUrls.push({
      id: item.id,
      url: item.url or item.MediaURL
      status: item.status
      permission: item.perm or item.permission
      w: item.w
      h: item.h
      m: item.m
      sizeDesc: item.sizeDesc
    })
  record.phoUrls = phoUrls
  # 计算不同id的数量作为pho值(图片数量)
  uniqueIds = new Set(phoUrls.map((p) -> p.id))
  record.pho = uniqueIds.size
  delete record.media

# 格式化rooms
formatRms = (record) ->
  if not record.propertyRoom
    return
  rooms = []
  roomFields = ['t','l','m','a','au','wlu','features','asrc','wlsrc']
  roomWLFields = ['width','length']
  for room in record.propertyRoom
    _room = {}
    for field in roomFields
      if room[field]?
        _room[field] = room[field]
    if room.width?
      _room.w = room.width
      _room.w_ft = propertiesHelper.getFloat(room.width * METER2FT)
    if room.length?
      _room.h = room.length
      _room.h_ft = propertiesHelper.getFloat(room.length * METER2FT)
    rooms.push(_room)
  record.rms = rooms
  delete record.propertyRoom


# 处理open house的日期时间
formatOpenHouseDateTime = (dateString) ->
  # UTC iso date string instead of local date time
  # TRBW11901690, {f: '2025-01-04T19:00:00Z',t: '2025-01-04T21:00:00Z',}
  if /\dZ$/.test dateString
    return formatUTC2EST(dateString)
  # Split the date and time components
  [datePart, timePart] = dateString.split 'T'
  # Split the time component to remove the milliseconds and timezone if any
  [time] = timePart.split '.'
  # Combine the date and time parts into the desired format
  formattedDate = "#{datePart} #{time.slice(0, 5)}"
  formattedDate

# 格式化open house
# {
#   "OpenHouseKey": "E11882086-2",
#   "f": "2024-12-15T19:00:00Z",
#   "t": "2024-12-15T21:00:00Z",
#   "status": "Active",
#   "tp": "Public"
# }
module.exports.formatOpenHouse = formatOpenHouse = (record) ->
  if not record.openHouse
    return
  openHouseDetails = []
  for oh in record.openHouse
    detail = {
      f: formatOpenHouseDateTime(oh.f),
      t: formatOpenHouseDateTime(oh.t),
      # l: oh.l,
      tp: oh.tp,
      action: if oh.status is 'Active' then true else false
    }
    openHouseDetails.push(detail)
  record.ohz = openHouseDetails
  delete record.openHouse

# 处理RESO特有的维护费用包含项目逻辑
processResoMfeeIncludes = (record) ->
  # 处理维护费用包含的项目
  if Array.isArray(record.mfeeInc) and record.mfeeInc.length > 0
    for item in record.mfeeInc
      if item and (val = MFEE_INCLUDE_MAP[item])
        record[val] = 1
  delete record.AssociationFee
  delete record.AssociationFeeFrequency
  delete record.AssociationFeeIncludes


# 转换面积的辅助函数
convertArea = (value, unit) ->
  return unless value and unit
  tmpValue = propertiesHelper.getFloat(value)
  multiplier = LOTSZ_UNIT_MAP[unit]
  if multiplier?
    propertiesHelper.getFloat(tmpValue * multiplier)
  else
    debug.warn "Unknown unit: #{unit}"
    tmpValue

###
# @description 将不同单位的土地面积统一转换为平方英尺
# @param {Object} record - 包含土地面积相关字段的记录对象
# @return {Object} 处理后的记录对象，包含统一单位的lotsz和lotszUt字段
###
formatLandSize = (record) ->
  # 处理面积转换
  if record.LotSizeArea
    if record.LotSizeAreaUnits
      record.lotsz = convertArea(record.LotSizeArea, record.LotSizeAreaUnits)
    else if record.LotSizeUnits
      record.lotsz = convertArea(record.LotSizeArea, record.LotSizeUnits)
    else
      debug.error "Unknown lot size unit: #{record.LotSizeUnits} for property #{record.sid}"
      record.lotsz = propertiesHelper.getFloat(record.LotSizeArea)
      record.lotsz = convertLotSize(record.lotsz)
  else if record.front_ft and record.depth
    record.lotsz = propertiesHelper.getFloat(record.front_ft * record.depth)
    if record.LotSizeUnits
      record.lotsz = convertArea(record.lotsz, record.LotSizeUnits)
    else
      debug.error "Unknown lot size unit: #{record.LotSizeUnits} for property #{record.sid}"
      record.lotsz = convertLotSize(record.lotsz)
    record.dim = "#{record.front_ft} * #{record.depth}"

  # 设置最终的单位和清理原始字段
  if record.lotsz?
    record.lotszUt = 'ft²'
    record.lotsz_code = 'Feet'
    delete record.LotSizeArea
    delete record.LotSizeAreaUnits
    delete record.LotSizeUnits
  return

###*
# 格式化经纪人信息
# @description 处理房源的经纪人和中介公司信息
# 1. 将 ListOfficeName 转换为 rltr 字段
# 2. 只有当办公室有完整信息时才添加到 la 数组
# 3. 支持处理主要经纪人、共同经纪人和买方经纪人信息
# @param {Object} record - 待处理的房源记录
###
formatListingAgent = (record) ->
  # 设置 rltr 字段
  record.rltr = record.ListOfficeName if record.ListOfficeName
  
  # 创建办公室对象的辅助函数
  createOfficeObject = (officeName, officeKey, officeFax, officePhone, officeAOR) ->
    return null unless (officeName and (officeKey or officeFax or officePhone or officeAOR))
    office = {nm: officeName}
    office.id = officeKey if officeKey
    office.fax = officeFax if officeFax
    office.tel = officePhone if officePhone
    office.aor = officeAOR if officeAOR
    return office
  
  # 创建经纪人对象的辅助函数
  createAgentObject = (agentName, agentKey, agentPhone, agentAOR) ->
    return null unless (agentName or agentKey) # 至少需要名字或ID之一
    agent = {}
    agent.nm = agentName if agentName
    agent.id = agentKey if agentKey
    agent.tel = agentPhone if agentPhone
    agent.aor = agentAOR if agentAOR
    return if Object.keys(agent).length then agent else null
  
  # 创建经纪人或合作经纪人
  createLaOrCoop = (isLa) ->
    prefixes = if isLa then ['List','CoList'] else ['Buyer','CoBuyer']
    result = []
    for prefix in prefixes
      # 获取office信息
      office = createOfficeObject(
        record["#{prefix}OfficeName"],
        record["#{prefix}OfficeKey"],
        record["#{prefix}OfficeFax"],
        record["#{prefix}OfficePhone"],
        record["#{prefix}OfficeAOR"]
      )
      if office
        # 获取经纪人信息
        agent = createAgentObject(
          record["#{prefix}AgentFullName"],
          record["#{prefix}AgentKey"],
          record["#{prefix}AgentDirectPhone"],
          record["#{prefix}AgentAOR"]
        )
        # 如果有agent信息，添加到office对象中
        office.agnt = [agent] if agent
        result.push office
    return result
  
  # la
  record.la = createLaOrCoop(true)
  # coop
  record.coop = createLaOrCoop(false)
  
  # 删除空数组
  delete record.la if (record.la?.length is 0)
  delete record.coop if (record.coop?.length is 0)


formatWashRooms = (record)->
  wrms = []
  for i in [1..5]
    if t = record["WashroomsType#{i}"]
      washroom = {
        t: propertiesHelper.getInt t
      }
      if l = record["WashroomsType#{i}Level"]
        washroom.l = l
      if p = record["WashroomsType#{i}Pcs"]
        washroom.p = propertiesHelper.getInt p
      wrms.push washroom
  if wrms.length
    record.bths = wrms

cleanRecord = (record) ->
  deletePatterns = [
    /^ListOffice.*/,
    /^ListAgent.*/,
    /^CoListOffice.*/,
    /^CoListAgent.*/,
    /^BuyerOffice.*/,
    /^BuyerAgent.*/,
    /^CoBuyerAgent.*/,
    /^CoBuyerOffice.*/,
    /^media$/   # already saved in phoUrls,pho, thumbUrl.
  ]
  # Iterate over the fields in the record
  for fieldName of record
    for pattern in deletePatterns
      if pattern.test(fieldName)
        delete record[fieldName]
        break
  return record

formatBuilding = (record) ->
  record.Building ?= {}
  building = {}
  # Map fields directly using an object
  fieldMappings = {
    AssociationAmenities: 'Amenities'
    Basement: 'BasementDevelopment'
    ArchitecturalStyle: 'ArchitecturalStyle'
    FireplaceYN: 'FireplacePresent'
    BathroomsTotalInteger: 'BathroomTotal'
    BedroomsAboveGrade: 'BedroomsAboveGround'
    BedroomsBelowGrade: 'BedroomsBelowGrade'
    BedroomsTotal: 'BedroomsTotal'
    Cooling: 'CoolingType'
    ExteriorFeatures: 'ExteriorFinish'
    HeatType: 'HeatingType'
    HeatSource: 'HeatingFuel'
    BuildingAreaTotal: 'TotalFinishedArea'
    Water: 'UtilityWater'
    PropertySubType: 'Type'
  }
  # Process fields that need string conversion
  stringFields = ['Basement', 'ArchitecturalStyle', 'Cooling', 'ExteriorFeatures',
                 'HeatType', 'HeatSource', 'PropertySubType', 'AssociationAmenities']
  for srcField, destField of fieldMappings
    if record[srcField]?
      if srcField in stringFields
        building[destField] = processFieldFromListToString(record[srcField])
      else
        building[destField] = record[srcField]
      delete record[srcField]

  record.Building = building

formatUtilities = (record) ->
  if record.Utilities
    if record.Utilities in ['Available', 'Yes']
      record.utilities = 'Y'
    else if record.Utilities in ['None', 'Unknown']
      record.utilities = 'N'
    delete record.Utilities

formatFurnished = (record) ->
  return if not record.Furnished
  if record.Furnished is 'Furnished'
    record.frnshd = 1
  else if record.Furnished is 'Unfurnished'
    record.frnshd = 0
  else if record.Furnished is 'Partially'
    record.frnshd = 0.5
  delete record.Furnished



###
检查并修改 DOM (Days on Market) 字段
当 DOM 为负数时，将其保存到 origDom 字段并删除 dom 字段
在 addTags 与 获取房源时会重新计算 dom
@param {Object} record - 房源记录对象
###
checkAndReviseDom = (record)->
  # 检查 dom 字段是否存在且为负数
  if record.dom? and record.dom < 0
    debug.error "#{record._id} checkAndReviseDom error, dom:#{record.dom} is invalid"
    # 保存原始 DOM 值到 origDom 字段
    record.origDom = record.dom
    # 删除 dom 字段,后续会在 addTags 和获取房源时重新计算
    delete record.dom
    
# 1去掉输入的NA字段，会导致geocoding出问题
# 2处理area/city
formatAddr = (record,srcRecord)->
  for i in ['st','zip','st_sfx','unt']
    if record[i] in ['NA','N/A']
      record[i] = ''
  # for some record city/CountyOrParish mapping incorrect,
  # eg: {    CountyOrParish: 'Ottawa',    City: 'Lower Town - Sandy Hill',     CityRegion: '4004 - Sandy Hill',}
  # CityRegion = cmty, city = subCity
  record = Object.assign record,getCityAndArea(srcRecord)
  # console.log '++++',record

# src record has different meaning for area and city
module.exports.getCityAndArea = getCityAndArea = (prop)->
  # this value could be city or area
  CountyOrParish = prop.CountyOrParish
  # this value could be city or area
  City = prop.City
  #invoke boundaryHelper
  type = AREA_TYPE_MAP[CountyOrParish]
  # console.log '++++',type,CountyOrParish
  if type is 'single-tiers'
    return {city:CountyOrParish, area:CountyOrParish, subCity:City}
  # CITY_AREA_MAP[type][City]
  return {city:City, area:CountyOrParish}

# 主转换函数
exports.convertFromRESO = convertFromRESO = ({srcRecord}, cb) ->
  try
    ret = doMapping srcRecord,RESOFieldMap

    # 格式化各种字段
    formatMAndLinkYn ret
    formatTimestamp ret
    formatStatusAndLst ret
    formatSaleType ret
    formatPtype ret
    # 调用通用的formatVturl函数，传入TREB RESO的字段数组
    formatVturl ret, ['VirtualTourURLUnbranded', 'VirtualTourURLUnbranded2', 'VirtualTourURLBranded', 'VirtualTourURLBranded2']
    # generatePhoUrls ret
    formatRms ret
    formatOpenHouse ret
    formatLandSize ret
    formatMfee ret
    processResoMfeeIncludes ret
    formatListingAgent ret
    # formatBuilding ret
    formatUtilities ret
    formatFurnished ret
    formatDaddr ret
    formatAddr ret,srcRecord
    formatWashRooms ret
    checkAndReviseDom ret
    # 设置默认值
    ret.cnty ?= 'CA'
    ret.prov ?= 'ON'
    ret.fce = abbrExposure ret.fce
    ret.src ?= 'TRB' # 如果更换了这个值，记得修改statusLstMapping
    ret.origSid = ret.origSid.toString()
    ret.id ?= ret.src + ret.sid
    if ret.tbthrms?
      ret.bthrms ?= ret.tbthrms
    cleanRecord ret
  catch err
    debug.error 'convert err', err
    return cb err

  debug.debug 'convert ret', ret
  cb null, ret
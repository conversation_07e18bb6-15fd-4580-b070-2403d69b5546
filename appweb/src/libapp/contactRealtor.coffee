objectPath = require('object-path')
gBusinessLogic = {}
#Generate all accept keys. if key is not in business logic and not valid,do not process
# else if key is not in business logic, check the key fall into which default category.
# top to bottom rule.
debugHelper = require '../lib/debug'
{PROJECT_BUSINESS_LOGIC} = require './contactRealtorProject'
{RM_LISTING_BUSINESS_LOGIC} = require './contactRealtorRmListing'
{MLS_BUSINESS_LOGIC} = require './contactRealtorMLS'
{EVALUATION_BUSINESS_LOGIC} = require './contactRealtorEvaluation'
propertiesHelper = require './properties'
util = require 'util'
libUser = require './user'
{getPropShareUrl} = require './propWeb'
{unifyAddress,formatLng,stripEmojis} = require '../lib/helpers_string'
cityHelper = require '../lib/cityHelper'
contactRealtorConstant = require './contactRealtorConstant'
common = require './common'
{TRUSTED_ASSIGN_BUSINESS_LOGIC} = require './contactRealtorTrustedAssign'
{BUY_TRUSTED_ASSIGN_BUSINESS_LOGIC} = require './contactRealtorBuyTrustedAssign'
{SALE_TRUSTED_ASSIGN_BUSINESS_LOGIC} = require './contactRealtorSaleTrustedAssign'
{ASSIGNMENT_BUSINESS_LOGIC} = require './contactRealtorAssignment'

CONTACT_REALTOR_VALUES = common.CONTACT_REALTOR_VALUES
TITLE_STR = common.TITLE_STR
debug = debugHelper.getDebugger()

config = null
module.exports._config_sections = ['contact']
module.exports._config = _config = (cfg)->
  if not config
    config = cfg

KEY_GENERATING_RULES = [
  {
    nm: 'src',
    vals: [
      CONTACT_REALTOR_VALUES.APP,
      CONTACT_REALTOR_VALUES.WEB,
      CONTACT_REALTOR_VALUES.WECHAT
    ]
  }

  {
    nm: 'page',
    vals: common.FORM_PAGE
  },
  {
    nm: 'isloggedIn',
    vals:[
      CONTACT_REALTOR_VALUES.LOGGEDIN
      CONTACT_REALTOR_VALUES.NOT_LOGGEDIN
    ]
  },
  {
    nm: 'role',
    vals:[
      #realtor 不可以有follow
      CONTACT_REALTOR_VALUES.NONE_VIP_REALTOR,
      CONTACT_REALTOR_VALUES.VIP_REALTOR,
      # CONTACT_REALTOR_VALUES.VIP_ALLIANCE, 不给vip 商家显示。
      CONTACT_REALTOR_VALUES.CUSTOMER_FOLLOW_NONE_RM_REALTOR
      CONTACT_REALTOR_VALUES.CUSTOMER_FOLLOW_RM_REALTOR,
      CONTACT_REALTOR_VALUES.CUSTOMER_FOLLOW_NONE
    ]
  },
  {
    nm: 'hasWechat',
    vals:[
      CONTACT_REALTOR_VALUES.HAS_WECHAT,
      CONTACT_REALTOR_VALUES.NO_WECHAT
    ]
  },
  {
    nm: 'owner',
    vals:[
      CONTACT_REALTOR_VALUES.NO_OWNER
      CONTACT_REALTOR_VALUES.OWNER_NOT_IN_CRM,
      CONTACT_REALTOR_VALUES.OWENR_IN_CRM_EQUAL_TO_FOLLOW_AGENT,
      CONTACT_REALTOR_VALUES.OWENR_IN_CRM_NOT_EQUAL_TO_FOLLOW_AGENT
    ]
  },
  {
    nm: 'propCity',
    vals:[
      CONTACT_REALTOR_VALUES.RM_CONTROL_CITY,#需要city列表，暂时use gta
      CONTACT_REALTOR_VALUES.RM_COOP_CITY,#rmfwng, should not happen for now. delay test.
      CONTACT_REALTOR_VALUES.RM_NONE_COOP_CITY,
    ]
  }
]


ALL_CONTACT_KEYS = {}
rule0 = KEY_GENERATING_RULES[0]
for val in rule0.vals
  ALL_CONTACT_KEYS[val] = 1
for i in [1..KEY_GENERATING_RULES.length-1]
  ret = {}
  rule = KEY_GENERATING_RULES[i]
  for val in rule.vals
    for k in Object.keys(ALL_CONTACT_KEYS)
      ret[k+'.'+val] = 1
  ALL_CONTACT_KEYS = ret

# a.b|c.d|e ->return array [a.b.d,a.b.e,a.c.d,a.c.e]
# deal with or in path
generateAllPath=(logicPath)->
  arr = logicPath.split('.')
  return logicPath unless arr?.length
  ret = arr[0].split('|')
  len = arr.length
  i=1
  # debug.debug 'ret',ret, len,i
  while i < arr.length
    tempRet = []
    currentPaths = arr[i].split('|')
    # debug.debug 'currentPaths',currentPaths
    for currentPath in currentPaths
      for p in ret
        tempRet.push "#{p}.#{currentPath}"
    ret = tempRet
    i++
  return ret
# console.log('vipRealtor｜noneVipRealtor'.split('｜'))
# console.log 'generateAllPath'
# console.log generateAllPath('app.project.loggedIn.vipRealtor|noneVipRealtor.default')


# debug.debug 'ALL_CONTACT_KEYS', ALL_CONTACT_KEYS
setBusinessLogicByPage =(page,businessLogics)->
  for logic in businessLogics
    # debug.debug 'logic.path',logic.path
    paths = generateAllPath(logic.path)
    # debug.debug 'paths',paths
    for p in paths
      objectPath.set(gBusinessLogic,
        p,
        logic.action)


STUDENT_RENTAL_LOGIC  = [{
  path: "#{CONTACT_REALTOR_VALUES.APP}|#{CONTACT_REALTOR_VALUES.WEB}.\
  #{CONTACT_REALTOR_VALUES.STUDENT_RENTAL}.\
  #{CONTACT_REALTOR_VALUES.DEFAULT}",
  action:  contactRealtorConstant.SHOW_FORM.SEND_TO_CRM_MLS
}]

setLandLordRentBusinessLogic = ()->
  objectPath.set(
    gBusinessLogic,
    "#{CONTACT_REALTOR_VALUES.APP}.\
    #{CONTACT_REALTOR_VALUES.LANDLORD_RENT}.\
    #{CONTACT_REALTOR_VALUES.NOT_LOGGEDIN}.#{CONTACT_REALTOR_VALUES.DEFAULT}",
    {redirectLogin:1}
  )
  objectPath.set(
    gBusinessLogic,
    "#{CONTACT_REALTOR_VALUES.APP}.\
    #{CONTACT_REALTOR_VALUES.LANDLORD_RENT}.\
    #{CONTACT_REALTOR_VALUES.LOGGEDIN}.#{CONTACT_REALTOR_VALUES.DEFAULT}",
    {
      UIType: [CONTACT_REALTOR_VALUES.AGENT_CARD],
      agentType: CONTACT_REALTOR_VALUES.LISTING_AGENT,
      title: TITLE_STR.CONTACT_LANDLORD
    }
  )

setStudentRentalLogic = ()->
  setBusinessLogicByPage CONTACT_REALTOR_VALUES.STUDENT_RENTAL, STUDENT_RENTAL_LOGIC

setProjectBusinessLogic = ()->
  setBusinessLogicByPage CONTACT_REALTOR_VALUES.PROJECT, PROJECT_BUSINESS_LOGIC

setRmListingBusinessLogic = ()->
  setBusinessLogicByPage CONTACT_REALTOR_VALUES.RM_LISTING, RM_LISTING_BUSINESS_LOGIC

setMLSBusinessLogic = ()->
  setBusinessLogicByPage CONTACT_REALTOR_VALUES.MLS, MLS_BUSINESS_LOGIC

setEvaluateBusinessLogic = ()->
  setBusinessLogicByPage CONTACT_REALTOR_VALUES.EVALUATION, EVALUATION_BUSINESS_LOGIC

setTrustedAssignmentBusinessLogic = ()->
  setBusinessLogicByPage CONTACT_REALTOR_VALUES.TRUSTED_ASSIGNMENT, TRUSTED_ASSIGN_BUSINESS_LOGIC

setBuyTrustedAssignmentBusinessLogic = ()->
  setBusinessLogicByPage CONTACT_REALTOR_VALUES.BUY_TRUSTEDASSIGNMENT, BUY_TRUSTED_ASSIGN_BUSINESS_LOGIC

setSaleTrustedAssignmentBusinessLogic = ()->
  setBusinessLogicByPage CONTACT_REALTOR_VALUES.SALE_TRUSTEDASSIGNMENT, SALE_TRUSTED_ASSIGN_BUSINESS_LOGIC

setAssignmentBusinessLogic = ()->
  setBusinessLogicByPage CONTACT_REALTOR_VALUES.ASSIGNMENT, ASSIGNMENT_BUSINESS_LOGIC

setBusinessLogic = ()->
  setStudentRentalLogic()
  setLandLordRentBusinessLogic()
  setProjectBusinessLogic()
  setRmListingBusinessLogic()
  setMLSBusinessLogic()
  setEvaluateBusinessLogic()
  setTrustedAssignmentBusinessLogic()
  setBuyTrustedAssignmentBusinessLogic()
  setSaleTrustedAssignmentBusinessLogic()
  setAssignmentBusinessLogic()

setBusinessLogic()


###
#key with longer length has higer priority.
#@param {string} key
#@return {object} {formDest(*),UIType,agentType,agentCardFields,formFields}
  # formDest:(*) ['landLordRent','project','projectToCrm','mls','rmlisting','evaluation','upgradeVip']
  # UIType: ['agentCard','form','mlsAgentPanel']
  # agentType:['followedAgent','followdRmAgent','listingAgent','mlsListingAgent']
  # agentCardFields: ['title','nm','cpny','eml','call','chat']
  # formFields:['nm','eml','mbl','placeHolder']
###

exports.findKeyInBusinessLogic = findKeyInBusinessLogic = (key)->
  #check k is valid:
  keyArray = key.split('.')
  # debug.debug('ALL_CONTACT_KEYS[key]',key,ALL_CONTACT_KEYS[key])
  if not ALL_CONTACT_KEYS[key]
    return {}
  while keyArray.length > 0
    key = keyArray.join('.')+'.'+CONTACT_REALTOR_VALUES.DEFAULT
    
    action = objectPath.get(gBusinessLogic, key)
    if action
      debug.debug 'matched key', key
      return action
    keyArray.splice(-1)
  return {}

# console.log 'ALL_CONTACT_KEYS',ALL_CONTACT_KEYS


#ownerNotInCrm ownerInCrmEqualToFollowAgent ownerInCrmNotEqualToFollowAgent

isRealtor = (user={})->
  return user.roles and ('realtor' in user.roles)

# user={roles:['vip_client']}, isVip(user) => false
isVip = (user={})->
  if not Array.isArray(user.roles)
    user.roles = [user.roles]
  return user.roles.some (e)-> e in ['vip','vip_plus']

exports.getUserFollowStatus = getUserFollowStatus = (user)->
  if not user
    return CONTACT_REALTOR_VALUES.CUSTOMER_FOLLOW_NONE
  if isRealtor(user)
    if isVip(user)
      return CONTACT_REALTOR_VALUES.VIP_REALTOR
    else
      return CONTACT_REALTOR_VALUES.NONE_VIP_REALTOR
  else if user.flwngRm
    return CONTACT_REALTOR_VALUES.CUSTOMER_FOLLOW_RM_REALTOR
  else if user.flwng
    return CONTACT_REALTOR_VALUES.CUSTOMER_FOLLOW_NONE_RM_REALTOR
  else
    return CONTACT_REALTOR_VALUES.CUSTOMER_FOLLOW_NONE

###endregion
NO_OWNER
OWNER_NOT_IN_CRM,
OWENR_IN_CRM_EQUAL_TO_FOLLOW_AGENT,
OWENR_IN_CRM_NOT_EQUAL_TO_FOLLOW_AGENT
###

###
根据page获取prop的拥有者的信息：
1. page: 'buyTrustedAssign','trustedAssignment','saleTrustedAssign' 获取设置的指定邮箱
2. page: 'landLordRent','rmlisting' 获取 prop 的 uid 信息
3. page: 'project' 获取 Sponsor Uid
4. page: 'mls' 获取 prop 的 Top Uid

return：{object} - user Info
###

findOwnerByPage = ({
  listingId,
  ProjectModel,
  PropertiesModel,
  UserModel,
  page,
  user,
  src,
  hideFubFld,
  locale,
  SysDataModel
})->
  # 这个定义的常量之所以没有放外面，是因为 PropertiesModel,ProjectModel 通过传参获得的
  FIND_OWNER_BY_PAGE_FN = {
    "#{CONTACT_REALTOR_VALUES.LANDLORD_RENT}": PropertiesModel.findRmListingUid
    "#{CONTACT_REALTOR_VALUES.PROJECT}": ProjectModel.findSponsorUid
    "#{CONTACT_REALTOR_VALUES.RM_LISTING}": PropertiesModel.findRmListingUid
    "#{CONTACT_REALTOR_VALUES.MLS}": PropertiesModel.findMlsTopUid
    "#{CONTACT_REALTOR_VALUES.EVALUATION}": ()-> null # NOTE:估价没有owner
    "#{CONTACT_REALTOR_VALUES.ASSIGNMENT}": PropertiesModel.findRmListingUid
  }
  # if not listingId
  #   throw ('No listing Id')
  if (page in [CONTACT_REALTOR_VALUES.TRUSTED_ASSIGNMENT, \
  CONTACT_REALTOR_VALUES.BUY_TRUSTEDASSIGNMENT,CONTACT_REALTOR_VALUES.SALE_TRUSTEDASSIGNMENT])
    # 查sysdata的_id:fubManagement的eml及邮箱拥有者的基本信息
    owner = await getSpecifiedEmail {SysDataModel,UserModel,locale,hideFubFld,page}
    return {owner,specificEmail:owner}
  # 查找房子拥有者的uid
  fn = FIND_OWNER_BY_PAGE_FN[page]
  if not fn
    debug.error 'ContactRealtor: no fn for page',page
    return {owner,receiveUser:null}
  debug.debug 'FIND_OWNER_BY_PAGE_FN=',fn,'id=',listingId
  uid = await fn(listingId, src)
  debug.debug 'owner uid',uid
  specificEmail = null
  # project/部分assignment 报名表发送到指定邮箱
  if (page in [CONTACT_REALTOR_VALUES.PROJECT,CONTACT_REALTOR_VALUES.ASSIGNMENT])
    specificEmail = await getSpecifiedEmail {SysDataModel,UserModel,locale,hideFubFld,page}
  unless uid
    return {owner:null,specificEmail}
  # 查找房子拥有者的具体信息
  owner = await getUserDetailWithFubInfo {id:uid,locale,UserModel,hideFubFld}
  return {owner,specificEmail}

getOwnerStatus = ({owner,user})->
  if not owner
    return CONTACT_REALTOR_VALUES.NO_OWNER
  else if not owner.fubAgentId
    return CONTACT_REALTOR_VALUES.OWNER_NOT_IN_CRM
  else if owner._id.toString() is user?.flwngRm?.uid.toString()
    return CONTACT_REALTOR_VALUES.OWENR_IN_CRM_EQUAL_TO_FOLLOW_AGENT
  else
    return CONTACT_REALTOR_VALUES.OWENR_IN_CRM_NOT_EQUAL_TO_FOLLOW_AGENT

getFollowedAgentInfo = ({user,agentType,UserModel,locale})->
  if agentType not in [CONTACT_REALTOR_VALUES.FOLLOWED_NONE_RM_REALTOR,
    CONTACT_REALTOR_VALUES.FOLLOWED_RM_REALTOR
  ]
    return null
  if agentType is CONTACT_REALTOR_VALUES.FOLLOWED_NONE_RM_REALTOR
     # agentType undefined, in unittest pre_processor error rateLimiter
    uid = user.flwng?[0]?.uid
  else if agentType is CONTACT_REALTOR_VALUES.FOLLOWED_RM_REALTOR
    uid = user.flwngRm?.uid # should only has one flwngRm
  if not uid
    debug.debug 'getFollowedAgentInfo:No valid user id',agentType,user,uid
    return null
  return await getUserDetailWithFubInfo {id:uid,UserModel,locale,UserModel}
# CONTACT_REALTOR_VALUES.RM_CONTROL_CITY,#需要city列表，暂时use gta + isOwnCtrlCityProp
# CONTACT_REALTOR_VALUES.RM_COOP_CITY,#rmfwng, should not happen for now. delay test.
# CONTACT_REALTOR_VALUES.RM_NONE_COOP_CITY,

getPropCity = (prop,popularCities)->
  debug.debug 'getPropCity',prop, propertiesHelper.isRMControlProp(prop)
  if propertiesHelper.isRMControlProp(prop) or isOwnCtrlCityProp(prop,popularCities)
    return CONTACT_REALTOR_VALUES.RM_CONTROL_CITY
  else
    return CONTACT_REALTOR_VALUES.RM_NONE_COOP_CITY
  #RM_COOP_CITY not deal for now

# @input:
# interface popularCities {
#   cities: city[],
#   mapping: 'PROV.CITY'{sale:number,rent:number},
# }
isOwnCtrlCityProp = (prop,popularCities)->
  # BUG: this @return has 'CA:' in front, which is not compatible with ownCtrlCity data structure
  # _id = unifyAddress {
  #   prov: cityHelper.getProvAbbrName(prop.prov_en or prop.prov),
  #   city:prop.city_en or prop.city
  # }
  prov = cityHelper.getProvAbbrName(prop.prov)
  city = propertiesHelper.formatCityName(prop.city)
  # _id = "#{prov}.#{city}"
  citiesMapping = popularCities.mapping or {}
  mappingObj = citiesMapping[prov]?[city] or {}
  saleOrRent = if propertiesHelper.isSale(prop) then 'sale' else 'rent'
  # console.log 'xxxxxxx',popularCities.mapping,mappingObj,mappingObj[saleOrRent]
  debug.debug 'cities array: ', popularCities.cities
  return mappingObj[saleOrRent]

###
join key,get the action based on the key.
return：{object} - {action,owner,followedAgentInfo, displayAgentInfo, key, specificEmail}
action：显示报名表信息的类型,标题，报名表去向
owner：prop的联系人信息
followedAgentInfo与displayAgentInfo：获取当前用户跟随经济的信息
###
exports.getContactRealtorAction = getContactRealtorAction = ({
  hasWechat,
  prop,
  page,
  src,
  user,
  ProjectModel,
  PropertiesModel,
  UserModel,
  hideFubFld = false,
  locale,
  popularCities,
  SysDataModel
})->
  src ?= CONTACT_REALTOR_VALUES.APP
  isLoggedIn = if user then CONTACT_REALTOR_VALUES.LOGGEDIN  \
    else CONTACT_REALTOR_VALUES.NOT_LOGGEDIN
  userStatus = getUserFollowStatus(user)
  wechatStatus = if hasWechat then CONTACT_REALTOR_VALUES.HAS_WECHAT \
    else CONTACT_REALTOR_VALUES.NO_WECHAT
  #for rm prop,use id first
  if (page in [CONTACT_REALTOR_VALUES.RM_LISTING, CONTACT_REALTOR_VALUES.LANDLORD_RENT,\
  CONTACT_REALTOR_VALUES.BUY_TRUSTEDASSIGNMENT,CONTACT_REALTOR_VALUES.SALE_TRUSTEDASSIGNMENT,\
  CONTACT_REALTOR_VALUES.ASSIGNMENT,CONTACT_REALTOR_VALUES.TRUSTED_ASSIGNMENT])
    listingId = prop.id
  else
    listingId = prop._id or prop.id
  unless (listingId or (page is CONTACT_REALTOR_VALUES.EVALUATION))
    return null
  {owner,specificEmail} = await findOwnerByPage {
    src,
    page,
    listingId,
    ProjectModel,
    PropertiesModel,
    UserModel,
    user,
    popularCities,
    locale,
    SysDataModel
  }
  # owner 是否有值在getOwnerStatus里面进行了判断
  ownerStatus = getOwnerStatus({owner,user})
  propCity = getPropCity(prop, popularCities)
  key = "#{src}.#{page}.#{isLoggedIn}.#{userStatus}.#{wechatStatus}.#{ownerStatus}.#{propCity}"
  # find Key in businessLogic
  action = findKeyInBusinessLogic(key)
  # append user follow realtor and user followed Rm realtor info
  unless (page in [CONTACT_REALTOR_VALUES.TRUSTED_ASSIGNMENT,\
  CONTACT_REALTOR_VALUES.BUY_TRUSTEDASSIGNMENT,CONTACT_REALTOR_VALUES.SALE_TRUSTEDASSIGNMENT])
    followedAgentInfo = await getFollowedAgentInfo {
      user,
      agentType: action.agentType,
      UserModel,
      locale
    }
  if action.agentType in [CONTACT_REALTOR_VALUES.FOLLOWED_NONE_RM_REALTOR,\
  CONTACT_REALTOR_VALUES.FOLLOWED_RM_REALTOR]
    if not followedAgentInfo
      debug.error "no follow agent info for action #{action}"
      return null
    displayAgentInfo =  followedAgentInfo
  else if action.agentType in [
    CONTACT_REALTOR_VALUES.OWNER,
    CONTACT_REALTOR_VALUES.LISTING_AGENT,
    CONTACT_REALTOR_VALUES.PROJECT_SPONSOR,
    CONTACT_REALTOR_VALUES.RM_LISTING_OWNER,
    CONTACT_REALTOR_VALUES.TOP_LISTING_AGENT
  ]
    if not owner
      error = "no owner for action #{action}"
      debug.error error
      return null
    displayAgentInfo = owner
    if prop.hideInfo
      displayAgentInfo = null
  return {action,owner,followedAgentInfo, displayAgentInfo,specificEmail,key}

exports.getUserDetailWithFubInfo = getUserDetailWithFubInfo = ({id,locale,UserModel,hideFubFld})->
  fields = {avt:1,nm_zh:1,nm_en:1, nm:1, cpny:1, cpny_en:1, cpny_zh:1, \
  eml:1,mbl:1, fubEml:1, fubMbl:1,fubAgentId:1, fubLeadEml:1,roles:1}
  ret = await UserModel.findByIdAsync id,{projection:fields}
  unless ret
    debug.error "no user found for userid:#{id}"
    return ret
  return dealUserDetailWithFubInfo {ret,locale,hideFubFld}

dealUserDetailWithFubInfo = ({ret,locale,hideFubFld}) ->
  ret.wesiteurl = '/1.5/wesite/'+ret._id
  if Array.isArray ret.eml
    ret.eml = ret.eml[0]
  if locale in ['zh','zh-cn']
    ret.cpny = ret.cpny_zh
  else
    ret.cpny = ret.cpny_en
  #ret.nm = ret.fnm or "#{(ret.ln or '')} #{(ret.fn or '')}" or ret.nm
  ret.nm = libUser.fullNameOrNickname locale,ret
  ret.eml = ret.fubEml if ret.fubEml
  ret.mbl = ret.fubMbl if ret.fubMbl
  for fld in ['cpny_en','cpny_zh','nm_zh','nm_en']
    delete ret[fld]
  if hideFubFld
    for fld in ['fubEml','fubMbl','fubAgentId']
      delete ret[fld]
  return ret

exports.getContactRealtor = ({
  MSG_STRINGS,
  gConfig,
  ProjectModel,
  UserModel,
  PropertiesModel,
  popularCities,
  SysDataModel,
  locale,user,hasWechat,page,src,prop})->
    if not user and (src is 'app')
      throw new Error(MSG_STRINGS.NEED_LOGIN)
    prop.city = prop.city_en or prop.city
    prop.prov = prop.prov_en or prop.prov
    prop.tp = prop.tp_en #for project type
    if prop
      listingId = prop._id
      uid = prop.uid # query from backend?,owner for rmlisting or project

    url = getPropShareUrl {prop,page,locale,shareHost:gConfig.share?.Host}
    #get user flwngRm
    user?={}
    if user._id
      try
        user = await UserModel.findByIdAsync user?._id, \
        {projection:UserModel.FORM_INPUT_FIELDS}
      catch error
        debug.error error
        throw new Error(MSG_STRINGS.DB_ERROR)
    hasWechat = UserModel.hasWechat(user,{hasWechat})
    #projectModel, pre_constrt. rmlisting
    try
      result = await getContactRealtorAction {
        hasWechat,
        user,
        page,
        src,
        prop,
        locale,
        ProjectModel,
        PropertiesModel,
        UserModel
        hideFubFld:true,
        popularCities,
        SysDataModel
      }
    catch err
      debug.error err
      throw new Error(MSG_STRINGS.DB_ERROR)
    unless result
      throw new Error(MSG_STRINGS.BAD_PARAMETER)
    {action, owner, displayAgentInfo} = result
    #handle action is {}, otherwise line 61 will raise error
    if not action?.UIType
      throw new Error(MSG_STRINGS.BAD_PARAMETER)
    ret = {action:action}
    action.formDest ?= []
    action.UIType ?= []
    if page is CONTACT_REALTOR_VALUES.MLS and \
    (CONTACT_REALTOR_VALUES.FORM in action.UIType) and action.formDest and \
    (CONTACT_REALTOR_VALUES.UPGRADE_VIP not in action.formDest)
      # 如果prop是sold或者delisted，则不显示showing date
      action.showShowingDate = true
    if (page is CONTACT_REALTOR_VALUES.MLS) and (prop.saleTpTag_en in ['Sold','Delisted'])
      action.showShowingDate = false
    if displayAgentInfo
      delete displayAgentInfo.roles if displayAgentInfo.roles
      ret.agentInfo = displayAgentInfo
      if displayAgentInfo.fubAgentId #crm 用户不显示chat
        ret.agentInfo.showChat = false
      else
        ret.agentInfo.showChat = true
      #如果是crm agent，显示default的内容，还是全显示？
      ret.mailBody = getDefaultMailContent {locale, prop, page, url}
      #Uncaught Exception:TypeError: this.locale is not a function when use l10n
      ret.mailSubject = CONTACT_REALTOR_VALUES.PROPERTY_INQUIRY
    formInfo = fillFormInfo {user,action,page,prop,UserModel,locale}
    ret.formInfo = formInfo if formInfo
    return ret

getDefaultMailContent = ({prop,page,url})->
  m = "#{getPlaceHolder({prop,page})} #{getPlaceHolderLocation({prop,page})}"
  return m+'%0d%0a'+url

getPlaceHolderLocation = ({prop,page})->
  location = ''
  if prop
    unt = prop.unt or ''
    if unt
      unt = unt+' '
    location = "#{unt}#{prop.addr} #{prop.city} #{prop.prov}"
    if page in [CONTACT_REALTOR_VALUES.PROJECT,CONTACT_REALTOR_VALUES.PROJECT_HAS_SPONSOR]
      location = prop.nmOrig or prop.nm_en or prop.nm
    if page is CONTACT_REALTOR_VALUES.MLS
      location += " (MLS#)#{prop.sid}"
  return location

getPlaceHolder = ({prop,page})->
  formPlaceHolder = ''
  if prop
    if page is CONTACT_REALTOR_VALUES.MLS
      if prop.saleTpTag_en in ['Sold','Delisted']
        formPlaceHolder = "#{CONTACT_REALTOR_VALUES.BOOK_VIEWING_SIMILAR}"
      else
        formPlaceHolder = "#{CONTACT_REALTOR_VALUES.BOOK_VIEWING_INFO}"
    else if (page in [CONTACT_REALTOR_VALUES.BUY_TRUSTEDASSIGNMENT,\
    CONTACT_REALTOR_VALUES.SALE_TRUSTEDASSIGNMENT])
      formPlaceHolder = ''
    else
      formPlaceHolder = "#{CONTACT_REALTOR_VALUES.INQUIRES_INFO}"
  return formPlaceHolder

fillFormInfo = ({user,action,page,prop,UserModel,locale})->
  if not action.UIType
    return null
  if not (CONTACT_REALTOR_VALUES.FORM in action.UIType)
    return null
  nm = stripEmojis(libUser.fullNameOrNickname(locale,user))
  fullName = libUser.getFirstLastName(nm)
  form = {nm, fn:fullName?.fn,ln:fullName?.ln, mbl: user?.mbl, eml: user?.eml}
  if Array.isArray(user?.eml)
    form.eml = user.eml[0]
  form.formPlaceHolder = getPlaceHolder {prop,page}
  action.formDest ?= []
  if CONTACT_REALTOR_VALUES.UPGRADE_VIP in action.formDest
    form.msg = 'See the listing agent contact information and get more advanced features.'
    form.submitLink = 'https://www.realmaster.ca/membership'
    form.submitTxt = 'Learn More'
    form.noForm = 1
  else if (page in [CONTACT_REALTOR_VALUES.BUY_TRUSTEDASSIGNMENT,\
  CONTACT_REALTOR_VALUES.SALE_TRUSTEDASSIGNMENT])
    form.buyOrSalePlaceHolder = "#{CONTACT_REALTOR_VALUES.BUY_SALE_PLACEHOLDER}"
  else
    form.formPlaceHolderLocation = getPlaceHolderLocation {prop,page}
  return form

getSpecifiedEmail = ({SysDataModel,UserModel,locale,hideFubFld,page}) ->
  fields = {avt:1,nm_zh:1,nm_en:1, nm:1, cpny:1, cpny_en:1, cpny_zh:1, \
    eml:1,mbl:1, fubEml:1, fubMbl:1,fubAgentId:1, fubLeadEml:1,roles:1}
  id = ''
  if page is CONTACT_REALTOR_VALUES.PROJECT
    id = CONTACT_REALTOR_VALUES.FUB_PROJECT_EMAIL
  else
    id = CONTACT_REALTOR_VALUES.FUB_ASSIGNMENT_EMAIL
  emilInfo = await SysDataModel.findById id
  if emilInfo?.eml
    eml = emilInfo.eml
  else
    eml = if page is CONTACT_REALTOR_VALUES.PROJECT then \
    config?.contact?.projectFubEmail else config?.contact?.trustedAssignEmail
  # 如果ret没有查找到，默认邮箱的eml要用于返回
  userInfo = {}
  ret = await UserModel.findByEmlAsync eml,{projection:fields}
  if ret
    userInfo = dealUserDetailWithFubInfo {ret,locale,hideFubFld}
  userInfo.eml = eml
  return userInfo
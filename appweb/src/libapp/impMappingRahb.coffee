debugHelper = require '../lib/debug'
propertiesHelper = require './properties'
{ objectStr2NumNClean } = require '../lib/helpers'
{ getFullAddress } = require '../lib/propAddress'

debug = debugHelper.getDebugger()

# add ListPrice to LOG_FIELDS
propertiesHelper.LOG_FIELDS.ListPrice = 1
rahbFieldMap = {
  ListingId: 'sid'
  Latitude: 'lat'
  Longitude: 'lng'
  CrossStreet: 'crsst'
  UnparsedAddress: 'addr'
  PostalCode: 'zip'
  UnitNumber: 'unt'
  StreetNumber: 'st_num'
  StreetName: 'st'
  StreetSuffix: 'st_sfx'
  StreetDirSuffix: 'st_dir'
  City : 'city'
  Township: 'cmty'
  StateOrProvince: 'prov'
  Country: 'cnty'
  PhotosCount: 'pho'
  ListPrice: 'lp'
  OriginalListPrice: 'olp'
  CloseDate : 'sldd'#TODO:Need to confirm whether this value is the expiration date or the sold date
  ClosePrice: 'sp'
  DirectionFaces: 'fce'
  VirtualTourURLUnbranded: 'vturl'
  VirtualTourURLBranded: 'vturl'
  PublicRemarks: 'm'
  YearBuilt: 'bltYr'
  LivingArea: 'sqft'
  TaxAnnualAmount: 'tax'
  TaxYear: 'taxyr'
  MaintenanceExpense: 'mfee'
  WaterSource: 'water'
  PoolFeatures: 'pool'
  ConstructionMaterials: 'constr'
  BedroomsTotal: 'tbdrms'
  Basement: 'bsmt'
  BathroomsTotalInteger: 'tbthrms'
  BathroomsFull: 'bthrms'
  BathroomsHalf: 'bthhlf'
  PetsAllowed: 'pets'
  Stories: 'lvl'
  RAHB_HoldoverDays: 'holdover'
  Possession: 'psn'
  ParkingTotal: 'tgr'
  GarageSpaces: 'gr'
  RAHB_MinRentalTerm: 'mintrm'
  FeedTypes: 'rhbtp'
  Zoning: 'zoning'
}

fieldsNeedToDestructureArray = ['water', 'pool', 'psn', 'BusinessType']
fieldsDoNotCopy = [
  '@odata'
  'ListingKey'
  'ModificationTimestamp'
  'BridgeModificationTimestamp'
  '_mt'
]

doMapping = (origRecord)->
  record = Object.assign {}, origRecord

  for key in fieldsDoNotCopy
    delete record[key]

  record = objectStr2NumNClean(record, true)

  for k, mappedK of rahbFieldMap
    if record.hasOwnProperty k
      record[mappedK] ?= record[k]
      delete record[k]

  for key in fieldsNeedToDestructureArray
    if Array.isArray record[key]
      record[key] = record[key].join(',')

  return record

formatPtype = (record) ->
  if /Residential/i.test(record.PropertyType)
    record.ptype = 'r'
    switch record.PropertySubType
      when 'Apartment', 'Condominium'
        record.ptype2 = ['Apartment']
      when 'Single Family Residence'
        record.ptype2 = ['House', 'Detached']
      when 'Duplex'
        record.ptype2 = ['House', 'Duplex']
      when 'Townhouse'
        record.ptype2 = ['Townhouse', 'Attached']
      when 'Multi Family'
        record.ptype2 = ['House', 'Multifamily']
      else
        record.ptype2 = [record.PropertySubType]
  else if /Commercial/i.test(record.PropertyType)
    record.ptype = 'b'
    record.ptype2 = [record.PropertySubType]
  else
    record.ptype = 'o'
    record.ptype2 = [record.PropertyType]

  delete record.PropertyType
  delete record.PropertySubType
  delete record.StructureType
  return record

exports.formatStatusAndLst = formatStatusAndLst = (record) ->
  if record.StandardStatus is 'Active'
    # MlsStatus can be 'Active', 'Active - CS', 'Active - NS'
    record.status = 'A'
    record.lst = 'New'
  else if record.MlsStatus is 'Cancelled' # or record.StandardStatus is 'Canceled'
    # StandardStatus is 'Canceled'.
    record.status = 'U'
    record.lst = 'Ter'
  else if record.MlsStatus is 'Expired' or record.StandardStatus is 'Expired'
    record.status = 'U'
    record.lst = 'Exp'
  else if record.MlsStatus is 'Sold'
    # StandardStatus is always 'Closed'
    record.status = 'U'
    record.lst = 'Sld'
  else if record.MlsStatus is 'Suspended'
    # StandardStatus is always 'Canceled'
    record.status = 'U'
    record.lst = 'Sus'
  else
    #  TODO: libapp/impMappingRahb.coffee formatStatusAndLst 148 RAHB formatStatusAndLst error MlsStatus:  Cancelled StandardStatus:  Canceled
    debug.error 'RAHB formatStatusAndLst error',
      'MlsStatus: ', record.MlsStatus,
      'StandardStatus: ', record.StandardStatus
  # delete record.MlsStatus
  delete record.StandardStatus
  return record

formatHeating = (record) ->
  # HeatingYN may be null, condition should exactly equal false
  if record.HeatingYN is false
    record.heat = 'None'
    return record

  if not record.Heating
    return record

  if /Baseboard/i.test(record.Heating)
    record.heat = 'Baseboard'
  else if /Forced Air/i.test(record.Heating)
    record.heat = 'Forced Air'
  else if /Water Radiators/i.test(record.Heating)
    record.heat = 'Water Radiators'
  else if /Water/i.test(record.Heating)
    record.heat = 'Water'
  else if /Heat Pump/i.test(record.Heating)
    record.heat = 'Heat Pump'

  if /Electric/i.test(record.Heating)
    record.fuel = 'Electric'
  else if /Propane/i.test(record.Heating)
    record.fuel = 'Propane'
  else if /Gas/i.test(record.Heating)
    record.fuel = 'Gas'
  else if /Oil/i.test(record.Heating)
    record.fuel = 'Oil'
  else if /Wood/i.test(record.Heating)
    record.fuel = 'Wood'

  delete record.Heating
  delete record.HeatingYN
  return record

formatAirCondition = (record) ->
  # CoolingYN may be null, condition should exactly equal false
  if record.CoolingYN is false
    record.ac = 'None'
  else
    record.ac = record.Cooling.join(',')
  delete record.CoolingYN
  delete record.Cooling
  return record

formatPhotos = (record) ->
  if not record.Media
    return record
  record.phoUrls = []
  for i in [0...record.Media.length]
    if record.Media[i].MediaCategory is 'Photo'
      record.phoUrls.push { id: record.Media[i].Order, url: record.Media[i].MediaURL }
  record.phosrc = 'rhb'
  delete record.Media
  return record

formatLandSize = (record) ->
  record.front_ft = propertiesHelper.getFloat record.FrontageLength
  record.dim = record.LotSizeDimensions?.replace('x',' * ')
  if dimArr = record.dim?.match(/(\d+[.]?\d*)(\s+[*]\s+)(\d+[.]?\d*)/)
    record.depth = propertiesHelper.getFloat dimArr[3]
  record.lotsz_code = record.RAHB_LotSizeCode
  record.irreg = record.RAHB_LotIrregularities
  if record.LotSizeSquareFeet
    record.lotsz = propertiesHelper.getFloat record.LotSizeSquareFeet
    record.lotszUt = 'ft²'
  else if record.LotSizeArea
    record.lotsz = propertiesHelper.getFloat record.LotSizeArea
    if record.LotSizeUnits?.toUpperCase() is 'ACRES'
      # 英亩转平方英尺
      record.lotsz = propertiesHelper.getFloat(record.lotsz * 43560)
      record.lotszUt = 'ft²'
    else if record.LotSizeUnits?.toUpperCase() isnt 'SQUARE FEET'
      debug.warn 'unknown lotSize unit,porpId:',record._id, ' unit:',record.LotSizeUnits
  return record

getOfficeAndAgent = ({ officeMlsId, agentMlsId, AgentCol, OfficeCol }) ->
  office = await OfficeCol.findOne {
    id: officeMlsId
  }, {
    projection: {
      _id: 1,
      id: 1,
      nm: 1,
      addr: 1,
      tel: 1,
      fax: 1,
    }
  }

  if not office
    return null

  result = {
    _id: office._id,
    id: office.id,
    nm: office.nm,
    addr: office.addr,
    tel: office.tel,
    fax: office.fax,
    agnt: [],
  }

  if not agentMlsId
    return result

  agent = await AgentCol.findOne {
    id: agentMlsId
  }, {
    projection: {
      _id: 1,
      id: 1,
      nm: 1,
      tel: 1,
      eml: 1,
    }
  }

  if not agent
    return result

  result.agnt.push agent
  return result

module.exports.formatOffice = formatOffice = (record) ->
  if record.ListOfficeMlsId
    office = {
      id: record.ListOfficeMlsId,
      nm: record.ListOfficeName,
      tel: record.ListOfficePhone,
      fax: record.ListOfficeFax,
      agnt: [{
        id: record.ListAgentMlsId,
        nm: record.ListAgentFullName,
        tel: record.ListAgentPreferredPhone,
        eml: record.ListAgentEmail,
      }],
    }
    record.la = [office]

  if record.CoListOfficeMlsId
    office = {
      id: record.CoListOfficeMlsId,
      nm: record.CoListOfficeName,
      tel: record.CoListOfficePhone,
      fax: record.CoListOfficeFax,
      agnt: [{
        id: record.CoListAgentMlsId,
        nm: record.CoListAgentFullName,
        tel: record.CoListAgentPreferredPhone,
        eml: record.CoListAgentEmail,
      }],
    }
    record.la ?= []
    record.la.push office

  if record.BuyerOfficeMlsId
    office = {
      id: record.BuyerOfficeMlsId,
      nm: record.BuyerOfficeName,
      tel: record.BuyerOfficePhone,
      fax: record.BuyerOfficeFax,
      agnt: [{
        id: record.BuyerAgentMlsId,
        nm: record.BuyerAgentFullName,
        tel: record.BuyerAgentPreferredPhone,
        eml: record.BuyerAgentEmail,
      }],
    }
    record.coop = [office]

  if record.CoBuyerOfficeMlsId
    office = {
      id: record.CoBuyerOfficeMlsId,
      nm: record.CoBuyerOfficeName,
      tel: record.CoBuyerOfficePhone,
      fax: record.CoBuyerOfficeFax,
      agnt: [{
        id: record.CoBuyerAgentMlsId,
        nm: record.CoBuyerAgentFullName,
        tel: record.CoBuyerAgentPreferredPhone,
        eml: record.CoBuyerAgentEmail,
      }],
    }
    record.coop ?= []
    record.coop.push office

  record.rltr = record.la?[0]?.nm

  delete record.ListOfficeKey
  delete record.ListOfficeMlsId
  delete record.ListOfficeName
  delete record.ListOfficePhone
  delete record.ListOfficeFax

  delete record.CoListOfficeKey
  delete record.CoListOfficeMlsId
  delete record.CoListOfficeName
  delete record.CoListOfficePhone
  delete record.CoListOfficeFax

  delete record.BuyerOfficeKey
  delete record.BuyerOfficeMlsId
  delete record.BuyerOfficeName
  delete record.BuyerOfficePhone

  delete record.CoBuyerOfficeKey
  delete record.CoBuyerOfficeMlsId
  delete record.CoBuyerOfficeName
  delete record.CoBuyerOfficePhone

  delete record.ListAgentKey
  delete record.ListAgentMlsId
  delete record.ListAgentCellPhone
  delete record.ListAgentPreferredPhone
  delete record.ListAgentEmail
  delete record.ListAgentAOR
  delete record.ListAgentFullName

  delete record.CoListAgentKey
  delete record.CoListAgentMlsId
  delete record.CoListAgentCellPhone
  delete record.CoListAgentPreferredPhone
  delete record.CoListAgentEmail
  delete record.CoListAgentAOR
  delete record.CoListAgentFullName

  delete record.BuyerAgentKey
  delete record.BuyerAgentMlsId
  delete record.BuyerAgentPreferredPhone
  delete record.BuyerAgentEmail
  delete record.BuyerAgentAOR
  delete record.BuyerAgentFullName

  delete record.CoBuyerAgentKey
  delete record.CoBuyerAgentMlsId
  delete record.CoBuyerAgentPreferredPhone
  delete record.CoBuyerAgentEmail
  delete record.CoBuyerAgentAOR
  delete record.CoBuyerAgentFullName

  return record

# deal with case[ { _id: 'RHBH4155817', RAHB_LeasesYN: true, lp: 849900 } ]
getIsLeaseFromAgreement = (record) ->
  if record.RAHB_LeasesYN or record.RAHB_LeaseAgreementYN
    return true
  false

# @param {Object} prop - ret property
# @param {Object} record - rni record
module.exports.formatSaleType = formatSaleType = (prop = {},record = {}) ->
  lp = prop.lp or prop.lpr
  # 1. RAHB_TransactionType存在时根据当前字段判断
  # 2. RAHB_TransactionType字段不存在时，根据lp判断(2w以内认为是出租)
  # 3. RAHB_TransactionType不存在并且价格超过2w时, 根据RAHB_LeasesYN/RAHB_LeaseAgreementYN 判断
  if record.RAHB_TransactionType
    if /lease|rent/i.test record.RAHB_TransactionType
      isLease = true
    else if /sale/i.test record.RAHB_TransactionType
      isLease = false
    else
      debug.error 'unknown RAHB_TransactionType:',record.RAHB_TransactionType
  else
    if not lp
      isLease = getIsLeaseFromAgreement record
    else
      if (parseInt(lp) < propertiesHelper.minSalePrice)
        isLease = true
      # price > 2w
      else
        isLease = getIsLeaseFromAgreement record
  if isLease
    prop.lpr = prop.lp
    prop.saletp = ['Lease']
    delete prop.lp
  else
    prop.saletp = ['Sale']

exports.convertFromRahb = convertFromRahb = ({
  srcRecord,
  Boundary,
  AgentCol,
  OfficeCol,
}, cb) ->
  try
    ret = doMapping srcRecord
    ret = formatPtype ret
    ret = formatStatusAndLst ret
    ret = formatHeating ret
    ret = formatAirCondition ret
    ret = formatOffice ret
    ret = formatPhotos ret
    ret = formatLandSize ret
    ret.addr = ret.addr?.split(',')[0]
    ret.src = 'RHB'  # 如果更换了这个值，记得修改statusLstMapping
    ret.lup = new Date(srcRecord.BridgeModificationTimestamp)
    ret.mt = ret.lup
    ret.rhbtp = ret.rhbtp?.map((v) ->
      v.toLowerCase()
    )
    formatSaleType ret,srcRecord
  catch err
    return cb err

  debug.debug 'convert ret', ret
  cb null, ret

exports.convertOffice = convertOffice = (srcRecord) ->
  addr = getFullAddress {
    prov: srcRecord.OfficeStateOrProvince,
    addr: srcRecord.OfficeAddress1,
    city: srcRecord.OfficeCity,
    zip: srcRecord.OfficePostalCode,
  }
  return {
    id: srcRecord.OfficeMlsId,
    key: srcRecord.OfficeKey,
    nm: srcRecord.OfficeName,
    mt: new Date(srcRecord.BridgeModificationTimestamp),
    tel: srcRecord.OfficePhone,
    fax: srcRecord.OfficeFax,
    rhbtp: srcRecord.FeedTypes?.map((v) -> v.toLowerCase()),
    addr,
    status: srcRecord.OfficeStatus,
    src: 'RHB',
  }

exports.convertMember = convertMember = (srcRecord) ->
  return {
    id: srcRecord.MemberMlsId,
    key: srcRecord.MemberKey,
    nm: srcRecord.MemberFullName,
    tel: srcRecord.MemberPreferredPhone or \
        srcRecord.MemberMobilePhone or \
        srcRecord.MemberDirectPhone,
    eml: srcRecord.MemberEmail,
    officeId: srcRecord.OfficeMlsId,
    officeKey: srcRecord.OfficeKey,
    mt: new Date(srcRecord.BridgeModificationTimestamp),
    rhbtp: srcRecord.FeedTypes?.map((v) -> v.toLowerCase()),
    type: srcRecord.MemberType,
    src: 'RHB',
    isDeleted: srcRecord.RAHB_IsDeleted,
  }

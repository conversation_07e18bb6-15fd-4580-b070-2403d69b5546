crypto = require('crypto')
util = require('util')
path = require('path')
emojiRegex = require('emoji-regex')
createHash = require('crypto').createHash
debugHelper = require './debug'
debug = debugHelper.getDebugger()

###*
# Base62 字符集常量，包含大写字母、小写字母和数字
# 用于 base62 编码转换，总共62个字符
# @constant {string}
###
BASE_62_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'



String.prototype.repeat = (count)->
  if count < 1 then return ''
  result = ''
  pattern = @valueOf()
  while count > 0
    if count & 1
      result += pattern
    count >>= 1
    pattern += pattern
  result

###################### Boolean ######################

module.exports.isObjectIDString = (fid)->
  return 'string' is typeof fid and /^[a-f0-9]{24}$/i.test fid

# NOTE: https://stackoverflow.com/questions/9238640/how-long-can-a-tld-possibly-be
# <EMAIL>

regex_email = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/
module.exports.isEmail = isEmail = (eml)->  regex_email.test eml

module.exports.isEmailArray = isEmailArray = (emails)->
  isEmailOK = true
  if (not Array.isArray emails) or (emails.length is 0)
    isEmailOK = false
  else
    emails.forEach (email)->
      if (not isEmail email)
        isEmailOK = false
  return isEmailOK


module.exports.isRegExp = (p0)->
  p0? and ((p0 instanceof RegExp) \
  or (p0.constructor is RegExp) \
  or (('function' is typeof p0.exec) and ('function' is typeof p0.test)))


module.exports.hasChinese = (txt)-> ('string' is typeof txt) and (/[\u4e00-\u9fa5]/.test txt)


###################### Formatter ######################

module.exports.fixlengthString = (fixl,str)->
  str ?= '-'
  str = str.toString()
  lf = fixl - str.length
  ' '.repeat(lf) + str

module.exports.capital2dash = (s,sym='-')->
  s.replace /([A-Z])/g,(x)-> sym + x.toLowerCase()

# some SMS message only send UPPER CASE strings, so make folder number as upper case
_chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz_-=<>@^{}()!~#$%&*+./?|'
_charsLevel = [26,36,62,85]
number2chars = (num,level=0)->
  base = _charsLevel[level] or 26
  if num <= 0 then return _chars[0]
  char = _chars[num % base]
  num = Math.floor(num / base)
  while num > 0
    char = _chars[num % base] + char
    num = Math.floor(num / base)
  char

module.exports.number2chars = number2chars

module.exports.number2chars2 = (num)-> number2chars num,2


module.exports.trim = (s)->
  if s then return s.replace /^\s+|\s+$/,''
  ''

module.exports.trimore = (s)->
  if s then return s.replace /^[\s\n\r\']+|[\s\n\r\']+$/mg,''
  ''


# NOTE: isEmail?  do not replace @ in /[^a-z0-9\s\-\_\.]/
module.exports.str2reg = (str, isSupportZh, isEmail)->
  str = str.toLowerCase()
  reg = /[^a-z0-9\s\-\_\.]/ig
  if isEmail
    reg =  /[^a-z0-9\s\-\_\.\@]/ig
  if isSupportZh
    reg = /[^\u4E00-\u9FCCa-z0-9\s\-\_\.]/ig
  str = str.replace reg,'.' # Got exception: Invalid regular expression: /^.*+1 (647) 836-3588.*$/: Nothing to repeat
  str = str.replace /\s+/g,'\\s+'
  str = str.replace '.','\.'
  if isEmail
    str = '^'+str
  # str = str.replace '@','\@'
  new RegExp(str, 'i')



module.exports.toCamelCase = (str)->
  str
  .toLowerCase()
  .replace(/\s(.)/g, ($1)-> $1.toUpperCase())
  .replace(/\s/g, '')

# 'ke-aei ine.ae' => 'Ke-Aei Ine.ae'
module.exports.strCapitalize =  strCapitalize = (str, toLower = true) ->
  return str unless 'string' is typeof str
  str = str.toLowerCase() if toLower
  str = str.replace(/(?:^|\s|-|\/|\.|\,)\S/g, (a)-> return a.toUpperCase())
  #Fletcher\'s Creek South, s should not to upper case
  #O'shanter-Sullivan s should to upper case
  str = str.replace(/(\')\S(?!(\s|$))/g, (a)-> return a.toUpperCase())
  str
# remove any chars other than a-z or _
module.exports.str2Key = str2Key = (str)->
  return str unless 'string' is typeof str
  str = strCapitalize str
  str.replace(/[^a-zA-Z\_]/g, '')

module.exports.preFormatURL = (title, len = 40) ->
  title_format = title.substr(0,len).replace(/\//g, '')
  title_format

module.exports.formatUrlStr = (str) ->
  return '' unless ('string' is typeof str)
  return str.replace(/(\s|%|\/)+/g, '-')

module.exports.safeURLFileName = (n)-> n.replace(/[\/\#\!\@\$\*\(\)\?\"\'\;\:\,\+\=]+/g,'')

module.exports.safeInput = (n) -> n.replace(/[\`\@\#\$\^\&\*\=\|\{\}\[\]\<\>\/\~\￥\…\—\【\】\+\\]/g, '')

module.exports.stripEmojis = (n) ->
  n ?= ''
  # TODO: node support: withEmojis = /\p{Extended_Pictographic}/u
  # n = n.replace /([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g,''
  emojiReg = emojiRegex()
  n = n.replace emojiReg,''
  n = n.replace(/\s+/g, ' ')
  # n = n.replace(/[\`\@\#\$\^\&\*\=\|\{\}\[\]\<\>\/\~\￥\…\—\【\】\+]/g, '')
  n.trim()

module.exports.md5 = (str, encoding = 'hex')->
  crypto.createHash('md5').update(str).digest(encoding)

# https://stackoverflow.com/questions/2901102/how-to-print-a-number-with-commas-as-thousands-separators-in-javascript
module.exports.currency = currency = (x, sign = '$', dig) ->
  try
    if((typeof(x) is 'string') and (x.indexOf(sign) isnt -1))
      return x
    tmp = parseInt(x)
    if isNaN(tmp)
      return null
    if tmp < 100 and dig < 2
      dig = 2
    parts = x.toString().split('.')
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    if dig == 0
      parts[1] = undefined
    else if dig > 0
      if parts[1]
        parts[1] = parts[1].substr(0, dig)
    return sign + parts.filter((val) ->
      val
    ).join('.')
  catch e
    console.error e
    return null
  return

module.exports.unifyAddress = unifyAddress = (param,appendZip=false)->
  # when has noAddr, means the addr field is not ready. Bypass this record
  return null if param.noAddr or (not param.prov)
  if  (not param.addr) and param.st
    if param.st_num
      param.addr = "#{param.st_num} #{param.st}"
    else
      param.addr =param.st
  uaddr = ''
  uaddr += param.cnty or 'CA'
  uaddr += ':'
  uaddr += param.prov
  # 如果有地址，优先地址。否则用zip或者cmty。没有地址，用城市或者area
  if param.addr or param.zip or param.cmty
    if param.city
      uaddr += ':'
      uaddr += param.city
      # if param.addr # addr has extention of address
      addr =  param.addr?.toString().trim().replace(/\./g,'')
      if appendZip and addr and param.zip and ('string' is typeof param.zip) and (param.zip.length > 3)
        # uaddr需要添加zip信息,取前三位
        uaddr += ":#{param.zip.slice(0, 3)}:#{addr}"
      else
        uaddr += ":#{addr or param.zip or param.cmty}"
    else if param.area
      uaddr += ":REGION #{param.area}"
    else if param.region
      uaddr += ":REGION #{param.region}"
    else
      uaddr +=':'
  else if param.subCity
    uaddr += ":SUBCITY #{param.subCity}"
  else if param.city
    uaddr += ":#{param.city}"
  else if param.area
    uaddr += ":REGION #{param.area}"
  else if param.region
    uaddr += ":REGION #{param.region}"
  uaddr.toUpperCase()

module.exports.getCommunityId = (param)->
  return param.bndCmty._id if param.bndCmty?._id #use bndCmty _id
  #if no bndCmty, then build cmty id
  return null if param.noAddr or (not param.prov) or (not param.city) or (not param.cmty)
  cmtyParm = {
    cnty:param.cnty,
    prov:param.prov,
    city:param.city,
    cmty:param.cmty
  }
  return unifyAddress cmtyParm

###################### Generator ######################

module.exports.randomString = (seed = 's')->
  crypto.createHash('sha1').update(Math.random().toString())\
  .update(Date.now().toString()).update(seed).digest('hex')



###################### Others ######################

module.exports.countChar = (str,char)->
  counter = 0
  for c in str
    counter++ if c is char
  counter


module.exports.logNoChangeLine = (m)->
  process.stdout.write m

module.exports.relog = relog = (m)->
  if process.stdout.clearLine
    process.stdout.clearLine()
    process.stdout.cursorTo(0)
    process.stdout.write m
  else
    console.log m


{sprintf,vsprintf} = require './sprintf'

class NMessage
  constructor: (@name,@msg,@values...)->
    null
  message:(i18n)->
    if i18n
      vsprintf i18n(@msg),@values
    else
      vsprintf @msg,@values
  nmessage:(i18n)->
    if @name?
      if i18n
        return i18n(@name) + ": " + vsprintf i18n(@msg),@values
      else
        return @name + ": " + vsprintf @msg,@values
    @message(i18n)

module.exports.NMessage = NMessage

class NError extends Error
  constructor: (@name,@errmsg,@values...)->
    super()
    @message() #TODO: not sure if work
  message:(i18n)->
    if i18n
      vsprintf i18n(@errmsg),@values
    else
      vsprintf @errmsg,@values
  nmessage:(i18n)->
    if @name?
      if i18n
        return i18n(@name) + ": " + vsprintf i18n(@msg),@values
      else
        return @name + ": " + vsprintf @msg,@values
    @message(i18n)

module.exports.NError = NError

module.exports.isStringWithValue = (v)->
  return false if not ('string' is typeof v)
  return false if v.trim() is ''
  return true

module.exports.isNullOrEmpty = (v)->
  return true if v is null
  return true if v is []
  return true if typeof v is 'undefined'
  if ('string' is typeof v) and (v.trim() is '')
    return true
  false
module.exports.formatLng = formatLng = (lng)->
  lng = Number(lng) if typeof lng isnt 'number'
  if lng < -180
    lng += 360
    formatLng lng
  else if lng > 180
    lng -= 360
    formatLng lng
  else
    return lng

module.exports.needRedirectUrl = needRedirectUrl = (lngOrg,url)->
  lng = formatLng lngOrg
  if lngOrg isnt lng.toString()
    currentUrl = url
    url = currentUrl.replace(lngOrg, lng)
    return url
  else
    return false

module.exports.isLatitude = (latitude) ->
# 纬度 -90°～90°
  if latitude > -90 and latitude < 90
    return true
  false

module.exports.isLongitude = (longitude) ->
# 经度 -180°～180°
  if longitude > -180 and longitude < 180
    return true
  false

###
 m = prop.m #remarks
 v = string[] #pool,gym etc...
###
module.exports.stringContainsAny = (m,v=[])->
  v ?= []
  m ?= ''
  if not Array.isArray v
    v = [v+'']
  ret = false
  m = (m+'').toLowerCase()
  for i in v
    i = (i+'').toLowerCase()
    if m.indexOf(i) >= 0
      ret = true
      break
  ret


###
https://www.cnblogs.com/GeniusLyzh/p/7113581.html
全角转半角
转化原理
全角空格unicode编码为12288，半角空格为32
其他字符半角(33-126)与全角(65281-65374)的unicode编码对应关系是：均相差65248
###
module.exports.toCDB = toCDB = (str='')->
  str = str+'' unless 'string' is typeof str
  tmp = ''
  for char,idx in str
    if str.charCodeAt(idx) > 65248 and str.charCodeAt(idx) < 65375
      tmp += String.fromCharCode(str.charCodeAt(idx) - 65248)
    else
      tmp += char
  return tmp

###
keepAlphanumeric('2 Rondeau "Dr"中文中文中') -> '2 Rondeau Dr '
@param {string} str - user input
@return {string} - string only contains alphanumeric and space
###
exports.keepAlphanumeric = keepAlphanumeric = (str)->
  return '' unless str
  return str.toString().replace(/[^\。\，\.\,\-\_\#\w\s]+/g,' ')

exports.keepAlphanumericToCDB = (str)->
  return toCDB(keepAlphanumeric(str)).trim()

exports.filterSpecialCase = (str)->
  str ?= ''
  return str unless str
  # handle str is number
  return str.toString().replace(/[\#\'\.\,\&\"]/g,'')

###
计算两个string的差异
https://en.wikipedia.org/wiki/Levenshtein_distance
https://www.tutorialspoint.com/levenshtein-distance-in-javascript

levenshteinDistance('a','b') -> 1
levenshteinDistance('a','bc') -> 2
levenshteinDistance('a','abc') -> 2
levenshteinDistance('ab','ac') -> 1
levenshteinDistance('wxyz','x') -> 3
###
exports.levenshteinDistance = levenshteinDistance = (str1, str2) ->
  track = Array(str2.length + 1).fill(null).map(() ->
    Array(str1.length + 1).fill(null))
  for i in [0..str1.length]
    track[0][i] = i
  for j in [0..str2.length]
    track[j][0] = j
  for j in [1..str2.length]
    for i in [1..str1.length]
      if str1[i - 1] is str2[j - 1]
        indicator = 0
      else
        indicator = 1
      track[j][i] = Math.min(
        track[j][i - 1] + 1,
        track[j - 1][i] + 1,
        track[j - 1][i - 1] + indicator,
      )
  return track[str2.length][str1.length]

###
Replace JSON.stringify.
- support function.
- Do not throw error. return null if error.
- Beautify JSON.stringify.
###
exports.stringify = (obj, {
  compact, # default is false
  colors, # default is false
  depth, # default is 3
  breakLength, # default is Infinity
  maxArrayLength, # default is 100
} = {}) ->
  try
    return util.inspect(obj, {
      compact: compact or false,
      depth: depth or 3,
      colors,
      breakLength: breakLength or Infinity,
      maxArrayLength: maxArrayLength or 100
    })
  catch e
    console.warn e
    return null

# 对字符串中的特殊字符添加转义符
# es query_string保留以下字符需转义;+ - = & | > < ! ( ) { } [ ] ^ " ~ * ? : \ /
exports.escapeSpecialCharacter = (str)->
  tmpStr = ''
  for char in str
    if char in ['\\','+','-','!','(',')',':','^','[',']','"','{','}','*','?','|','&','/','<','>','~','=']
      tmpStr += '\\'
    tmpStr += char
  return tmpStr

# url参与path join
# 此函数不会将url中的http://改为http:/
exports.pathJoinWithUrl = (url,args...)->
  urlObj = new URL(url)
  urlObj.pathname = path.posix.join(urlObj.pathname,args...)
  return urlObj.toString()

# 判断两个字符串是不是子串关系
exports.isSubstring = (stringA,stringB) ->
  # 添加类型判断，字符串大小写统一
  if (typeof stringA isnt 'string') or (typeof stringB isnt 'string')
    return false
  tmpStrA = stringA.toLowerCase()
  tmpStrB = stringB.toLowerCase()
  if ((tmpStrA.length >= tmpStrB.length) and tmpStrA.includes(tmpStrB))
    return true
  else if ((tmpStrB.length > tmpStrA.length) and tmpStrB.includes(tmpStrA))
    return true
  else
    return false

# 去除str中除数字之外的文字并改为xxx-xxx-xxxx格式
exports.filterOtherThanTel = (str)->
  return str unless str
  str = str.toString().replace(/[\D]+/g,'')
  return str.slice(0,3) + '-' + str.slice(3,6) + '-' + str.slice(6)

# 判断两个数组是不是子集关系,如果不是找出差集
exports.isSubsetBetweenArray = (arryA,arryB) ->
  isSubset = true
  diffArray = []
  for itemA in arryA
    unless arryB.includes(itemA)
      isSubset = false
      unless diffArray.includes(itemA)
        diffArray.push(itemA)
  if isSubset # A是B的子集
    return {isSubset,diffArray}
  else
    isSubset = true
  for itemB in arryB
    unless arryA.includes(itemB)
      isSubset = false
      unless diffArray.includes(itemB)
        diffArray.push(itemB)
  return {isSubset,diffArray}

# 去掉JSON中的特殊字符
exports.filterJSONSpecialChar = (str)->
  unless str
    return str
  return str.replace(/(\\n|\\t|\\r|\\b|\\f|")/g,'')

# 检查字符串是否满足传入长度，是否满足内容需求以字母开头且由字母和数字组成,如果是nanoid，不限制以字母开头
exports.isAlphaNumeric = (str,length,regType)->
  maxLength = 32
  if (not str) or (str.length is 0) or (str.length > maxLength)
    return false
  if length and (str.length isnt length)
    return false
  reg = /^[a-zA-Z]+[a-zA-Z0-9]+$/
  if regType is 'nanoid'
    reg = /^[a-zA-Z0-9]+$/
  if not str.match(reg)
    return false
  return true

mongoSpecialChar = ['\\','*','/','<','>','$','.'] # mongo中比较特殊的字符
# 对将要保存到数据库的用户输入的字符串进行特殊字符处理，将字符串中的<script>...</script>包含标签中的内容全部替换成''，对字符串中的特殊字符添加转义符
exports.addEscapeCharForSpecialChar = (content)->
  unless content?.length
    return content
  string = content.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gim,'')
  for item in mongoSpecialChar
    if item is '\\'
      regExp = new RegExp("\\\\", 'g')
      string = string.replace(regExp, "\\\\")
    else
      regExp = new RegExp("\\#{item}", 'g')
      string = string.replace(regExp, "\\#{item}")
  return string

# 从数据库取出来的内容去掉特殊字符的转义符
exports.removeEscapeCharForSpecialChar = (content)->
  unless content?.length
    return content
  for item in mongoSpecialChar
    if item is '\\'
      regExp = new RegExp("\\\\\\\\", 'g')
      content = content.replace(regExp, '\\')
    else
      regExp = new RegExp("\\\\\\#{item}", 'g')
      content = content.replace(regExp, item)
  return content

module.exports.getHash = (str)->
  createHash('sha1').update(str).digest('base64')

###
# @description 计算两个字符串的相似度
# @param {string} str1 - 第一个字符串
# @param {string} str2 - 第二个字符串
# @returns {number} - 返回相似度，范围0-1，1表示完全相同
###
module.exports.calculateStringSimilarity = (str1, str2)->
  # 处理空字符串情况
  if (not str1?) or (not str2?)
    return 0

  # 转换为小写并去除两端空白符以进行不区分大小写的比较
  s1 = str1.toLowerCase().trim()
  s2 = str2.toLowerCase().trim()

  if s1 is s2
    return 1
  
  # 使用Levenshtein距离算法计算编辑距离
  m = s1.length
  n = s2.length

  # 计算相似度
  distance = levenshteinDistance s1,s2
  maxLength = Math.max(m, n)
  similarity = 1 - (distance / maxLength)
  
  return similarity

###*
# 将 32 位有符号整数转换为 Base62 编码字符串
# Base62 使用 A-Z, a-z, 0-9 共62个字符进行编码
# @param {number} n - 要转换的 32 位有符号整数
# @return {string} 返回 Base62 编码的字符串
# @throws {Error} 当输入不是有效的整数时抛出错误
###
module.exports.int32ToBase62 = int32ToBase62 = (n) ->
  # 参数验证：检查输入是否为数字
  if not (('number' is typeof(n)) and (parseInt(n) is n))
    debug.warn 'int32ToBase62: input is not an integer',n
    return n
  
  # 将负数视为高位无符号值进行处理
  # JavaScript 中 >>> 0 操作可以将有符号 32 位整数转换为无符号 32 位整数
  num = n >>> 0
  result = []
  
  # 特殊情况：如果输入为 0，直接返回 base62 字符集的第一个字符
  if n is 0
    return BASE_62_CHARS[0]
  
  # 循环执行 base62 转换算法
  # 不断除以 62 并取余数，余数对应 base62 字符集中的字符
  while num > 0
    remainder = num % 62
    # 将对应的 base62 字符添加到结果数组的开头
    result.unshift(BASE_62_CHARS[remainder])
    # 整数除法，继续处理下一位
    num = Math.floor(num / 62)
  
  # 验证结果是否有效
  if result.length is 0
    return n
  
  # 将字符数组连接成字符串并返回
  return result.join('')
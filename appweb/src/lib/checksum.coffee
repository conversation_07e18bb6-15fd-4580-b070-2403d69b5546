crypto = require 'crypto'
debugHelper = require './debug'
debug = debugHelper.getDebugger()

exports.createSum = createSum = (str,key,digit=6)->
  md5sum = crypto.createHash 'md5'
  md5sum.update str
  md5sum.update key
  md5sum.digest('hex').substr(-digit)

exports.checkSum = checkSum = (str,key,sum)->
  md5sum = crypto.createHash 'md5'
  md5sum.update str
  md5sum.update key
  r = md5sum.digest('hex')
  debug.debug 'checksum',str,key,sum,r,'r.length',r.length,'sum.length',sum.length
  (r.length >= sum.length) and (r.substr(-sum.length) is sum)

# return '[timestamp][sum][str]'
exports.randomSum = randomSum = (str,key)->
  ts = (Date.now() + Math.floor(Math.random() * 234)).toString(16).substr(-4)
  sum = createSum (ts + str),key,6
  ts + sum + str

exports.checkRandomSum = checkRandomSum = (str,key)->
  ts = str.substr(0,4)
  sum = str.substr 4,6
  str = str.substr 10
  if checkSum (ts + str),key,sum
    return str
  null

CIPHER_ALG = 'aes-256-ctr' #'aes-128-ofb'
IV_LENGTH = 16
# NOTE iv= initial vector = salt

exports.encrypt = encrypt = (str,key, cipher_algoritm = CIPHER_ALG)->
  iv = crypto.randomBytes(IV_LENGTH)
  cipher = crypto.createCipheriv cipher_algoritm, key, iv
  encrypted = cipher.update(str) #+ cipher.final('hex')
  encrypted = Buffer.concat([encrypted, cipher.final()])
  ret = iv.toString('hex') + ':' + encrypted.toString('hex')
  # console.log('+++++encrypted=',ret)
  return ret

exports.decrypt = decrypt = (str,key, cipher_algoritm = CIPHER_ALG)->
  textParts = str.split(':')
  iv = Buffer.from(textParts.shift(), 'hex')
  encryptedText = Buffer.from(textParts.join(':'), 'hex')
  decipher = crypto.createDecipheriv(cipher_algoritm, key, iv)
  decrypted = decipher.update(encryptedText)# + decipher.final('utf8')
  decrypted = Buffer.concat([decrypted, decipher.final()])
  ret = decrypted.toString()
  # console.log('--------ret',ret)
  return ret

# NOTE:crypto.createDecipher is deprecated
# exports.decryptOld = decryptOld = (str,key, cipher_algoritm = 'aes-128-ofb')->
#   decipher = crypto.createDecipher(cipher_algoritm, key)
#   debug.debug 'decipher',decipher
#   decipher.update(str, 'hex', 'utf8') + decipher.final('utf8')

exports.randomEncrypt = randomEncrypt = (str,key)->
  text = randomSum str,key
  encrypt text,key

exports.randomDecrypt = randomDecrypt = (str,key)->
  text = decrypt str,key
  checkRandomSum text,key


# encrypt(data+salt)+sum(encrypt(data+salt))
# 
exports.shareEncode = shareEncode = (str,key)->
  unless str or key
    debug.error 'Error, No Str!'
    return 'Error No str'
  # console.log str + ':' + typeof str + ':' + key
  try
    ts = (Date.now() + Math.floor(Math.random() * 234)).toString(16).substr(-4)
    estr = encrypt (str),key
    sum  = createSum (ts + estr),key,6
    return ts+estr+sum
  catch e
    debug.error e
    return null

# decrypt(data) -> data+salt
# checkSum(data+salt)
exports.shareDecode = shareDecode = (str='',key)->
  try
    str = str + ''
    pass = checkSum str.slice(0,-6),key,str.substr(-6)
    return [false, null] unless pass
    data = decrypt str.slice(4,-6),key
    return [true, data]
  catch err
    # node v14提示Invalid IV length, v16以上提示Invalid initialization vector
    if (/length/ig.test err.toString()) or (/initialization vector/ig.test err.toString())
      # data = decryptOld str.slice(4,-6),key
      # 忽略打开旧版本分享的error信息
      return [false,null]
    else
      debug.error "str:#{str},key:#{key},error:",err
      return [false,null]

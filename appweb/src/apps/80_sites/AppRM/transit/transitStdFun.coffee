# TransitRouteRegion = COLLECTION  'vow', 'transit_route_region'
# TransitStop = COLLECTION  'vow', 'transit_stop'
# TransitRouteShape = COLLECTION 'vow','transit_route_shape'
ProvAndCity = MODEL 'ProvAndCity'
Transit = MODEL 'Transit'
debug = debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger() #3, 3
helpers_object = INCLUDE 'lib.helpers_object'
# TRANSIT_REGIONS = { path: '/img/map-layer/transitRouteRegionWithStop/' }
{simplify} = INCLUDE 'lib.simplifyGeojson'
{sprintf,vsprintf} = INCLUDE 'lib.sprintf'
# TransitRouteShape.createIndex {prov:1,route_tp:1,tp:1,'bboxP': '2dsphere'}

# STR_TRANSIT_REGION = 'Transit_Region'
# PLSWAIT STR_TRANSIT_REGION

TRANSIT_ROUTE_TP = {}
TRANSIT_ROUTE_TP_MAP = {}
TRANSIT_ROUTE_TP_DEPENDENCY = 'app_transit_route_tps'
PLSWAIT TRANSIT_ROUTE_TP_DEPENDENCY
GTA_REGIONS = ['Toronto','Ajax','Aurora','Brampton','Caledon',\
  'Burlington','Durham','Gwillimbury','Markham','Mississauga',\
  'Milton','Newmarker','Oakville','Pickering','Richmond Hill',\
  'York','Vaughan','Whitby']
GVA_REGIONS = ['Vancouver','Burnaby','Coquitlam','Richmond','Victoria']
# TRANSIT_PROVS_REGION_LIST = [
#   {region:GTA_REGIONS,v:'GTA',k:'GTA'}, #bbox
#   {region:GVA_REGIONS,v:'GVA',k:'GVA'},
#   {prov:'AB',v:'Alberta',k:'AB'},
#   {prov:'QC',v:'Quebec',k:'QC'},
#   {prov:'NS',v:'Nova Scotia',k:'NS'}
#   {prov:'SK',v:'Saskatchewan',k:'SK'}
# ]
# CUST_REGIONS = ['GTA','GVA']
# REGIONS_LIST = []
# PROVS_LIST = []
# for r in TRANSIT_PROVS_REGION_LIST
#   if r.region
#     REGIONS_LIST = REGIONS_LIST.concat r.region
#   if r.prov
#     PROVS_LIST.push r.prov
TRANSIT_TYPES = [
  {route_tp:'Sub',v: 'Sub'}
  {route_tp:'Rail',v: 'Rail'}
  {route_tp:'Bus',v: 'Bus'}
  {route_tp:'Tram',v: 'Tram'}
  {route_tp:'Ferry',v: 'Ferry'}
]

# TRANSIT_REGION_FIELDS = {
#   prov:1,
#   region:1,
#   route_tp:1,
#   agency:1,
#   img:1,
#   bbox:1,
#   urlBase:1,
#   colorCnt:1,
#   bnds:1
# }

# zoom = 11, big area
# zoom = 17 very small area on map
# reducePoints = (coordinates,zoom)->
#   tolerance = 0.0008
#   highQuality = false
#   if zoom is 17
#     tolerance = 0.0001
#     highQuality = true
#   # tolerance 0.1-5
#   return simplify(coordinates,tolerance,highQuality)

# generateFeat = (feat)->
#   tmp = {
#     nm:feat?.properties?.nm,
#     color:feat?.properties?.color,
#     route_id:feat?.properties?.route_id
#   }
#   # if feat?.geometry?.coordinates
#   #   if feat.geometry.type is 'LineString'
#   #   # console.log 'prevLength',feat.geometry.coordinates.length
#   #     tmp.coords14 = [reducePoints(feat.geometry.coordinates,14)]
#   #     tmp.coords17 = [reducePoints(feat.geometry.coordinates,17)]
#   #     console.log('+++++++',tmp.coords14.length,tmp.coords17.length)
#   #   else if feat.geometry.type is 'MultiLineString'
#   #     tmp.coords14 = []
#   #     tmp.coords17 = []
#   #     for i in feat.geometry.coordinates
#   #       tmp.coords14.push reducePoints(i,14)
#   #       tmp.coords17.push reducePoints(i,17)
#   #     console.log '+++++MultiLineString',tmp.coords14.length,tmp.coords14[0].length
#   return tmp

# generateRegion = (tr)->
#   tmp = {
#     urlBase:tr.urlBase,
#     colorCnt:tr.colorCnt,
#     prov:tr.prov,
#     rgn:tr.region,
#     tp:tr.tp,
#     rttp:tr.route_tp,
#     img:tr.img,
#     bbox:tr.bbox,
#     feat:[]
#   }
#   if tr?.bnds?.features?.length
#     for feat in tr.bnds.features
#       tmp.feat.push generateFeat(feat)
#   return tmp

# TODO:
# TransitRouteRegion.findToArray {},{project:TRANSIT_REGION_FIELDS},(err,transRegions)->
#   throw err if err
#   TRANSIT_REGIONS.rgns = []
#   for tr in transRegions
#     TRANSIT_REGIONS.rgns.push generateRegion(tr)
#   # console.log TRANSIT_REGIONS
#   PROVIDE STR_TRANSIT_REGION

# TODO 1, add route points
# 2, reduce points according to zoom level
gProvLocMap = {
  'NL':{lat:53.135509,lng:-57.660435}
  'AB':{lat:53.621422794,lng:-113.5180460102}
  'BC':{lat:50.726669,lng:-123.647621}
  'NS':{lat:44.673461378,lng:-63.56275085}
  'ON':{lat:43.76610,lng:-79.50824}
  'QC':{lat:46.813602,lng:-71.2146}
  'SK':{lat:52.1952016241,lng:-106.7120482586324}
  'PE':{lat:46.25,lng:-63}
  'MN':{lat:56.415211,lng:-98.739075}
  'NB':{lat:46.498390,lng:-66.159668}
}
getLocFromProv = (prov)->
  return gProvLocMap[prov]
getCustRegionName = (region,prov)->
  ret = []
  if region in GTA_REGIONS
    k1 = 'GTA'
    k2 = 'ON'
    loc1 = {lat:43.7172885576,lng:-79.3999684788}
    loc2 = loc1
    # return [{k:'GTA',zoom:14},{k:'ON',isProv:1,zoom:18}]
  else if region in GVA_REGIONS
    k1 = 'GVA'
    k2 = 'BC'
    loc1 = {lat:49.2214,lng:-123.016}
    loc2 = loc1
    # return [{k:'GVA',zoom:14},{k:'BC',isProv:1,zoom:18}]
  else
    k1 = null
    k2 = prov
    loc2 = getLocFromProv(prov)
  ret.push {k:k1,v:k1,zoom:10.7,loc:loc1} if k1
  ret.push {k:k2,v:k2,zoom:7,isProv:1,loc:loc2} if k2
  return ret #[{k:prov,isProv:1,zoom:18},null]
getProvFromCustRegionName = (region)->
  if (region is 'GTA') or (region in GTA_REGIONS)
    return 'ON'
  else if (region is 'GVA') or (region in GVA_REGIONS)
    return 'BC'
  else if /^[A-Z]{2}$/.test region
    return region
  # TODO: for now, ON regions not in GTA are here, future may be BC region not in GVA
  return null

getRegionFromCustRegionName = (region)->
  if (region is 'GTA') #or (region in GTA_REGIONS)
    return GTA_REGIONS
  else if (region is 'GVA') #or (region in GVA_REGIONS)
    return GVA_REGIONS
  else
    return null
# sortByTransitType = (a,b)->
#   if a.route_tp is b.route_tp
#     return 0
#   if (a.route_tp is 'Sub') and (b.route_tp isnt 'Sub')
#     return -1
#   if (a.route_tp is 'Rail') and (b.route_tp in ['Bus','Tram','Ferry'])
#     return -1
#   if (a.route_tp is 'Ferry') and (b.route_tp in ['Bus','Tram'])
#     return -1
#   if (a.route_tp is 'Tram') and (b.route_tp is 'Bus')
#     return -1
#   return 1

# getValue = (tp)->
#   if tp in ['Sub']
#     return 0
#   else if tp in ['Rail','Ferry']
#     return 1
#   else if tp in ['Bus','Tram']
#     return 2
#   else
#     return 3
# sortByTransitType = (a,b)->
#   return getValue(a) - getValue(b)
getAreaValue = (a)->
  tp = a.k
  if tp in ['GTA']
    return 0
  else if tp in ['ON','GVA']
    return 1
  else if tp in ['BC','QC']
    return 2
  else
    return 3
sortByArea = (a, b)->
  return getAreaValue(a) - getAreaValue(b)

Transit.getTransitRouteType (transitRouteTp)->
  TRANSIT_ROUTE_TP = transitRouteTp
  ret = {}
  # appendToRet = (reg,ele)->
  #   ret[reg] ?= []
  #   found = ret[reg].indexOf(ele) > -1
  #   ret[reg].push ele if not found
      
  # ret = {Bus:[{GTA},'GVA']}
  for tmp in TRANSIT_ROUTE_TP #tmp = {_id:{region,prov,transit_tp}}
    {region,prov,route_tp} = tmp._id
    ret[route_tp] ?= {}
    [k1,k2] = getCustRegionName(region,prov)
    ret[route_tp][k1.k] = k1 if k1
    ret[route_tp][k2.k] = k2 if k2
  for k,v of ret
    ret[k] = Object.values(v).sort(sortByArea)
  #   # This region not in displayed list(region&prov), add to list,
  #   if region in REGIONS_LIST
  #     # append to cust region
  #     regionBelongsTo = getCustRegionName(region)
  #     appendToRet(regionBelongsTo,route_tp)
  #   else if prov in PROVS_LIST
  #     # append to cust prov
  #     appendToRet(region,route_tp)
  #     appendToRet(prov,route_tp)
  #   else
  #   # if not ((region in REGIONS_LIST) or (prov in PROVS_LIST))
  #     found = TRANSIT_PROVS_REGION_LIST.find((ele)->
  #       ele.v is region)
  #     if not found
  #       TRANSIT_PROVS_REGION_LIST.push {region:[region],v:region,k:region,isProv:false,isCity:true}#lat,lng,bbox
  #       REGIONS_LIST.push region
  #     appendToRet(region,route_tp)
  #     appendToRet(prov,route_tp)
  # for k,arr of ret
  #   arr.sort sortByTransitType
  debug.debug 'TRANSIT_ROUTE_TP: ret->',ret
  # console.log('+++++++TRANSIT_ROUTE_TP',TRANSIT_ROUTE_TP)
  TRANSIT_ROUTE_TP_MAP = ret
  PROVIDE TRANSIT_ROUTE_TP_DEPENDENCY

# TODO: @rain available to all user? needLogin?

#routeType by prov
# {ON: [{nm:'Sub-TTC',transit_tp:'Sub',agency:'TTC'},{nm:'Rail-Go',...}]}
ADDSTDFN 'getRouteTpsByProv',{needReq:true,userObject:true},(opt,cb)->
  req = opt.req
  locale = req.locale()

  userTransitMap = helpers_object.deepCopyObject TRANSIT_ROUTE_TP_MAP
  # provs = helpers_object.deepCopyObject TRANSIT_PROVS_REGION_LIST
  routeTypes = helpers_object.deepCopyObject TRANSIT_TYPES
  # console.log '++++userTransitMap',userTransitMap
  for tp,areas of userTransitMap
    for region in areas
      region.v = req.l10n(region.v, 'provience')#+'-'+ transit.agency
      # console.log region.v,req.l10n(region.v, 'provience')
  # for region, arr of userTransitMap
  #   tmp = []
  #   for transit in arr
  #     tmp.push {
  #       nm:req.l10n(transit, 'transit')#+'-'+ transit.agency
  #       route_tp:transit
  #     }
  #   userTransitMap[region] = tmp
  # for opt in provs
  #   opt.v = req.l10n(opt.v, 'transit')#
  
  for tp in routeTypes
    tp.v = req.l10n(tp.v)#
    # console.log tp.v,req.l10n(tp.v, 'transit')
  # console.log '+++routeTypes',routeTypes
  ret = {map:userTransitMap,routeTypes,ok:1}
  cb null,ret

# 合并线路图
# ADDSTDFN 'getTransitRegions',(opt,cb)->
#   cb null,TRANSIT_REGIONS


# ADDSTDFN 'getTransitProvs',(opt,cb)->
  # cb null,{mapping:TRANSIT_PROVS}


# POST 'getTransitProvs',(req,resp)->
  # resp.send {mapping:TRANSIT_PROVS}

ADDSTDFN 'getTransitRoutes',{needReq:true,userObject:true},(opt,cb)->
  # if opt.prov in CUST_REGIONS
  req = opt.req
  opt.region = getRegionFromCustRegionName(opt.region)
  opt.prov = getProvFromCustRegionName(opt.prov)
  Transit.getTransitRoutes opt, (err,routes)->
    return cb err if err
    ret = {ok:1,routes:routes}
    if not routes.length
      # TODO: l10n
      route_tp = req.l10n(opt.route_tp)
      ret.msg = sprintf(req.l10n("No %s routes found in this area"), route_tp)
      # req.l10n('No routes found in this area') # or for this type
    cb null, ret


# POST 'getTransitRoutes',(req,resp)->
#   opt = req.body
#   debug.debug 'getTransitRoutes'
#   Transit.getTransitRoutes opt,(err,routes)->
#     debug.debug 'error',err,routes
#     if err
#       return resp.send {ok:0,err:err}
#     resp.send {ok:1,routes:routes}


# getStopNameShort = (stop={})->
#   nm = stop.nm[0] or ''
#   if /-/.test(nm)
#     return nm.split('-')[0].replace('STATION','').trim()
#   return nm
# NOTE: GET single route info
ADDSTDFN 'getRouteInfo',(opt,cb)->
  return cb 'No id',null unless (opt.id)
  Transit.getTransitRoutesById opt.id, (err, route)->
    return cb err if err
    return cb null,{ok:1,route:route}

# POST 'getRouteInfo',(req,resp)->
#   opt=req.body
#   Transit.getTransitRoutesById opt.id, (err, route)->
#     return resp.send err if err
#     return resp.send {ok:1,route:route}


# format stop names
# parseStops = (stops)->
#   stops.fnm = stop.nm or stop.
ADDSTDFN 'getStopInfo',(opt,cb)->
  return cb 'No id',null unless (opt.id)
  Transit.getTransitStopById opt.id, (err, stop)->
    return cb err if err
    return cb null,{ok:1,stop:stop}

# POST 'getStopInfo',(req,resp)->
#   opt=req.body
#   Transit.getTransitStopById opt.id, (err, stop)->
#     return resp.send err if err
#     return resp.send {ok:1,stop:stop}


# POST 'getTransitStops',(req,resp)->
#   opt = req.body
#   unless (opt.bbox or opt.route_shape_id)
#     return resp.send {ok:0, err:MSG_STRINGS.BAD_PARAMETER}
#   if opt.bbox
#     return Transit.getTransitStopsByBbox {
#       bbox: opt.bbox,
#       zoom: opt.zoom,
#       agency: opt.agency,
#       route_tp: opt.route_tp,
#       prov: opt.prov
#       },(err, stops)->
#         return resp.send  err if err
#         return resp.send {ok:1,stops:stops}
#   if opt.route_shape_id
#     console.log 'opt.route_shape_id',opt.route_shape_id
#     Transit.getTransitStopsByRouteShapeId opt.route_shape_id,(err,stops)->
#       return resp.send  err if err
#       return resp.send {ok:1,stops:stops}

# TODO: on map !== list, list show 2 stops. map show 1;
# TODO: routes, render in front end
# stop->nm:[]
# get stops from transit route

# if opt.route_shape_id, get squenced stops in route
# if bbox, get all stops within bbox
# if zoom > 14, get unmerged stops.
MIN_MAP_STOPS = 10
isLatLngInBbox = (stop={},bbox=[])->
  {lat,lng} = stop
  if(bbox[0] <= lng and lng <= bbox[2] and bbox[1] <= lat and lat <= bbox[3])
    return true
  return false
MAP_STOP_LIMIT = 25
ADDSTDFN 'getTransitStops',{needReq:true},(opt,cb)->
  unless (opt.bbox or opt.route_shape_id)
    return cb null, {ok:0, err:MSG_STRINGS.BAD_PARAMETER}
  req = opt.req
  # opt.prov = getProvFromCustRegionName(opt.prov)
  # 1 find shape stops or find stops in bbox when no selectedRoute,
  # 2 still find stops in bbox when < 10 stops in bbox from selectedRoute
  # 3 merge and remove duplicate
  # if opt.route_shape_id
  delete opt.req
  debug.debug 'opt=>',opt
  opt.limit ?= MAP_STOP_LIMIT
  done = ({err = null,stops,extras=[],cnt=0})->
    if err
      return cb err
    return cb null,{ok:1,stops,extras,cnt}
  # selected stop maybe child stop, but ids is parent.ids
  selectedStop = opt.selectedStop or ''
  selectedStopIds = opt.selectedStopIds or []
  try
    {err,stops} = await Transit.getTransitStopsByRouteShapeId opt.route_shape_id
  catch error
    return cb error
  if err
    return done({err})
  extraOpts = {
    bbox: opt.bbox,
    zoom: opt.zoom,
    # agency: opt.agency,
    route_tp: [], #opt.route_tp,
    # region:opt.region,
    limit:Math.min(opt.limit,MAP_STOP_LIMIT),
    # prov: opt.prov,
    # route_shape_id:opt.route_shape_id,
  }
  extraOpts.except = opt.except if opt.except
  stopsMap = {}
  for s in stops
    stopsMap[s._id] = 1
    s.fromRoute = 1
  try
    {err,mapStops,cnt} = await Transit.getTransitStopsByBbox extraOpts
  catch error
    return cb error
  if err
    return done({err})
  newStops = []
  for s in mapStops
    s.extra = true if selectedStop
    if req.verGTE('6.2.2')
      if (s._id isnt selectedStop) and \
        # (not (s._id in selectedStopIds)) and \
        not stopsMap[s._id]
          newStops.push s
  return done({stops,extras:newStops,cnt})

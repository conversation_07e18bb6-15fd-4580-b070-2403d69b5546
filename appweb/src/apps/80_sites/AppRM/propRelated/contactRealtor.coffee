APP '1.5'
APP 'contactRealtor',true
UserModel = MODEL 'User'
PropertiesModel = MODEL 'Properties'
ProjectModel = MODEL 'Project'
FormInputModel = MODEL 'FormInput'
SysDataModel = MODEL 'SysData'
debug = DEBUG()
gConfig = CONFIG(['share'])
contactRealtorHelper = INCLUDE 'libapp.contactRealtor'
{respError} = INCLUDE 'libapp.responseHelper'
common = INCLUDE 'libapp.common'
CONTACT_REALTOR_VALUES = common.CONTACT_REALTOR_VALUES
followUpBossLib = INCLUDE 'libapp.followUpBoss'
{getPropShareUrl} = INCLUDE 'libapp.propWeb'
{currencyFormat} = INCLUDE 'libapp.properties'
getDotHtml = DEF 'getDotHtml'
userLib = INCLUDE 'libapp.user'
UserCol = COLLECTION 'chome','user'
popularCities = DEF 'popularCitiesWithOwnControlData'

# prop: {
#   saletp: [ '出售' ],
#   saletp_en: [ 'Sale' ],
#   _id: 'TRBN12134075',
#   sid: 'N12134075',
#   addr: '65 Hillmount Rd',
#   id: 'TRBN12134075',
#   city: '万锦',
#   prov: '安大略省',
#   city_en: 'Markham',
#   prov_en: 'Ontario',
#   lp: 1188800,
#   ptype2: [ '镇屋', '外接式' ]
# },
isValidProp = (prop,page)->
  return false unless prop
  if (page is CONTACT_REALTOR_VALUES.EVALUATION) and (prop._id or prop.id)
    return true
  return false unless (prop._id and prop.id)
  # BUG: eng user has no city_en
  # return false unless (prop.city or prop.city_en)
  # return false unless (prop.prov or prop.prov_en)
  # BUG: not all has tp
  # return false unless (prop.tp or prop.tp_en)
  return true

###
#POST /1.5/contactRealtor
input:
page:(*) ['landLordRent','project','mls','rmlisting','evaluation','buyTrustedAssign','trustedAssignment','saleTrustedAssign','assignment']
hasWechat

#

prop:{_id,addr,city,prov,sid,uid}

return
{ok:0,needLogin:true}
return {ok:1,ret:{
  action: {
    UIType:[], emun[form, agentCard]
    formDest: string, emun ['landLordRent','project','mls','rmlisting','evaluation','projectToCrm','upgradeVip']
    showChat: false
  },
  agentInfo:{nm,eml,placeHolder,mbl,avt,wesite}
  formInfo:{formPlaceHolder,nm, eml,mbl}
}
formDest will be used as form type.

###
POST '',(req, resp) ->
  body = req.body
  page = body.page
  src = body.src or req.getDevType()
  # unless page and url
  unless page
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
  hasWechat = body.hasWechat
  prop = body.prop
  if not (isValidProp(prop,page))
    debug.error 'invalid body:',body
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
  locale = req.locale()
  UserModel.appAuth {req,resp}, (user) ->
    # current logic all case require login, so do not check key again.
    # check key in future when user can fill form even not logged in.
    isBuyOrSalePage = (page in [CONTACT_REALTOR_VALUES.BUY_TRUSTEDASSIGNMENT,\
    CONTACT_REALTOR_VALUES.SALE_TRUSTEDASSIGNMENT,CONTACT_REALTOR_VALUES.TRUSTED_ASSIGNMENT,\
    CONTACT_REALTOR_VALUES.PROJECT,CONTACT_REALTOR_VALUES.ASSIGNMENT])
    # 没有user并且page不是buyTrustedAssign，saleTrustedAssign,trustedAssignment,project,assignment提醒登录
    if (not user) and (not isBuyOrSalePage)
      return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),resp,category:MSG_STRINGS.NEED_LOGIN}
    user = user or {}
    hasWechat = UserModel.hasWechat(user,{hasWechat})
    params = {
      MSG_STRINGS,gConfig,ProjectModel,PropertiesModel
      UserModel,locale,
      user,hasWechat,page,src,prop,
      popularCities,SysDataModel
    }
    try
      ret = await contactRealtorHelper.getContactRealtor params
    catch err
      # NOTE: Do not log config and model into log
      params2 = {
        locale,
        user,hasWechat,page,src,
        prop,
      }
      debug.error err,' params:',params2
      return respError {clientMsg:req.l10n(err),category:err,resp}
    ret.action.title_tr = req.l10n(ret.action.title) if ret.action?.title?
    if ret?.formInfo?.formPlaceHolder
      ret.formInfo.formPlaceHolder = req.l10n ret.formInfo.formPlaceHolder
      if ret?.formInfo?.formPlaceHolderLocation
        ret.formInfo.formPlaceHolder += " #{ret.formInfo.formPlaceHolderLocation}"
    # if ret?.action?.UIType and (CONTACT_REALTOR_VALUES.SHOW_REALTOR_YELLOW_PAGE in ret.action.UIType)
    #   #TOCONFIRM, back to where？
    #   ret.action.yellowPageUrl = "/1.5/user/follow?d=/1.5/index&city=#{prop.city}\
    #   &prov=#{prop.prov}&cityName=#{req.l10n prop.city}"
    # if ret.mailBody# TODO: tranlation has problem
    #   ret.mailBody = req.l10n ret.mailBody
    resp.send {ok:1, ret}

CONTACT_ASAP = 'Our stuff will contact you ASAP'
CONTACT_NEXT_BUSINESS_DAY = 'Our stuff will contact you on next business day'

getFormMsg = (isOnDuty) ->
  return if isOnDuty then CONTACT_ASAP else CONTACT_NEXT_BUSINESS_DAY

GET 'manage',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'crmAdmin',user
      return resp.ckup 'generalError',{err:"Not Authorized"}
    SysDataModel.findById 'contactRealtor',(err,ret)->
      isWorking = ret?.isWorking or false
      replyDate = ret?.replyDate or ''
      status = getFormMsg isWorking
      resp.ckup 'admin/contactRealtor',{status,isWorking,replyDate}

POST 'updateSysdata',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'crmAdmin',user
      return resp.ckup 'generalError',{err:"Not Authorized"}
    update = req.body or {}
    try
      await SysDataModel.updateById 'contactRealtor',update
    catch err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),sysErr:err,resp}
    status = getFormMsg update.isWorking
    resp.send {ok:1,status}

POST 'updateCrmUserStatus',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'crmAdmin',user
      return resp.ckup 'generalError',{err:"Not Authorized"}
    {id,crStat} = req.body
    try
      await UserModel.setUserContactRealtorStatus {id,crStat}
      return resp.send {ok:1}
    catch err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),sysErr:err,resp}
      
# waht is this used for ? front desk control.
POST 'getCrmUsers',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'crmAmdin',user
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED),sysErr:err,resp}
    {uid,eml} = req.body or {}
    try
      users = await FormInputModel.getCrmUsers {uid,eml}
      return resp.send {ok:1,users}
    catch err
      respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),sysErr:err,resp}

POST 'getContactRealtorUserStatus',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless user
      return respError {
        clientMsg:req.l10n(MSG_STRINGS.NOT_LOGGEDIN),
        sysErr:err,
        resp}
    statusMapping =
      working: CONTACT_ASAP
      offwork: CONTACT_NEXT_BUSINESS_DAY
      assigned: 'We are working on your request. Our agent will contact you when the appointment confirmed.'
      called: 'Our stuff called you, but nobody pick up the phone. you can call us at ************'
    try
      formProps = await FormInputModel.getFormInputList {uid:user?._id}
      contactRealtorData = await SysDataModel.findById 'contactRealtor'
      user = await UserCol.findOne {_id:user._id}
      if crStat = user?.crStat
        status = statusMapping.called if crStat is 'called'
        status = statusMapping.assigned if crStat is 'assigned'
      else if contactRealtorData?.isWorking
        status = statusMapping.working
      else
        status = statusMapping.offwork
      return resp.send {ok:1,formProps,status,statusTl:'Request status'}
    catch err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),sysErr:err,resp}
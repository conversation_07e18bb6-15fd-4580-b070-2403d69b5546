###
# 学校相关方法集合
#
# 使用说明:
# 1. 在 tpls/config.coffee->apps 中添加 '<%this.srcPath%>/model' 来使用此模块
#
# 设计目标:
# 1. 黑盒 API - 从 mongo 切换到 SQL 时无需修改代码
# 2. DRY (Don't Repeat Yourself)
#
# 数据库索引:
# - School 集合:
#   db.school.createIndex({addr:1,loc:1,geoq:1})
#   db.school.createIndex({loc:1})
#   db.school.createIndex({'loc.0':1,'loc.1':1})
#   db.school.createIndex({'bnds.features.geometry':'2dsphere'})
#
# - 地图搜索优化:
#   db.school.createIndex({lat:1,lng:1,IsActive:1,addr:1})
#   db.private_schools.createIndex({lat:1,lng:1,hide:1})
###

debug = DEBUG()
helpers = INCLUDE 'lib.helpers'
geolib = INCLUDE 'geolib'
i18n = INCLUDE 'lib.i18n'

SchoolRmMergedCol = COLLECTION 'vow','sch_rm_merged'
SchoolEqaoCol = COLLECTION 'vow','sch_eqao'
SchoolFraserCol = COLLECTION 'vow','sch_fraser'
SchoolRmManualCol = COLLECTION 'vow','sch_rm_manual'
SchoolFindschoolCol = COLLECTION 'vow','sch_findschool'
SchoolStatCol = COLLECTION 'vow','sch_stat'
PrivateSchools = COLLECTION 'vow','private_schools'
University = COLLECTION 'vow','university'
SchoolBoards = COLLECTION 'vow','school_board'
SchoolError = COLLECTION 'vow','school_error'

boundaryHelper = INCLUDE 'libapp.boundaryHelper'
cityHelper = INCLUDE 'lib.cityHelper'
libUser = INCLUDE 'libapp.user'
{formatChart,formatRmRankPub,formatRmRankKeyFacts,addKeyFacts,mergeArrayByYearmark,formatEqaoStudents,filterMergeFields,\
  formatRmRankPir,formatRmRankScore,formatAdjRank,formatIvyRank,generateSchoolName,delSchoolCommWord} = INCLUDE 'libapp.schoolHelper'
{hasCensusChartData} = INCLUDE 'libapp.censusHelper'
censusYear = '2021'
{checkAndTranslate} = INCLUDE 'libapp.propertiesTranslate'
{ separateUnitFromAddr } = INCLUDE 'lib.propAddress'
{unifyAddress,filterOtherThanTel} = INCLUDE 'lib.helpers_string'
{ checkSchoolType,
  checkSchoolProgram
} = INCLUDE 'libapp.schoolWeight_helper'

# 在主模式下创建索引
config = CONFIG(['serverBase'])
if config.serverBase?.masterMode
  PrivateSchools.createIndex({sex:1},{background:true})
  PrivateSchools.createIndex({rmRanking: 1,top:1},{background:true})
  PrivateSchools.createIndex({nm:1},{background:true})
  PrivateSchools.createIndex({IsActive:1},{background:true})
  PrivateSchools.createIndex({weight: 1},{background:true})
  PrivateSchools.createIndex({lat:1,lng:1,hide:1,IsActive:1},{background:true})

  University.createIndex({tp:1},{background:true})
  University.createIndex({IsActive:1},{background:true})
  University.createIndex({weight:1},{background:true})
  University.createIndex({nm:1},{background:true})
  University.createIndex({'campus.city': 1,'campus.prov': 1},{background:true})
  University.createIndex({'campus.lat': 1,'campus.lng': 1},{background:true})

  SchoolEqaoCol.createIndex({eqaoId:1})

# 基础学校字段
SCHOOL_FIELDS_BASIC = {
  _id:1,
  IsActive:1,
  nm:1,
  loc:1,
  bns:1,
  gf:1,
  gt:1,
  fi:1,
  ef:1,
  ele:1,
  mid:1,
  hgh:1,
  catholic:1,
  eng:1,
  pub:1,
  tp:1,
  ap:1,
  ib:1,
  art:1,
  sport:1,
  gif:1,
  addr:1,
  eqao:1,
  fraser:1,
  hot:1,
  tel:1,
  url:1,
  fax:1,
  sbid:1,
  rmRank:1,
  rmEqao:1,
  rmFraser:1,
  hasCensus:1,
  hasAdj:1,
  firate:1,
  fitotal:1,
  firank:1,
  boardInfo:1,
  sourceId:1
}
# 扩展学校字段
SCHOOL_FIELDS_EXTEND = Object.assign {
  city:1,
  nation:1, 
  prov:1,
  zip:1
}, SCHOOL_FIELDS_BASIC

# 基础私立学校字段
PRIVATE_SCHOOL_FIELDS_BASIC = {
  _id:1,
  IsActive:1,
  lat:1,
  lng:1,
  hot:1,
  nm:1,
  city:1,
  addr:1,
  tp:1,
  pub:1,
  religion:1,
  sex:1,
  grd:1,
  tuitn:1,
  tuitnBoarding:1,
  fndd:1,
  boarding:1,
  admIntvw:1,
  admSSAT:1,
  ivyRank:1,
  adjSummary:1,
  adjDetail:1,
  rmRanking:1,
  rmScore:1,
  tuitnPreYear:1,
  top:1
}

# 扩展私立学校字段
PRIVATE_SCHOOL_FIELDS_EXTEND = Object.assign {
  city:1,
  sz:1
}, PRIVATE_SCHOOL_FIELDS_BASIC

# 学校类型映射及颜色
schTypeMapping = {
  ele: { nm: 'Elementary', color: '#F1F8EC', textColor: '#5cb85c' }
  mid: { nm: 'Middle', color: '#F1F8EC', textColor: '#5cb85c' }
  hgh: { nm: 'Secondary', color: '#F1F8EC', textColor: '#5cb85c' }
  university: { nm: 'University', color: '#F1F8EC', textColor: '#5cb85c' }
  college: { nm: 'College', color: '#F1F8EC', textColor: '#5cb85c' }
  fi: { nm: 'French Immersion', color: '#DAE3F3', textColor: '#007aff' }
  ef: { nm: 'Extended French', color: '#DAE3F3', textColor: '#007aff' }
  eng: { nm: 'English', color: '#DAE3F3', textColor: '#007aff' }
  catholic: { nm: 'Catholic', color: '#FDF1E8', textColor: '#e03131' }
  top: { nm: 'TOP', color: '#FDF1E8', textColor: '#e03131' }
  boarding: { nm: 'Boarding', color: '#DAE3F3', textColor: '#007aff' }
  sex: { nm: '', color: '#DAE3F3', textColor: '#007aff' }
  tuitnPreYear: { nm: '', color: '#DAE3F3', textColor: '#007aff' }
  campus: { nm: 'Campus', color: '#DAE3F3', textColor: '#007aff' }
  campuses: { nm: 'Campuses', color: '#DAE3F3', textColor: '#007aff' }
  pri: { nm: 'Private', color: '#DAE3F3', textColor: '#007aff' }
  chtr: { nm: 'Charter', color: '#DAE3F3', textColor: '#007aff' }
  sprt: { nm: 'Separate', color: '#DAE3F3', textColor: '#007aff' }
  frcp: { nm: 'Francophone', color: '#DAE3F3', textColor: '#007aff' }
  idp: { nm: 'Independent', color: '#DAE3F3', textColor: '#007aff' }
  frna: { nm: 'First Nation', color: '#DAE3F3', textColor: '#007aff' }
  ptst: { nm: 'Protestant', color: '#DAE3F3', textColor: '#007aff' }
  ib: { nm: 'IB', color: '#DAE3F3', textColor: '#007aff' }
  ap: { nm: 'AP', color: '#DAE3F3', textColor: '#007aff' }
  art: { nm: 'Art', color: '#DAE3F3', textColor: '#007aff' }
  gif: { nm: 'Gifted', color: '#DAE3F3', textColor: '#007aff' }
  sport: { nm: 'Sport', color: '#DAE3F3', textColor: '#007aff' }
}

# 公立学校排序映射
PUBLIC_SORT_MAPPING = {
  fraser: {'firank':1}
  nm: {'nm':1}
  eqaog3: {'rmG3Ranking':1}
  eqaog6: {'rmG6Ranking':1}
  eqaog9: {'rmG9Ranking':1}
}

###*
# 添加单个标签
# @param {Array} tags - 标签数组
# @param {String} type - 标签类型
# @param {String} [name] - 可选的标签名称
# @return {void}
###
addSingleTag = (tags, type, name) ->
  tag = Object.assign {}, schTypeMapping[type]
  tag.nm = name if name?
  tags.push tag

###*
# 处理私立学校标签
# @param {Object} sch - 学校对象
# @param {Array} tags - 标签数组
# @return {void}
###
handlePrivateSchoolTag = (sch, tags) ->
  tagConfigs = [
    { field: 'top', value: 1 }
    { field: 'sex', useFieldAsName: true }
    { field: 'boarding', value: 'Yes' }
    { field: 'tuitnPreYear', useFieldAsName: true, fallback: 'tuitn' }
  ]

  for config in tagConfigs
    if config.value? and (sch[config.field] is config.value)
      addSingleTag(tags, config.field)
    else if config.useFieldAsName and (fieldValue = sch[config.field] or sch[config.fallback])
      addSingleTag(tags, config.field, fieldValue)

###*
# 处理公立学校标签
# @param {Object} sch - 学校对象
# @param {Array} tags - 标签数组
# @return {void}
###
handlePublicSchoolTag = (sch, tags) ->
  publicFields = ['ele', 'mid', 'hgh', 'catholic', 'fi', 'ef', 'eng', 'pri', 'chtr',
                 'sprt', 'frcp', 'idp', 'frna', 'ptst', 'ib', 'ap', 'art', 'gif', 'sport']
  
  for field in publicFields when (sch[field] is 1) or (sch[field] is 'Yes')
    addSingleTag(tags, field)

###*
# 为学校添加标签
# @param {Object} sch - 学校对象
# @param {String} [locale='en'] - 语言环境
# @return {void}
###
addTags = (sch, locale='en') ->
  return unless sch
  _t = i18n.getFun locale
  tags = []

  if sch.tp in ['university', 'college']
    addSingleTag(tags, sch.tp)
    campusType = if sch.campus?.length is 1 then 'campus' else 'campuses'
    addSingleTag(tags, campusType)
  else if sch.private
    handlePrivateSchoolTag(sch, tags)
  else
    handlePublicSchoolTag(sch, tags)

  for tag in tags
    tag.nm = checkAndTranslate(_t, tag.nm, 'school')

  if sch.tp in ['university', 'college']
    tags[1]?.nm = "#{sch.campus?.length} #{tags[1]?.nm}"#翻译后添加校区数量

  sch.tags = tags

###*
# 过滤边界字段
# @param {Object} bnd - 边界对象
# @param {Array} fields - 需要保留的字段数组
# @return {Object} 过滤后的对象
###
filterBoundByFields = (bnd, fields) ->
  return {} unless bnd
  ret = {}
  for k in fields when bnd[k]?
    ret[k] = bnd[k]
  ret

# 程序字段
gProgarmFields = ['fi','ef','ap','ib','sport','gif','ef','art','eng']

###*
# 获取边界键值
# @param {Object} bnd - 边界对象
# @return {String} 边界键值
###
getBoundaryKey = (bnd) ->
  key = "#{if bnd.gf <= 0 then 'K' else bnd.gf}-#{bnd.gt}"
  for fld in gProgarmFields when bnd[fld]
    key = "#{key}_#{fld}"
  key

###*
# 获取公立学校简称
# @param {String} [nm=''] - 学校名称
# @return {String} 学校简称
###
getShortPubSchoolName = (nm='') ->
  nm.toUpperCase()
    .replace(' Junior and Senior Public School','')
    .replace('Junior Public School','JP')
    .replace('Senior Public School','SP')
    .replace('Public School','PS')
    .replace('Catholic School','CS')
    .replace(' Community School','')

###*
# 清理学校名称
# @param {String} [nm=''] - 学校名称
# @return {String} 清理后的名称
###
clearSchoolName = (nm='') ->
  nm.replace(/\s(Public|School|Elementory|Middle|Secondary|Catholic)/gi, '')

###*
# 按照年级分类。各bound的年级merge到school上。
# used for map school with multiple boundary
# 聚合边界
# @param {Object} sch - 学校对象
# @param {String} locale - 语言环境
# @return {void}
###
aggregateBounds = (sch, locale) ->
  return unless sch.bns
  bnsObj = {}
  
  for bn, i in sch.bns
    bnKey = getBoundaryKey(bn)
    bnsObj[bnKey] ?= {
      bnid: bn.bnid,
      gf: bn.gf,
      gt: bn.gt,
      eng: bn.eng,
      i: [],
      sw: bn.sw,
      ne: bn.ne
    }
    
    bnsObj[bnKey].extStr = ''
    for fld in gProgarmFields when bn[fld]
      bnsObj[bnKey][fld] = bn[fld]
      bnsObj[bnKey].extStr += "#{fld.toUpperCase()} "
    bnsObj[bnKey].extStr = bnsObj[bnKey].extStr.trim()
    bnsObj[bnKey].i.push i

  bns = []
  for k, bn of bnsObj
    addTags(bn, locale)
    bns.push(bn)
  sch.bns = bns

###*
# 减少Fraser和EQAO数据
# @param {Object} school - 学校对象
# @return {void}
###
reduceFraserEqao = (school) ->
  if (f = school.fraser)?.length > 0
    school.fraser = f[0]
  if (e = school.eqao)?.length > 0
    school.eqao = e[0]

###*
# 更新学校信息
# @param {Object} sch - 学校对象
# @return {void}
###
updateSch = (sch) ->
  #代码在native，没loc给[]
  sch.loc ?= []
  
  updateGrade = (obj) ->
    if (obj.gf is 0) or (obj.gf is -1) or (typeof obj.gf is 'undefined')
      obj.gf = 'K'
      
  if sch.bns
    updateGrade(bn) for bn in sch.bns
  updateGrade(sch)

  if (sch.pub or sch.tp is 'Public') or sch.catholic
    sch.schTp = if sch.catholic then 'catholic' else 'public'
    sch.grade = sch.gf + (if sch.gt then ('-' + sch.gt) else '')
  else
    sch.schTp = 'private'
    sch.grade = sch.grd

  if sch.fraser?.length > 0 or sch.rmFraser
    sch.fraser = mergeArrayByYearmark(sch.fraser, sch.rmFraser)
    frs = sch.fraser[0]
    sch.firate = frs.firate
    # sch.firank = frs.firank + '/' + frs.fitotal
    sch.firank = frs.firank
    sch.fitotal = frs.fitotal
    if frs.filast5rank and frs.filast5total
      sch.filast5rank = "#{frs.filast5rank}/#{frs.filast5total}"
      sch.schRank = sch.filast5rank
  if sch.eqao or sch.rmEqao
    eqao = sch.eqao or []
    if rmEqao = sch.rmEqao
      eqao = mergeArrayByYearmark(eqao, rmEqao)
  if (eqao = sch.eqao?[0])?
    # 学校卡片针对EQAO：g3与g6优先显示g6，g9的信息
    if eqao.g9rank and eqao.g9total
      sch.eqaorate = "#{eqao.g9rank}/#{eqao.g9total} G9"
      sch.G9rank = eqao.g9rank
      sch.G9total = eqao.g9total
    else if eqao.g6rank and eqao.g6total
      sch.eqaorate = "#{eqao.g6rank}/#{eqao.g6total} G6"
      sch.G6rank = eqao.g6rank
      sch.G6total = eqao.g6total
    else if eqao.g3rank and eqao.g3total
      sch.eqaorate = "#{eqao.g3rank}/#{eqao.g3total} G3"
      sch.G3rank = eqao.g3rank
      sch.G3total = eqao.g3total

###*
# 减少边界数据
# @param {Object} bn - 边界对象
# @param {Object} sch - 学校对象
# @return {Object} 减少后的边界对象
###
reduceBn = (bn, sch) ->
  {
    bnid: bn.bnid
    exbnid: bn.exbnid
    exclude: bn.exclude
    gf: bn.gf
    gt: bn.gt
    ele: bn.ele
    mid: bn.mid
    hgh: bn.hgh
    fi: bn.fi
    ef: bn.ef
    eng: bn.eng
    catholic: sch.catholic
    useSch: bn.useSch
    sw: bn.sw
    ne: bn.ne
    ap: bn.ap
    ib: bn.ib
    art: bn.art
    sport: bn.sport
    gif: bn.gif
  }

###*
# 检查位置是否在学校边界内
# @param {Object} sch - 学校对象
# @param {Array} bnds - 边界ID数组
# @param {Array} loc - 位置坐标
# @param {Boolean} noFi - 是否排除法语学校
# @param {Array} returnSchools - 返回的学校数组
# @param {String} locale - 语言环境
# @return {void}
###
ifInSchoolBoundaries = (sch, bnds, loc, noFi, returnSchools, locale) ->
  matchedBns = {}
  sch.bns ?= []

  # 有bnid时用bnid match, 没有要用loc去计算是否match
  for bn in sch.bns when (bnds?.indexOf(bn?.bnid) >= 0) or (loc and helpers.isInBoundary(bn.bn, loc))
    matchedBns[bn.bnid] = reduceBn(bn, sch)

  return if Object.keys(matchedBns).length < 1

  for bnid, bn of matchedBns when bn.exclude is 1
    delete matchedBns[bnid]
    delete matchedBns[bn.exbnid] if bn.exbnid

  return if Object.keys(matchedBns).length < 1

  #返回的school的bns只有homeschool的。
  delete sch.bns

  if sch.loc?[0]? and loc?[0]?
    try
      sch.dist = geolib.getDistance(
        {latitude: sch.loc[0], longitude: sch.loc[1]},
        {latitude: loc[0], longitude: loc[1]}
      )
    catch e
      debug.error 'ifInSchoolBoundaries sch:', sch.loc, loc
      debug.error e
      sch.dist = ''

  for k, bn of matchedBns when not (noFi and (bn.fi or bn.ef))
    addTags(bn, locale)
    schoolBnd = Object.assign({}, sch, bn, {bns: [bn]})
    returnSchools.push(schoolBnd)

###*
# 比较具有一个边界的学校
# @param {Object} a - 第一个学校对象
# @param {Object} b - 第二个学校对象
# @return {Number} 比较结果
###
compareSchoolWithOneBoundary = (a, b) ->
  _compareSch(_getCompareObj(a), _getCompareObj(b))

###*
# 获取比较对象
# @param {Object} sch - 学校对象
# @return {Object} 用于比较的对象
###
_getCompareObj = (sch) ->
  if sch.bns?.length > 0
    return if sch.bns[0].useSch then sch else sch.bns[0]
  sch

###*
# 比较学校
# @param {Object} a - 第一个学校对象
# @param {Object} b - 第二个学校对象
# @return {Number} 比较结果
###
_compareSch = (a, b) ->
  return 1 if b.eng and not a.eng
  return -1 if a.eng and not b.eng
  
  return 1 if a.catholic and not b.catholic
  return -1 if b.catholic and not a.catholic

  # extended French
  return 1 if a.ef and not b.ef
  return -1 if b.ef and not a.ef
  
  # french immersion
  return 1 if a.fi and not b.fi
  return -1 if b.fi and not a.fi

  # program gif/ib/ap/art/sport
  hasSpecial = (x) -> x.gif or x.ib or x.ap or x.art or x.sport
  return 1 if hasSpecial(a) and not hasSpecial(b)
  return -1 if hasSpecial(b) and not hasSpecial(a)

  # grade
  (parseInt(a.gf) or 0) - (parseInt(b.gf) or 0)

###*
# 设置学校类型
# @param {Object} sch - 学校对象
# @return {void}
###
setUpSchoolType = (sch) ->
  return unless sch.tp
  
  typeMap =
    'Catholic': 'catholic'
    'Private': 'pri'
    'Charter': 'chtr'
    'Separate': 'sprt'
    'Francophone': 'frcp'
    'Independent': 'idp'
    'First Nation': 'frna'
    'Protestant': 'ptst'

  if typeField = typeMap[sch.tp]
    sch[typeField] = true

###*
# 使用边界框构建查询
# @param {Array} bbox - 边界框坐标
# @return {Object} 查询对象
###
buildQueryWithBbox = (bbox) ->
  [swLng, swLat, neLng, neLat] = bbox
  {
    $and: [
      {'lat': {$lt: neLat}},
      {'lat': {$gt: swLat}},
      {'lng': {$lt: neLng}},
      {'lng': {$gt: swLng}}
    ]
  }

###*
# 构建查询
# @param {Object} params - 查询参数
# @param {Array} [params.bbox] - 边界框
# @param {String} [params.prov] - 省份
# @param {String} [params.city] - 城市
# @param {Array} [params.filter] - 过滤条件
# @param {String} [params.nm] - 名称
# @return {Object} 查询对象
###
buildQuery = ({bbox, prov, city, filter, nm}) ->
  query = {}
  
  if prov
    query.prov = cityHelper.getProvAbbrName(prov)
  if city
    query.city = city

  if nm
    nm = nm.replace(/[^-'\u4e00-\u9eff0-9a-zA-Z\u00C0-\u00FF ]/g,'')
      .replace(/[\s-']+/g, '.*')
      .replace(/[aA]/g,'[aàâ]')
      .replace(/[cC]/g,'[cç]')
      .replace(/[eE]/g,'[eèéêë]')
      .replace(/[iI]/g,'[iîï]')
      .replace(/[oO]/g,'[oôö]')
      .replace(/[uU]/g,'[uùûü]')
    query.nm = new RegExp('.*' + nm + '.*', 'i')

  if bbox
    Object.assign(query, buildQueryWithBbox(bbox))

  if filter?.length
    qNor = []
    qOr = []
    qAnd = []

    for segments in filter
      for k, v of segments
        if k is 'top'
          qAnd.push({top: 1})
        else if k in ['pub','catholic','eng','fi','ef','ele','mid','hgh','ib','ap','gif','art','sport']
          qAnd.push({[k]: v})
        else if not segments.noGenderRestrict
          qOr.push('Co-Ed') if segments.coedu
          qOr.push('All-Girls') if segments.girls
          qOr.push('All-Boys') if segments.boys
          query.sex = {$in: qOr} if qOr.length > 0

    if qAnd.length > 0
      query.$and ?= []
      query.$and.push({$and: qAnd})

    if qNor.length > 0
      query.$nor = qNor

  query

###*
# 通过ID构建查询
# @param {String|Number} id - 学校ID
# @return {Object} 查询对象
由于不能确定值是什么，还需要考虑查询不同的表，所以用包涵所有情况
merged表：_id 是 省份+6位数字，sourceId 是 manual表或findschool表的_id
manual表：_id 是 String S+省份+school.nm.toUpperCase()
findschool表：_id 是 int 7位数字
###
buildQueryById = (id) ->
  {$or: [{_id: parseInt(id)}, {_id: id},{sourceId: parseInt(id)}, {sourceId: id}]}


###*
# 翻译学校信息
# @param {Object} sch - 学校对象
# @param {String} [locale='en'] - 语言环境
# @return {void}
###
translateSch = (sch, locale='en') ->
  return unless sch
  _t = i18n.getFun(locale)
  
  if sch.city
    sch.city_en = sch.city
    sch.city = _t(sch.city)
  if sch.prov
    sch.prov_en = sch.prov
    sch.prov = _t(sch.prov, 'municipal')

###*
# 处理列表显示
# @param {Object} sch - 学校对象
# @param {String} locale - 语言环境
# @return {void}
###
dealForList = (sch, locale) ->
  updateSch(sch)
  addTags(sch, locale)
  translateSch(sch, locale)
  formatRmRankKeyFacts(sch)
  addKeyFacts(sch, i18n.getFun(locale))
  

  delete sch.eqao
  delete sch.rmEqao
  delete sch.rmRank
  delete sch.rmFraser
  
  sch.canExchange = (sch.rmRankKeyFacts and Object.keys(sch.rmRankKeyFacts).length > 0) or sch.hasCensus or sch.hasAdj

###*
# 构建学校关联查询的pipeline stages
# @param {Boolean} indetail - 是否需要详细信息
# @return {Array} pipeline stages数组
###
buildRmSchoolLookupStages = (indetail) ->
  pipeline = buildRankLookupStages()
  pipeline = pipeline.concat(buildSchRankLookupStages())

  if indetail
    pipeline = pipeline.concat(buildFraserLookupStages())
    pipeline = pipeline.concat(buildSchEqaoLookupStages())
    pipeline = pipeline.concat(buildSchoolLookupStages())
  else
    pipeline = pipeline.concat(buildFindSchoolEqaoLookupStages())
    pipeline = pipeline.concat(buildCensusLookupStages())

  return pipeline

###*
# 构建EQAO关联查询stages
# @return {Array} pipeline stages数组
###
buildRankLookupStages = ->
  [
    # 关联 sch_rm_rank 表
    # 展开 eqaoId 数组
    {
      $unwind: {
        path: "$eqaoId",
        preserveNullAndEmptyArrays: true
      }
    }
    
    # 关联 sch_rm_rank 表
    {
      $lookup: {
        from: 'sch_rm_rank',
        localField:'eqaoId',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              rmRank: 1,
            }
          }
        ],
        as: 'rmRankData'
      }
    }
    {
      $addFields:
        rmRank: { $arrayElemAt: ['$rmRankData.rmRank', 0] }
    }
  ]

###*
# 构建Fraser关联查询stages
# @return {Array} pipeline stages数组
###
buildFraserLookupStages = ->
  [
    # 关联 sch_fraser 表
    {
      $lookup: {
        from: 'sch_fraser',
        localField:'fraserId',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              fraser: 1
            }
          }
        ],
        as: 'fraserData'
      }
    },
    {
      $addFields:
        rmFraser: { $arrayElemAt: ['$fraserData.fraser', 0] }
    }
  ]

###*
# 构建Rank关联查询stages
# @return {Array} pipeline stages数组
###
buildSchRankLookupStages = ->
  [
    # 关联 sch_rm_rank 表
    {
      $lookup:
        from: 'sch_rm_rank'
        localField: '_id'
        foreignField: '_id'
        pipeline: [
          {
            $project: {
              ivyRank: 1,
              adjSummary: 1,
              adjDetail: 1,
              top100: 1
            }
          }
        ],
        as: 'rmRankData'
    }
    {
      $addFields:
        ivyRank: { $arrayElemAt: ['$rmRankData.ivyRank', 0] },
        adjSummary: { $arrayElemAt: ['$rmRankData.adjSummary', 0] },
        adjDetail: { $arrayElemAt: ['$rmRankData.adjDetail', 0] },
        top100: { $arrayElemAt: ['$rmRankData.top100', 0] },
        hasAdj: {
          $or: [
            { $gt: [{ $size: { $ifNull: ['$rmRankData.ivyRank', []] } }, 0] },
            { $gt: [{ $size: { $ifNull: ['$rmRankData.adjDetail', []] } }, 0] }
          ]
        }
    }
  ]


###*
# 构建EQAO关联查询stages
# @return {Array} pipeline stages数组
###
buildSchEqaoLookupStages = ->
  [
    # 展开 eqaoId 数组
    {
      $unwind: {
        path: "$eqaoId",
        preserveNullAndEmptyArrays: true
      }
    }
    
    # 关联 sch_eqao 表
    {
      $lookup: {
        from: 'sch_eqao',
        localField:'eqaoId',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              eqao: 1
            }
          }
        ],
        as: 'eqaoData'
      }
    }
    
    {
      $addFields:
        rmEqao: { $arrayElemAt: ['$eqaoData.eqao', 0] }
    }
  ]

###*
# 构建School关联查询stages
# @return {Array} pipeline stages数组
###
buildSchoolLookupStages = ->
  [
    # 关联 sch_findschool 表
    {
      $lookup:
        from: 'sch_findschool'
        localField: 'sourceId'
        foreignField: '_id'
        as: 'findSchoolData'
    }
    # 关联 sch_rm_manual 表
    {
      $lookup:
        from: 'sch_rm_manual'
        localField: 'sourceId'
        foreignField: '_id'
        as: 'rmManualData'
    }
    # 重构文档,按照优先级合并字段
    {
      $replaceRoot: {
        newRoot: {
          $mergeObjects: [
            { $arrayElemAt: ['$findSchoolData', 0] }, # findschool表数据最终覆盖
            '$$ROOT', # sch_rm_merged表数据
            { $arrayElemAt: ['$rmManualData', 0] },
            {
              rmEqao:'$rmEqao', 
              rmFraser:'$rmFraser',
              boardInfo:'$boardInfo',
              rmRank: '$rmRank', 
              ivyRank: '$ivyRank',
              adjSummary: '$adjSummary',
              adjDetail: '$adjDetail'
            }
          ]
        }
      }
    }
  ]

###*
# 构建FindSchool的eqao关联查询stages
# @return {Array} pipeline stages数组
###
buildFindSchoolEqaoLookupStages = ->
  [
    # 关联 sch_findschool 表获取 eqao
    {
      $lookup: {
        from: 'sch_findschool',
        localField: 'sourceId',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              eqao: { $arrayElemAt: ['$eqao', 0] }
            }
          }
        ],
        as: 'findSchoolData'
      }
    }
    
    # 关联 sch_rm_manual 表获取 eqao
    {
      $lookup: {
        from: 'sch_rm_manual',
        localField: 'sourceId',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              eqao: { $arrayElemAt: ['$eqao', 0] }
            }
          }
        ],
        as: 'rmManualData'
      }
    }
    
    # 使用 $ifNull 合并两个来源的 eqao
    {
      $addFields: {
        eqao: {
          $ifNull: [
            { $arrayElemAt: ['$findSchoolData.eqao', 0] },
            { $arrayElemAt: ['$rmManualData.eqao', 0] }
          ]
        }
      }
    }
    
    # 清理中间字段
    {
      $project: {
        findSchoolData: 0,
        rmManualData: 0
      }
    }
  ]

###*
# 构建Census关联查询stages
# @return {Array} pipeline stages数组
###
buildCensusLookupStages = ->
  [
    # 关联 school_census - 处理数字类型 _id
    {
      $lookup:
        from: 'school_census'
        let: { schoolId: '$sourceId' }
        pipeline: [
          { $match: { $expr: { $eq: ['$_id', '$$schoolId'] } } }
          { $project: { _id: 1 } } # 只返回 _id 用于判断是否存在
        ]
        as: 'censusData'
    }
    
    # 如果没有找到匹配，尝试字符串匹配
    {
      $lookup:
        from: 'school_census'
        let: { schoolId: { $toString: '$sourceId' } }
        pipeline: [
          { $match: { $expr: { $eq: ['$_id', '$$schoolId'] } } }
          { $project: { _id: 1 } } # 只返回 _id 用于判断是否存在
        ]
        as: 'censusDataStr'
    }
    
    {
      $addFields:
        hasCensus: {
          $or: [
            { $ne: [{ $size: '$censusData' }, 0] },
            { $ne: [{ $size: '$censusDataStr' }, 0] }
          ]
        }
    }
  ]

# 检查 _id是否是 省份+6位数字格式
checkIdFormat = (id) ->
  if not id
    return false
  if typeof id isnt 'string'
    return false
  return /^[A-Z]{2}\d{6}$/.test(id)

###*
# School 类 - 提供学校相关的数据操作方法
#
# @class School
# @description 包含了公立学校、私立学校和大学的数据操作方法
#
# 主要功能:
# - 获取学校列表和详情
# - 保存和更新学校信息
# - 统计学校访问量
# - 处理学校边界数据
#
# 数据来源:
# - SchoolRmMergedCol: 合并后的学校数据
# - SchoolFindSchoolCol: 学校基础数据
# - PrivateSchools: 私立学校数据
# - University: 大学数据
###
class School
  ###*
  # 获取城市列表
  # @return {Promise<Array>} 城市列表
  ###
  @getDistinctCityList: ->
    await SchoolRmMergedCol.distinct('city')

  ###*
  # 获取家庭学校,调用getSchoolsByQuery，得到home school
  # @param {Object} options - 查询参数
  # @param {Array} options.schs - 预先计算的学校ID
  # @param {Array} options.bnds - 预先计算的边界ID
  # @param {Array} options.loc - 属性位置
  # @param {Boolean} options.noFi - 仅返回英语学校
  # @param {String} [options.locale='en'] - 语言环境
  # @param {Boolean} [options.noBnd=false] - 学校地图不显示边界
  # @param {Object} [options.fields=null] - 可选的字段指定
  # @return {Promise<Array>} 学校列表
  ###
  @getHomeSchools: ({
    schs, # 预先计算的学校ID
    bnds, # 预先计算的边界ID
    loc,  # 属性位置
    noFi, # 仅返回英语学校
    locale = 'en',
    noBnd = false, # 学校地图不显示边界
    fields = null # 可选的字段指定
  }) ->
    if not (loc or schs or bnds)
      throw new Error(MSG_STRINGS.BAD_PARAMETER)

    lng = parseFloat(loc?[1]) or 0
    lat = parseFloat(loc?[0]) or 0

    if Array.isArray(schs)
      q = {$or: [{_id: {$in: schs}}, {sourceId: {$in: schs}}]}
    else
      q = {
        IsActive: 1
        'bnds.features.geometry': {
          $geoIntersects: {
            $geometry: {
              type: 'Point'
              coordinates: [lng, lat]
            }
          }
        }
      }

    pipeline = [
      {$match: q}
    ]

    pipeline = pipeline.concat(buildRmSchoolLookupStages())

    fields = fields or SCHOOL_FIELDS_EXTEND
    pipeline.push({$project: fields})

    schools = await SchoolRmMergedCol.aggregate(pipeline)
    returnSchools = []

    if noBnd
      returnSchools = schools or []
    else
      for sch in (schools or [])
        ifInSchoolBoundaries(sch, bnds, loc, noFi, returnSchools, locale)

    returnSchools.sort(compareSchoolWithOneBoundary)
    
    for sch in returnSchools
      dealForList(sch, locale)
      sch.gf = 'K' if sch.gf<=0
      if sch.loc?[0]? and loc?[0]?
        try
          sch.dis = Math.ceil(
            geolib.getDistance(
              {latitude: sch.loc[0],longitude: sch.loc[1]},
              {latitude: loc[0], longitude: loc[1]}
            )/100
          )/10
    return returnSchools


  ###*
  # 获取学校列表
  # @param {Object} params - 查询参数
  # @param {String} params.curSchoolType - 学校类型 'public'/'private'/'university'/'college'
  # @param {String} [params.nm] - 学校名称
  # @param {String} [params.prov] - 省份
  # @param {String} [params.city] - 城市
  # @param {Array} [params.filter] - 过滤条件 [{'mid':1,'ib':1},{'ele':1,'ib':1}]
  # @param {Array} [params.bbox] - 地图范围 [swLng,swLat,neLng,neLat]
  # @param {Object} [params.sort] - 排序
  # @param {Number} [params.page=0] - 页码
  # @param {String} [params.locale='en'] - 语言
  # @param {Number} [params.limit=50] - 每页数量
  # @param {Boolean} [params.needExtendFields=false] - 是否需要扩展字段
  # @param {Boolean} [params.showOnMap] - 是否显示在地图上
  # @param {Array} [params.fields] - 指定返回字段
  # @return {Array|Object} 学校列表或带计数的对象
  ###
  @getSchools:({
    curSchoolType, #public or private,'university','college'
    nm, #optional,school name to search
    prov, #optional
    city, #optional
    filter, # [{'mid':1,'ib':1},{'ele':1,'ib':1}]，[$and:[{$or}]]
    bbox, #bbox:[swLng,swLat,neLng,neLat]
    sort, #optional
    page=0,
    locale='en', #user lang
    limit=50,
    needExtendFields=false, #optional,需要全部字段
    showOnMap,
    fields=null #optional, 指定返回的字段, ['_id', 'nm', 'addr']
    })->
      params = {
        nm,
        prov,
        city,
        filter,
        bbox,
        sort,
        page,
        locale,
        limit,
        needExtendFields,
        showOnMap,
        fields
      }
      if curSchoolType is 'private'
        ret_pv = privateSchList = await @getPrivateSchools params
        return ret_pv
      else if curSchoolType is 'public'
        ret_pb = await @getPublicSchools params
        return ret_pb
      else if curSchoolType is 'college'
        params.tp = 'college'
        ret_col = await @getUniversities(params)
        return ret_col
      else if curSchoolType is 'university'
        params.tp = 'university'
        ret_uni = await @getUniversities(params)
        return ret_uni
      else #query both
        publicSchList = await @getPublicSchools params
        privateSchoolList = await @getPrivateSchools params
        universitiesList= await @getUniversities params
        schList = publicSchList.concat(privateSchoolList).concat(universitiesList)
        schList.sort (sch1, sch2)->
          return sch1.nm > sch2.nm
        return schList

  ###*
  # 获取带计数的学校列表
  # @param {Object} params - 查询参数
  # @param {String} params.curSchoolType - 学校类型 'public'/'private'/'university'/'college'
  # @param {String} [params.nm] - 学校名称
  # @param {String} [params.prov] - 省份
  # @param {String} [params.city] - 城市
  # @param {Array} [params.filter] - 过滤条件 [{'mid':1,'ib':1},{'ele':1,'ib':1}]
  # @param {Array} [params.bbox] - 地图范围 [swLng,swLat,neLng,neLat]
  # @param {Object} [params.sort] - 排序
  # @param {Number} [params.page=0] - 页码
  # @param {String} [params.locale='en'] - 语言
  # @param {Number} [params.limit=50] - 每页数量
  # @param {Boolean} [params.needExtendFields=false] - 是否需要扩展字段
  # @param {Boolean} [params.showOnMap] - 是否显示在地图上
  # @param {Array} [params.fields] - 指定返回字段
  # @return {Object} 包含学校列表和总数的对象 {schList: Array, cnt: Number}
  ###
  @getSchoolsWithCnt: ({
    curSchoolType,
    nm,
    prov, 
    city,
    filter,
    bbox,
    sort,
    page=0,
    locale='en',
    limit=50,
    needExtendFields=false,
    showOnMap,
    fields=null
    })->
      params = {
        nm,
        prov,
        city, 
        filter,
        bbox,
        sort,
        page,
        locale,
        limit,
        needExtendFields,
        showOnMap,
        fields
      }

      if curSchoolType is 'private'
        ret_pv = await @getPrivateSchoolsWithCnt params
        return ret_pv
      else if curSchoolType is 'public'  
        ret_pb = await @getPublicSchoolsWithCnt params
        return ret_pb
      else if curSchoolType is 'college'
        params.tp = 'college'
        ret_col = await @getUniversitiesWithCnt params
        return ret_col
      else if curSchoolType is 'university'
        params.tp = 'university'
        ret_uni = await @getUniversitiesWithCnt params
        return ret_uni
      else #query both
        {schList: publicSchList, cnt: pbCnt} = await @getPublicSchoolsWithCnt params
        {schList: privateSchoolList, cnt: pvCnt} = await @getPrivateSchoolsWithCnt params
        {schList: universitiesList, cnt: uniCnt} = await @getUniversitiesWithCnt params
        
        schList = publicSchList.concat(privateSchoolList).concat(universitiesList)
        schList.sort (sch1, sch2)->
          return sch1.nm > sch2.nm
        
        totalCnt = pbCnt + pvCnt + uniCnt
        return {schList, cnt: totalCnt}

  #filter params:
  #
  #'city','pub','catholic','eng','fi','ef','ele','mid','hgh','ib','ap','gif','art','sport'
  ###*
  # 获取公立学校列表
  # @param {Object} params - 查询参数
  # @param {String} [params.nm] - 学校名称
  # @param {String} [params.prov] - 省份
  # @param {String} [params.city] - 城市
  # @param {Array} [params.filter] - 过滤条件 [{'mid':1,'ib':1},{'ele':1,'ib':1}]
  # @param {Array} [params.bbox] - 地图范围 [swLng,swLat,neLng,neLat]
  # @param {Object} [params.sort] - 排序
  # @param {Number} [params.page=0] - 页码
  # @param {String} [params.locale='en'] - 语言
  # @param {Number} [params.limit=50] - 每页数量
  # @param {Boolean} [params.needExtendFields=false] - 是否需要扩展字段
  # @param {Boolean} [params.showOnMap=false] - 是否显示在地图上
  # @param {Array} [params.fields] - 指定返回字段
  # @return {Array} 公立学校列表
  ###
  @getPublicSchools:({
    nm, #optional
    prov, #optional
    city, #optional
    filter, #optional [{'mid':1,'ib':1},{'ele':1,'ib':1}]，[$and:[{$or}]]
    bbox, #optional bbox:[swLng,swLat,neLng,neLat]
    sort, #optional
    page=0,
    locale='en',
    limit=50,
    needExtendFields=false, #需要全部字段
    showOnMap=false,
    fields=null #optional, 指定返回的字段, ['_id', 'nm', 'addr']
    })->
      # 构建基础查询条件
      query = buildQuery({bbox,prov,city,nm,filter})
      query = Object.assign query, {IsActive:1, addr:{$ne:null}}
      
      # 添加排序相关的查询条件
      if sort?.tp is 'fraser'
        query['fraserId'] = {$ne:null}
        query['firank'] = { $ne: null }
      if /eqaog/.test sort?.tp
        query['rmG'+sort.tp.slice(-1)+'Ranking'] = {$ne:null}
      
      pipeline = [
        # 匹配条件
        { $match: query }
      ]
      
      # 添加排序
      if PUBLIC_SORT_MAPPING[sort?.tp]
        pipeline.push({ $sort: PUBLIC_SORT_MAPPING[sort.tp] })
      else
        pipeline.push({ $sort: {weight: -1} })

      # 添加分页
      if page
        pipeline.push({ $skip: page * limit })
      pipeline.push({ $limit: limit + 1 })
      # 添加 lookup stages
      pipeline = pipeline.concat(buildRmSchoolLookupStages())
      # 如果需要限制字段
      if needExtendFields
        fields = fields or SCHOOL_FIELDS_EXTEND
        pipeline.push({ $project: fields})

      schools = await SchoolRmMergedCol.aggregate(pipeline)
      returnSchools = []
      schools ?= []
      for sch in schools
        dealForList sch,locale
        aggregateBounds sch,locale
        sch.snm = getShortPubSchoolName(sch.nm)
        sch.ssnm = clearSchoolName(sch.nm)
        sch._id = ''+sch._id
        if showOnMap
          sch.fnm = sch.nm
          sch.nm = sch.fnm.replace(/\s(Public|School|Elementory|Middle|Secondary|Catholic)/gi, '')
        if needExtendFields
          reduceFraserEqao sch
        returnSchools.push sch
      return returnSchools

  ###*
  # 获取带计数的公立学校列表
  # @param {Object} params - 查询参数
  # @param {String} [params.nm] - 学校名称
  # @param {String} [params.prov] - 省份
  # @param {String} [params.city] - 城市
  # @param {Array} [params.filter] - 过滤条件 [{'mid':1,'ib':1},{'ele':1,'ib':1}]
  # @param {Array} [params.bbox] - 地图范围 [swLng,swLat,neLng,neLat]
  # @param {Object} [params.sort] - 排序
  # @param {Number} [params.page=0] - 页码
  # @param {String} [params.locale='en'] - 语言
  # @param {Number} [params.limit=50] - 每页数量
  # @param {Boolean} [params.needExtendFields=false] - 是否需要扩展字段
  # @param {Boolean} [params.showOnMap=false] - 是否显示在地图上
  # @param {Array} [params.fields] - 指定返回字段
  # @return {Object} 包含公立学校列表和总数的对象 {schList: Array, cnt: Number}
  ###
  @getPublicSchoolsWithCnt = ({
    nm,
    prov,
    city,
    filter,
    bbox,
    sort,
    page=0,
    locale='en',
    limit=50,
    needExtendFields=false,
    showOnMap=false,
    fields=null,
    isOnlyCnt=false
    })->
      unless isOnlyCnt
        returnSchools = await @getPublicSchools({
          nm,
          prov,
          city,
          filter,
          bbox,
          sort,
          page,
          locale,
          limit,
          needExtendFields,
          showOnMap,
          fields
        })
      
      query = buildQuery({bbox, prov, city, nm, filter})
      query = Object.assign query, {IsActive: 1, addr: {$ne: null}}
      
      if sort?.tp is 'fraser'
        query['fraserId'] = {$ne:null}
        query['firank'] = { $ne: null }
      if /eqaog/.test sort?.tp
        query['rmG'+sort.tp.slice(-1)+'Ranking'] = {$ne:null}

      cnt = await SchoolRmMergedCol.countDocuments query
      return {schList: returnSchools or [], cnt}

  ###*
  # 根据ID获取学校董事会信息
  # @param {String} sbid - 学校董事会ID
  # @return {Object} 学校董事会信息
  ###
  @getSchoolBoardById:(sbid)->
    if not sbid
      throw new Error('no school board id')
    board = await SchoolBoards.findOne {_id:sbid}
    return board

  ###*
  # 根据ID获取公立学校详细信息
  # @param {Object} params - 查询参数
  # @param {String} params.id - 学校ID
  # @param {String} [params.locale='en'] - 语言
  # @param {Object} [params.schBn] - 学校边界信息
  # @param {Boolean} [params.allFraser] - 是否获取所有Fraser信息
  # @param {Boolean} [params.noBounds=0] - 是否不需要边界信息
  # @param {Boolean} [params.noAggregate=0] - 是否不需要聚合信息
  # @param {Boolean} [params.canExchange=0] - 是否可以交换
  # @return {Object} 公立学校详细信息
  ###
  @getPublicSchoolById:({
    id, #required
    locale='en',
    schBn,#optional, the boundary need to returned
    allFraser, #optional
    noBounds=0,
    noAggregate = 0,
    canExchange = 0,
    showCurBnTxt = false
    })->
      if not id
        throw new Error('id required')

      q = {addr:{$ne:null}, IsActive: 1}
      q = Object.assign q, buildQueryById id
      pipeline = [
        # 匹配条件
        { 
          $match: q
        }
        # 关联 school_board
        { 
          $lookup: {
            from: 'school_board',
            localField: 'sbid',
            foreignField: '_id',
            as: 'boardInfo'
          }
        }
      ]
      
      pipeline.push({ $limit: 1 })
      
      # 添加 lookup stages
      pipeline = pipeline.concat(buildRmSchoolLookupStages(true))
      
      pipeline.push({ $project: Object.assign(SCHOOL_FIELDS_EXTEND, {ivyRank:1, adjSummary: 1,adjDetail:1})})
      schools = await SchoolRmMergedCol.aggregate(pipeline)
      school = schools[0]
      
      if not school
        return null

      if school.bns and (not noBounds)
        if schBn
          bns = []
          for bn in school.bns
            bn.gf = 'K' if bn.gf<=0
            notEqual = false
            for fld in gProgarmFields
              debug.debug 'fld',fld,'bn[fld]',bn[fld],'schBn[fld]',schBn[fld]
              if bn[fld] isnt (schBn[fld] or 0)
                notEqual = true
            if (bn.gf is schBn.gf) and (bn.gt is schBn.gt) and (not notEqual)
              bns.push filterBoundByFields bn,\
                ['bnid','gf','gt','eng','exbnid','exclude','bn','tags'].concat(gProgarmFields)
          school.cBns = bns # current selected boundaries
          if school.cBns[0]
            for fld in gProgarmFields
              #overwrite sch with bnd.
              school[fld]=school.cBns[0][fld]
        unless noAggregate
          aggregateBounds school,locale
      if noBounds # schoolR detail do not need bnds
        delete school.bns
        delete school.bnds
      if not allFraser #schoolR detail need all fraser, map only need first
        reduceFraserEqao school
      setUpSchoolType(school)
      translateSch school,locale
      addTags school,locale
      updateSch school
      if canExchange and (school.rmRank or school.adjSummary or school.ivyRank) and (school.prov_en is 'ON')
        school.rankMap = {}
        formatEqaoStudents school
        formatRmRankScore school
        # formatChart school
        formatRmRankPub school
        formatIvyRank school
        formatAdjRank school
      formatRmRankKeyFacts school
      addKeyFacts(school, i18n.getFun(locale), showCurBnTxt)
      return school

  ###*
  # 根据ID保存公立学校信息
  # @param {Object} params - 保存参数
  # @param {String} params.id - 学校ID
  # @param {String} [params.nm] - 学校名称
  # @param {String} [params.prov] - 省份
  # @param {String} [params.city] - 城市
  # @param {String} [params.zip] - 邮编
  # @param {String} [params.tel] - 电话
  # @param {String} [params.url] - 网址
  # @param {String} [params.addr] - 地址
  # @param {Array} [params.loc] - 位置
  ###
  @savePublicSchoolById:({
    id, #required
    nm,
    prov,
    city,
    zip,
    tel,
    url,
    addr,
    loc,
    IsActive,
    eqaoId,
    fraserId,
    coll
    })->
      if not id
        throw new Error('id required')
      obj = {}
      params = {
        nm,
        prov,
        city,
        zip,
        tel,
        url,
        addr,
        loc,
        IsActive,
        eqaoId,
        fraserId
      }

      for fld, val of params
        if val?
          obj[fld] = val
          if fld is 'loc'
            obj.lat = val[0]
            obj.lng = val[1]
      obj.mt = new Date()
      {transactionOptions, session} = GETTRANSACTION 'vow'
      try
        session.startTransaction(transactionOptions)

        orig = await School.getPublicSchoolByIdAndColl(id,coll)

        # 检查是否至少有一个表中存在该学校数据
        unless orig && Object.keys(orig).length > 0
          throw new Error "Not exists #{id}"

        updateFields = {}
        # 更新与orig中数据不同的字段
        for fld in Object.keys(obj)
        # 使用严格不等判断，避免类型转换导致的误判
          if orig[fld]?.toString() isnt obj[fld]?.toString()
            # 同时更新orig对象和updateFields对象
            orig[fld] = updateFields[fld] = obj[fld]

        # 修改findschool表中的数据的时候，保存到manual的数据中没有nm,city,prov字段，导致搜索不出来对应学校
        manaulUpdateFields = {
          'nm':orig.nm,
          'city':orig.city,
          'prov':orig.prov,
          'IsActive':orig.IsActive,
          ...updateFields
        }
        id = orig._id
        if checkIdFormat(id)
          if updateFields.eqaoId?.length > 0
            eqaoData = await SchoolEqaoCol.findOne {_id:updateFields.eqaoId.toString()}
            updateFields.eqaoId = [updateFields.eqaoId]
            if eqaoData
              for rank in ['rmG9Ranking','rmG6Ranking','rmG3Ranking']
                updateFields[rank] = eqaoData[rank] if eqaoData[rank]
          if updateFields.fraserId?.length > 0
            fraserData = await SchoolFraserCol.findOne {_id:updateFields.fraserId}
            if fraserData
              fraser = fraserData?.fraser[0]
              for rank in ['firank','fitotal','firate']
                updateFields[rank] = fraser[rank] if fraser[rank]
          await SchoolRmMergedCol.updateOne {_id:id}, {$set:updateFields}, {session}
          id = orig.sourceId
        
        await SchoolRmManualCol.updateOne {_id:id}, {$set:manaulUpdateFields}, {upsert:true, session}
        await session.commitTransaction()
      catch err
        await session.abortTransaction()
        throw err
      finally
        await session.endSession()

  ###*
  # 根据索引删除公立学校边界信息
  # @param {Object} params - 删除参数
  # @param {String} params.id - 学校ID
  # @param {Number} params.bnsIndex - 边界索引
  ###
  @removePublicSchoolBnsByIndex:({id,bnsIndex})->
    q = buildQueryById id
    ts = new Date()
    unset = {}
    set = {}
    set.mt = ts

    {transactionOptions,session} = GETTRANSACTION 'vow'
    try
      session.startTransaction(transactionOptions)
      # 在事务中查询原始数据
      orig = await SchoolRmManualCol.findOne(q)
      
      if orig?.bns?[parseInt(bnsIndex)]
        unset["bns.#{bnsIndex}"] = 1
      else
        orig = await SchoolRmMergedCol.findOne(q)
        if not orig
          orig = await SchoolFindschoolCol.findOne(q)
        if orig?.bns?[parseInt(bnsIndex)]
          delete orig.bns[parseInt(bnsIndex)]
          set.bns = orig.bns
          
      unless orig?
        throw new Error "Not exists #{id}"
      ids = [orig._id]
      if orig.sourceId
        ids.push orig.sourceId
      # 在事务中执行更新操作
      await SchoolRmManualCol.updateOne(
        {_id:{$in:ids}}, 
        {$unset: unset, $set: set, $setOnInsert: {ts:ts}}, 
        {upsert:true, session}
      )
      
      await SchoolRmManualCol.updateOne(
        {_id:{$in:ids}}, 
        {$pull: {bns: null}}, 
        {session}
      )

      # 同步更新SchoolRmMergedCol数据
      await SchoolRmMergedCol.updateOne(
        {$or:[{_id:{$in:ids}},{sourceId:{$in:ids}}]},
        {$unset: unset, $set: set},
        {session}
      )

      await SchoolRmMergedCol.updateOne(
        {$or:[{_id:{$in:ids}},{sourceId:{$in:ids}}]},
        {$pull: {bns: null}},
        {session} 
      )

      await session.commitTransaction()
    catch err
      await session.abortTransaction()
      throw err
    finally
      await session.endSession()

  ###*
  # 保存公立学校边界信息
  # @param {Object} params - 保存参数
  # @param {String} params.id - 学校ID
  # @param {String} params.bnid - 边界ID
  # @param {Number} params.bnsIndex - 边界索引
  # @param {String} [params.Comments] - 备注
  # @param {Boolean} [params.IsActive=true] - 是否激活
  # @param {Object} params.bn - 边界信息
  # @param {Number} [params.ef=0] - Extended French
  # @param {Number} [params.ele=0] - Elementary
  # @param {Number} [params.mid=0] - Middle
  # @param {Number} [params.hgh=0] - High
  # @param {Number} [params.eng=0] - English
  # @param {Number} [params.exclude=0] - 是否排除
  # @param {Number} [params.fi=0] - French Immersion
  # @param {String} [params.gf] - Grade From
  # @param {String} [params.gt] - Grade To
  # @param {Array} [params.loc] - 位置
  # @param {Array} [params.ne] - 东北角坐标
  # @param {Array} [params.sw] - 西南角坐标
  # @param {Number} [params.ap=0] - AP课程
  # @param {Number} [params.ib=0] - IB课程
  # @param {Number} [params.gif=0] - 资优课程
  # @param {Number} [params.sport=0] - 体育课程
  # @param {Number} [params.art=0] - 艺术课程
  ###
  @savePublicSchoolBns:({
    id, #required
    bnid, #required
    bnsIndex, #required
    Comments,
    IsActive=true,
    bn, #required
    ef=0,
    ele=0,
    mid=0,
    hgh=0,
    eng=0,
    exclude=0,
    fi=0,
    gf,
    gt,
    loc,
    ne,
    sw,
    ap=0,
    ib=0,
    gif=0,
    sport=0,
    art=0,
    coll
    })->
      if not id
        throw new Error('id required')
      bnid = bnid or helpers.number2chars2(Date.now())
      obj={}
      updateBns = {}
      params = {
        bnid,
        Comments,
        bn,
        ef,
        ele,
        mid,
        hgh,
        eng,
        exclude,
        fi,
        gf,
        gt,
        ne,
        sw,
        ap,
        ib,
        gif,
        sport,
        art
      }
      keys = Object.keys(params)
      for fld in keys
        if params[fld]?
          updateBns[fld] = params[fld]
      for fld in ['eng','fi','ef','ele','mid','hgh','ap','ib','sport','art','gif']
        obj[fld] = 1 if params[fld] is 1
      if loc
        obj.loc = loc
      if IsActive in [true,1]
        obj.IsActive = 1
      obj.mt = new Date()
      
      {transactionOptions, session} = GETTRANSACTION 'vow'
      try
        session.startTransaction(transactionOptions)

        orig = await School.getPublicSchoolByIdAndColl(id,coll)

        # 检查是否至少有一个表中存在该学校数据
        unless orig
          throw new Error "Not exists #{id}"

        orig.bns ?= []
        # 处理边界数据
        if oldBn = orig.bns[parseInt(bnsIndex)]
          newBn = updateBns
          for k,v of oldBn #copy fileds in old but not in new to new
            if not newBn[k]?
              newBn[k] = v
              debug.debug "Keep #{k}=>#{v}"
        else
          debug.debug "No boundary index:#{bnsIndex} for #{id}"
          debug.debug orig
          
        orig.bns[parseInt(bnsIndex)] = updateBns
        obj.bns = orig.bns
        obj.bnds = boundaryHelper.getJsonFromBns(orig.bns)
        id = orig._id
        if checkIdFormat(id)
          # 在事务中更新数据
          await SchoolRmMergedCol.updateOne {_id:id}, {$set:obj}, {upsert:true, session}
          id = orig.sourceId
        # 修改findschool表中的数据的时候，保存到manual的数据中没有nm,city,prov字段，导致搜索不出来对应学校
        for f in ['nm','city','prov','IsActive']
          obj[f] = orig[f]
        await SchoolRmManualCol.updateOne {_id:id}, {$set:obj}, {upsert:true, session}
        await session.commitTransaction()
      catch error_Schools_update
        await session.abortTransaction()
        debug.error error_Schools_update
        try
          await SchoolError.updateOne {_id:orig._id},{$set:obj},{upsert:true}
        catch error_SchoolError_update
          debug.error error_SchoolError_update
        throw error_Schools_update
      finally
        await session.endSession()

  ###*
  # 获取私立学校列表
  # @param {Object} params - 查询参数
  # @param {String} [params.locale] - 语言
  # @param {String} [params.nm] - 学校名称
  # @param {String} [params.city] - 城市
  # @param {Array} [params.filter] - 过滤条件
  # @param {Array} [params.bbox] - 地图范围
  # @param {Number} [params.page] - 页码
  # @param {Boolean} [params.needExtendFields] - 是否需要扩展字段
  # @param {String} [params.prov] - 省份
  # @param {Object} [params.sort] - 排序
  # @param {Number} [params.limit=30] - 每页数量
  # @param {Boolean} [params.showOnMap=false] - 是否显示在地图上
  # @param {Array} [params.fields] - 指定返回字段
  # @return {Array} 私立学校列表
  ###
  @getPrivateSchools:({
    locale,#lang
    nm,
    city,
    filter, # [{'mid':1,'ib':1},{'ele':1,'ib':1}]，[$and:[{$or}]]
    bbox, #地图范围 bbox:[swLng,swLat,neLng,neLat]
    page, #optinal，current page
    needExtendFields,#optional,需要所有字段
    prov,
    sort,
    limit=30,
    showOnMap=false,
    fields=null #optional, 指定返回的字段, ['_id', 'nm', 'addr']
    })->
      # 构建基础查询条件
      query = Object.assign(
        buildQuery({bbox,prov,city,filter,nm}),
        {hide:{$nin:[true,1]},IsActive:1}
      )

      # 构建aggregate管道
      pipeline = [
        # 基础匹配条件
        { $match: query }
      ]

      # 添加lookup关联
      pipeline.push({
        $lookup: {
          from: "sch_rm_rank"
          localField: "_id"
          foreignField: "_id"
          as: "rankData"
        }
      })

      # 添加字段投影
      pipeline.push({
        $project: {
          ...PRIVATE_SCHOOL_FIELDS_BASIC,
          ivyRank:{ $arrayElemAt: ['$rankData.ivyRank', 0] },
          hasAdj: {
            $or: [
              { $ne: [{ $size: '$rankData.adjSummary' }, 0] },
              { $ne: [{ $size: '$rankData.adjDetail' }, 0] }
            ]
          }
        }
      })

      # 添加排序
      sortStage = switch sort?.tp
        when 'nm' then { nm: 1 }
        when 'ivy' then { rmRanking: -1, weight: -1 }
        else { weight: -1 }
      pipeline.push({ $sort: sortStage })

      # 添加分页
      if page
        pipeline.push({ $skip: page * limit })
      pipeline.push({ $limit: limit + 1 })

      # 执行查询
      list = await PrivateSchools.aggregate(pipeline)
      list ?= []
      for s in list
        s.private = true
        #代码在native，没loc给[]
        s.loc ?= []
        if s.lat and s.lng
          s.loc = [s.lat,s.lng]
        translateSch s,locale
        addTags s,locale
        formatRmRankKeyFacts s, 'private'
        addKeyFacts(s, i18n.getFun(locale))
        s.canExchange = (s.rmRankKeyFacts && (Object.keys(s.rmRankKeyFacts).length > 0)) or s.hasAdj
        if showOnMap
          s.fnm = s.nm
          s.nm = s.fnm.replace(/\s(Public|School|Elementory|Middle|Secondary|Catholic)/gi, '')

      if sort?.tp is 'ivy'
        list.sort((a,b) -> 
          # 如果两者都有值,按数值大小排序
          if a.rmRanking? and b.rmRanking?
            return a.rmRanking - b.rmRanking
          # 如果只有一个有值,有值的排在前面
          if a.rmRanking? then return -1
          if b.rmRanking? then return 1
          # 都没有值则保持原顺序
          return 0
        )
      return list

  ###*
  # 获取带计数的私立学校列表
  # @param {Object} params - 查询参数
  # @param {String} [params.locale] - 语言
  # @param {String} [params.nm] - 学校名称
  # @param {String} [params.city] - 城市
  # @param {Array} [params.filter] - 过滤条件
  # @param {Array} [params.bbox] - 地图范围
  # @param {Number} [params.page] - 页码
  # @param {Boolean} [params.needExtendFields] - 是否需要扩展字段
  # @param {String} [params.prov] - 省份
  # @param {Object} [params.sort] - 排序
  # @param {Number} [params.limit=30] - 每页数量
  # @param {Boolean} [params.showOnMap=false] - 是否显示在地图上
  # @param {Array} [params.fields] - 指定返回字段
  # @return {Object} 包含私立学校列表和总数的对象 {schList: Array, cnt: Number}
  ###
  @getPrivateSchoolsWithCnt = ({
    locale,
    nm,
    city,
    filter,
    bbox,
    page,
    needExtendFields,
    prov,
    sort,
    limit=30,
    showOnMap=false,
    fields=null,
    isOnlyCnt=false
    })->
      unless isOnlyCnt
        list = await @getPrivateSchools({
          locale,
          nm,
          city,
          filter,
          bbox,
          page,
          needExtendFields,
          prov,
          sort,
          limit,
          showOnMap,
          fields
        })
      query = buildQuery({bbox,prov,city,filter,nm})
      query = Object.assign query, {hide:{$nin:[true,1]},IsActive:1}
      cnt = await PrivateSchools.countDocuments query
      return {schList: list or [], cnt}

  ###*
  # 增加私立学校访问量
  # @param {String|Array} ids - 学校ID或ID数组
  # @return {Object} 更新结果
  ###
  @increasePrivateSchoolVc:(ids)->
    unless Array.isArray(ids)
      ids = [ids]
    ret = await PrivateSchools.updateMany {_id: {$in:ids}},{$inc:{vc:1,vcd:1}}
    return ret

  ###*
  # 增加公立学校统计数据
  # @param {Object} params - 参数对象
  # @param {String|Array} params.ids - 学校ID或ID数组
  # @param {Boolean} params.isView - 是否为查看操作,否则为分享操作
  # @param {Boolean} params.pre - 是否为预分享(点击分享但未实际分享)
  # @param {Object} params.user - 用户对象
  # @param {String} params.suffix - 查看位置后缀(wb/yx/dz/app)
  # @param {String} params.devType - 设备类型(app/web)
  # @return {Object} 更新结果
  ###
  @incPublicSchoolStats:({
    ids,
    isView, # view flag, else is for share
    pre,    # flag for pre-share, share clicked but not shard
    user,   # user obj
    suffix,  # suffix indicated view location, wb/yx/dz/app
    devType # app or web
  })->
    getPrefix = ()->
      if isView
        return 'vc'
      if pre then 'shrp' else 'shr'
    getIncObj = (prefix)->
      inc = {}
      inc[prefix] = 1
      inc[prefix+'d'] = 1
      inc
    addYearMonthStat = (inc)->
      now = new Date()
      year = now.getFullYear()
      month = now.getMonth()+1
      inc["#{year}vc"] = 1
      inc["#{year}#{month}vc"] = 1
    opt = arguments[0]
    # console.log '++++++++',ids,opt
    unless ids
      return null
    unless Array.isArray(ids)
      ids = [ids]
    if ('string' is typeof ids) #and /,/.test ids[0]
      ids = ids.split(',')
    unless (Array.isArray(ids) and ids.length)
      return null
    prefix = getPrefix()
    inc = getIncObj(prefix)
    # devType区分app/web
    if devType is 'app'
      prefix += 'app'
    if suffix
      inc[prefix+suffix] = 1
    role = 'c'
    if user and libUser.isRealtor(user)
      role = 'r'
    inc[prefix+role] = 1
    addYearMonthStat(inc)
    _ids = [] #
    for id in ids
      if /\d+|\w+/.test(id)
        _ids.push id
        # NOTE: _id inconsistent, some are string, some are number
        if parseInt id
          _ids.push parseInt id
    q = {_id:{$in:_ids}}
    debug.debug '_ids: ',_ids
    # if not (rm_ids.length or rmids.length or _ids.length)
    if _ids.length
      ret = await SchoolStatCol.updateMany q, {$inc:inc}, {upsert:false}
    return ret

  ###*
  # 根据ID数组获取私立学校信息
  # @param {Object} params - 参数对象
  # @param {Array} params.ids - 学校ID数组
  # @param {String} [params.locale='en'] - 语言环境
  # @return {Array} 私立学校列表
  ###
  @getPrivateSchoolByIds:({ids,locale='en'})->
    if not Array.isArray(ids)
      throw new Error('array of id Required')
    schs = await PrivateSchools.findToArray {_id: {$in:ids},IsActive:1}, {projection:PRIVATE_SCHOOL_FIELDS_EXTEND}
    for sch in schs
      sch.private = true
      translateSch sch,locale
      addTags sch,locale
    return schs

  ###*
  # 根据ID获取私立学校详细信息
  # @param {Object} params - 参数对象
  # @param {String} params.id - 学校ID
  # @param {String} [params.locale='en'] - 语言环境
  # @param {Boolean} [params.isAdmin] - 是否为管理员
  # @param {Boolean} [params.canExchange] - 是否可以交换
  # @return {Object} 私立学校详细信息
  ###
  @getPrivateSchoolById:( {id, locale='en',isAdmin,canExchange})->
    unless id
      throw new Error('id Required')
    
    # 构建基础pipeline
    pipeline = [
      # 匹配条件
      { 
        $match: {
          _id: id,
          IsActive: 1
        }
      }
    ]

    # 关联 sch_rm_rank 表获取排名数据
    pipeline = pipeline.concat(buildSchRankLookupStages())

    # 添加projection
    projection = helpers.deepCopyObject PRIVATE_SCHOOL_FIELDS_EXTEND
    if isAdmin
      projection.vc = 1
      projection.vcd = 1
    pipeline.push({ $project: {
      ...projection,
      ivyRank: 1,
      adjSummary: 1,
      adjDetail: 1,
      top100: 1
    } })

    # 执行聚合查询
    schools = await PrivateSchools.aggregate(pipeline)
    sch = schools[0]

    return null unless sch

    sch.private = 1
    translateSch sch,locale
    addTags sch,locale
    if canExchange and (sch.ivyRank or sch.adjSummary)
      # formatChart sch
      formatRmRankScore sch
      if sch.top100
        sch.rankMap = {}
        formatIvyRank sch
      else
        formatRmRankPir sch
      formatAdjRank sch
    formatRmRankKeyFacts sch, 'private'
    addKeyFacts(sch, i18n.getFun(locale))
    return sch

  ###*
  # 获取大学列表
  # @param {Object} params - 参数对象
  # @param {String} [params.locale] - 语言环境
  # @param {String} [params.nm] - 学校名称
  # @param {String} [params.prov] - 省份
  # @param {String} [params.city] - 城市
  # @param {Array} [params.bbox] - 地图范围[swLng,swLat,neLng,neLat]
  # @param {Number} [params.page] - 页码
  # @param {Boolean} [params.needExtendFields] - 是否需要扩展字段
  # @param {Object} [params.sort] - 排序
  # @param {String} [params.tp] - 类型['university','college']
  # @param {Number} [params.limit=30] - 每页数量
  # @param {Boolean} [params.showOnMap] - 是否显示在地图上
  # @param {Array} [params.fields] - 指定返回字段
  # @return {Array} 大学列表
  ###
  @getUniversities:({
    locale,#lang
    nm,
    prov,
    city,
    bbox, #地图范围 bbox:[swLng,swLat,neLng,neLat]
    page, #optinal，current page
    needExtendFields,#optional,需要所有字段
    sort,
    tp,#['university','college']
    limit=30,
    showOnMap #optional, if has showCampus, merge all campus and return as list
    fields=null #optional, 指定返回的字段, ['_id', 'nm', 'addr']
    })->
      searchOpt = {fields:{},limit:limit+1}
      if page
        searchOpt.skip = page*limit
      # if sort?.tp is 'nm'
      searchOpt.sort = {weight:-1}
      # TODO: sort by score
      query = buildQuery({nm})
      query['campus.city'] = city if city
      query['campus.prov'] = cityHelper.getProvAbbrName prov if prov
      query.tp = tp if tp
      query.IsActive = {$ne:0}
      if bbox
        swLng = bbox[0]
        swLat = bbox[1]
        neLng = bbox[2]
        neLat = bbox[3]
        query = Object.assign query, {$and:[{'campus.lat':{$lt:neLat}},{'campus.lat':{$gt:swLat}},{'campus.lng':{$lt:neLng}},{'campus.lng':{$gt:swLng}}]}
      # debug.debug(JSON.stringify(query))
      # Restrict to provided 'fields'
      if fields?
        searchOpt.fields = fields
      list = await University.findToArray query,searchOpt
      #filter with bbox,只要含有bbox的。
      # list 是shool的，每个school还有多个校区
      # debug.debug list
      list ?= []
      for s in list
        addTags s,locale
        for campus in s.campus
          translateSch campus,locale
          if campus.nm and (s.nm isnt campus.nm)
            campus.nm = "#{s.nm} #{campus.nm}"
          else
            campus.nm = s.nm
          if campus.lat and campus.lng
            campus.loc = [campus.lat,campus.lng]
          if bbox
            if campus.lat < bbox[1] or campus.lat > bbox[3] or campus.lng < bbox[0] or campus.lng > bbox[2]
              campus.del = 1
        if s.campus
          s.campus =s.campus.filter (e) -> e.del isnt 1
      if showOnMap
        ret = []
        for s in list
          for campus,i in s.campus
            campus[tp] = true
            campus.tp = s.tp
            campus.tags = s.tags
            campus._id="#{s._id}##{i}"
            # NOTE: already translated?
            # translateSch campus,locale
            ret.push campus
        list = ret
      return list
  
  ###*
  # 获取带计数的大学列表
  # @param {Object} params - 参数对象
  # @param {String} [params.locale] - 语言环境
  # @param {String} [params.nm] - 学校名称
  # @param {String} [params.prov] - 省份
  # @param {String} [params.city] - 城市
  # @param {Array} [params.bbox] - 地图范围
  # @param {Number} [params.page] - 页码
  # @param {Boolean} [params.needExtendFields] - 是否需要扩展字段
  # @param {Object} [params.sort] - 排序
  # @param {String} [params.tp] - 类型
  # @param {Number} [params.limit=30] - 每页数量
  # @param {Boolean} [params.showOnMap] - 是否显示在地图上
  # @param {Array} [params.fields] - 指定返回字段
  # @return {Object} 包含大学列表和总数的对象 {schList: Array, cnt: Number}
  ###
  @getUniversitiesWithCnt = ({
    locale,
    nm,
    prov,
    city,
    bbox,
    page,
    needExtendFields,
    sort,
    tp,
    limit=30,
    showOnMap,
    fields,
    isOnlyCnt=false
    })->
      unless isOnlyCnt
        list = await @getUniversities({
          locale,
          nm,
          prov,
          city,
          bbox,
          page,
          needExtendFields,
          sort,
          tp,
          limit,
          showOnMap,
          fields
        })
      
      query = buildQuery({nm})
      query['campus.city'] = city if city
      query['campus.prov'] = cityHelper.getProvAbbrName prov if prov
      query.tp = tp if tp
      query.IsActive = {$ne:0}
      if bbox
        swLng = bbox[0]
        swLat = bbox[1]
        neLng = bbox[2]
        neLat = bbox[3]
        query = Object.assign query, {$and:[{'campus.lat':{$lt:neLat}},{'campus.lat':{$gt:swLat}},{'campus.lng':{$lt:neLng}},{'campus.lng':{$gt:swLng}}]}
    
      cnt = await University.countDocuments query
      return {schList:list or [], cnt}

  ###*
  # 根据ID获取大学详细信息
  # @param {Object} params - 参数对象
  # @param {String} params.id - 大学ID
  # @param {String} [params.locale='en'] - 语言环境
  # @return {Object} 大学详细信息
  ###
  @getUniversityById:({id,locale='en'})->
    if not id
      throw new Error('id required')
    sch = await University.findOne {_id:id}
    addTags sch,locale
    for campus,i in sch.campus
      translateSch campus,locale
    return sch

  ###*
   * 根据省份获取公立学校列表
   * @param {Object} params - 查询参数
   * @param {String} params.prov - 省份名称
   * @param {Object} [params.fields] - 指定返回的字段
   * @param {String} [params.coll] - 集合类型,manual表示使用手动维护的集合
   * @return {Promise<Array>} 返回学校列表
   ###
  @getPublicSchoolsByProv:({prov,fields,coll})->
    # 构建查询条件,转换省份名称为标准缩写
    query = {prov: cityHelper.getProvAbbrName(prov)}

    # 根据coll参数选择查询的集合
    searchColl = switch coll
      when 'manual' then SchoolRmManualCol 
      when 'findschool' then SchoolFindschoolCol
      else SchoolRmMergedCol

    # 执行查询并返回结果
    return await searchColl.findToArray query, {fields}

  ###*
  # 根据ID和集合类型查找公立学校，分别找出findschool,rm_manual,rm_merged表的数据，然后合并
  # @param {String} id - 学校ID
  # @param {String} coll - 集合类型,manual表示使用手动维护的集合
  # @return {Promise<Object>} 返回学校信息
  ###
  @getPublicSchoolByIdAndColl:(id,coll)->
    # 参数校验
    if not id
      throw new Error('id required')

    # 构建查询条件
    query = buildQueryById(id)
    if checkIdFormat id
      coll = 'merged'
    # 根据coll参数选择查询的集合
    searchColl = switch coll
      when 'manual' then SchoolRmManualCol 
      when 'findschool' then SchoolFindschoolCol
      else SchoolRmMergedCol
    pipeline = [# 匹配文档
      { $match: query }
    ]
    # merged表和其他表对应的字段不一致，需要分开处理
    if coll in ['manual','findschool']
      # 关联 sch_findschool 表
      pipeline.push {$lookup:{from: 'sch_findschool',localField: '_id',foreignField: '_id',as: 'findSchoolData'} }

      # 关联 sch_rm_manual 表  
      pipeline.push {$lookup:{from: 'sch_rm_manual',localField: '_id',foreignField: '_id',as: 'rmManualData'} }

      # 关联 sch_rm_merged 表
      pipeline.push {$lookup:{from: 'sch_rm_merged',localField: '_id',foreignField: 'sourceId',as: 'rmMergedData'} }
    else
      # 关联 sch_findschool 表
      pipeline.push {$lookup:{from: 'sch_findschool',localField: 'sourceId',foreignField: '_id',as: 'findSchoolData'} }

      # 关联 sch_rm_manual 表  
      pipeline.push {$lookup:{from: 'sch_rm_manual',localField: 'sourceId',foreignField: '_id',as: 'rmManualData'} }

      # 关联 sch_rm_merged 表
      pipeline.push {$lookup:{from: 'sch_rm_merged',localField: '_id',foreignField: '_id',as: 'rmMergedData'} }

    # 重构文档,按照优先级合并字段
    pipeline.push {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              { $arrayElemAt: ['$findSchoolData', 0] }, # findschool表数据优先
              { $arrayElemAt: ['$rmMergedData', 0] }, # merged表数据次之
              { $arrayElemAt: ['$rmManualData', 0] }, # manual表数据最后
              {_id:{ $arrayElemAt: ['$rmMergedData._id', 0] }}
            ]
          }
        }
      }

    # 执行聚合查询并返回结果
    school = await searchColl.aggregate(pipeline)
    return school[0]


MODEL 'School',School

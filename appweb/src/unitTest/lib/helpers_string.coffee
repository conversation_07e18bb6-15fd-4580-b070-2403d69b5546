should = require('should')
testHelpers = require('../00_common/helpers')
helpersStringLib = require('../../built/lib/helpers_string')

# ./mate4.sh compile
# ./test.sh -f lib/helpers_string.js

describe 'Helpers function tests',->
  before (done) ->
    @timeout(300000)
    done()

  describe 'isEmail',->
    tests=[
      {
        input:'farid142@hotmaiñ.com',
        output:false
      },
      {
        input:'<EMAIL>'
        output: false,
      },
      {
        input:'<EMAIL>'
        output: true,
      }
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'.<EMAIL>'
        output: false
      },
      {
        input:'<EMAIL>',
        output: false
      },
      {
        input:'<EMAIL>'
        output: false
      },
      {
        input:'username@sina'
        output: false
      }
      {
        input:'username@<EMAIL>',
        output: false
      },
      {
        input:'username@@yeah.com',
        output: false
      },
      {
        input: '<EMAIL>@',
        output: false
      },
      {
        input:'<EMAIL>.'
        output: false
      },
      {
        input:'<EMAIL>'
        output: false
      },
      {
        input:'username@.com'
        output: false
      },
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'7409489😄<EMAIL>'
        output: false
      },
      {
        input:'740948991@qq😄.com'
        output: false
      },
      {
        input:'<EMAIL>'
        output: false
      },
      {
        input:'username@.com'
        output: false
      },
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'7409489😄<EMAIL>'
        output: false
      },
      {
        input:'740948991@qq😄.com'
        output: false
      },
      {
        input:'<EMAIL>'
        output: false
      },
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'<EMAIL>'
        output: true
      }
    ]
    tests.forEach (test)->
      it "should conver #{test.input} to #{test.output}", (done)->
        output = helpersStringLib.isEmail test.input
        should.equal(output, test.output)
        done()

  describe 'int32ToBase62',->
    tests=[
      {
        input: 0,
        output: 'A',
        description: '零值应该返回 A'
      },
      {
        input: 1,
        output: 'B',
        description: '1 应该返回 B'
      },
      {
        input: 61,
        output: '9',
        description: '61 应该返回 9 (base62字符集的最后一个字符)'
      },
      {
        input: 62,
        output: 'BA',
        description: '62 应该返回 BA'
      },
      {
        input: 123,
        output: 'B9',
        description: '123 应该返回 B9'
      },
      {
        input: 3844,
        output: 'BAA',
        description: '3844 应该返回 BAA'
      },
      {
        input: 238327,
        output: '999',
        description: '238327 应该返回 999'
      },
      {
        input: 2147483647,
        output: 'CVUmlB',
        description: '32位有符号整数最大值 2147483647'
      },
      {
        input: -1,
        output: 'EqpPMD',
        description: '负数 -1 应该被转换为无符号整数 4294967295'
      },
      {
        input: -2147483648,
        output: 'CVUmlC',
        description: '32位有符号整数最小值 -2147483648'
      },
      {
        input: 4294967295,
        output: 'EqpPMD',
        description: '32位无符号整数最大值 4294967295'
      },
      {
        input: 'abc',
        output: 'abc',
        description: '无效输入字符串应该原样返回'
      },
      {
        input: null,
        output: null,
        description: '无效输入 null 应该原样返回'
      },
      {
        input: undefined,
        output: undefined,
        description: '无效输入 undefined 应该原样返回'
      },
      {
        input: 3.14,
        output: 3.14,
        description: '浮点数应该原样返回'
      }
    ]

    tests.forEach (test)->
      it "#{test.description}: #{test.input} -> #{test.output}", (done)->
        output = helpersStringLib.int32ToBase62 test.input
        should.equal(output, test.output)
        done()

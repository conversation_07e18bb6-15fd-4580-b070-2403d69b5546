should = require('should')
helpers = require('../00_common/helpers')
impFormat = require('../../built/libapp/impFormat')
METER_PER_FEET = 0.3048
debug = null

describe 'impFormat',->
  before (done) ->
    @timeout(30000)
    request = helpers.getServer()
    debug = helpers.DEBUG()
    done()
    return

  describe 'formatBasement',->
    tests = [
      {
        input: {
          'bsmt' : 'Partially finished',
        },
        output:[
          'Prt', 'Fin'
        ]
      },
      {
        input: {
          'bsmt' : ['Partially finished','Crawl space', '(Partially finished)']
        },
        output:[
          'Prt', 'Fin', 'Crw'
        ]
      },
      
      {
        input: {
          'bsmt' : 'Unknown (Partially finished),Partially finished',
        },
        output:[
          'NAN', 'Prt', 'Fin'
        ]
      },
      {
        input: {
          'bsmt' : 'Unfinished,Full (Unfinished)',
        },
        output:[
          'unFin','Full'
        ]
      },
      {
        input:{
          'bsmt':'Full Sep Ent',
        },
        output:[
          'Full','Sep'
        ]
      },
      # Test new regex patterns
      {
        input:{
          'bsmt':'Walk out',
        },
        output:[
          'W/O'
        ]
      },
      {
        input:{
          'bsmt':'Walk-Out',
        },
        output:[
          'W/O'
        ]
      },
      {
        input:{
          'bsmt':'Walk Up',
        },
        output:[
          'W/U'
        ]
      },
      {
        input:{
          'bsmt':'Walk-Up',
        },
        output:[
          'W/U'
        ]
      },
      {
        input:{
          'bsmt':'No',
        },
        output:[
          'NON'
        ]
      },
      {
        input:{
          'bsmt':'N/A',
        },
        output:[
          'NON'
        ]
      },
      {
        input:{
          'bsmt':'Not Applicable',
        },
        output:[
          'NAN'
        ]
      },
      {
        input:{
          'bsmt':'Cellar',
        },
        output:[
          'Slab'
        ]
      },
      {
        input:{
          'bsmt':'Dugout',
        },
        output:[
          'Slab'
        ]
      },
      {
        input:{
          'bsmt':'Remodeled Basement',
        },
        output:[
          'Fin'
        ]
      },
      {
        input:{
          'bsmt':'Exterior Entry',
        },
        output:[
          'Sep'
        ]
      },
      {
        input:{
          'bsmt':'Apartment,Finished,Full',
        },
        output:[
          'Apt', 'Fin', 'Full'
        ]
      },
      {
        input:{
          'bsmt':['Apartment','Finished'],
        },
        output:[
          'Apt','Fin',
        ]
      },
      {
        input:{
          'bsmt':'None',
        },
        output:[
          'NON'
        ]
      },
      {
        input:{
          'bsmt':['Full','Unfinished'],
        },
        output:[
          'Full','unFin'
        ]
      }
    ]
    tests.forEach (test)->
      it "should format: #{test.input.bsmt} to #{test.output}", (done)->
        ret = impFormat.formatBasement test.input
        debug.debug 'test.output',test.output
        debug.debug 'ret',ret
        should.deepEqual ret, test.output
        done()

  describe 'formatProp',->
    describe 'setOrigId',->
      it 'should set origId when not exists', (done)->
        prop = {_id: 'TESTA'}
        ret = impFormat.formatProp prop, {}
        should.deepEqual ret.origId, ['TESTA']
        done()

      it 'should append _id to existing origId array', (done)->
        prop = {_id: 'TESTA', origId: ['TESTB']}
        ret = impFormat.formatProp prop, {}
        should.deepEqual ret.origId, ['TESTB', 'TESTA']
        done()

    describe 'formatAddress',->
      it 'should format address and preserve original values', (done)->
        prop = {
          addr: '123 Main Street',
          unt: '0',
          city: 'Toronto',
          prov: 'ON',
          cmty: 'Downtown',
          st: 'Main',
          st_num: '123'
        }
        metaInfo = {}
        ret = impFormat.formatProp prop, metaInfo
        should.not.exist ret.unt
        should.equal ret.addr, '123 Main St'
        should.equal ret.faddr, '123 Main St, Toronto, ON, CA'
        should.exist ret.origAddr
        should.exist metaInfo.addr
        done()

    describe 'formatSqft',->
      it 'should handle numeric sqft for residential property', (done)->
        prop = {
          sqft: 2000,
          ptype: 'r'
        }
        ret = impFormat.formatProp prop, {}
        should.equal ret.sqft1, 2000
        should.equal ret.sqft2, 2000
        done()

      it 'should handle numeric sqft for non-residential property', (done)->
        prop = {
          sqft: 50000,
          ptype: 'b'
        }
        ret = impFormat.formatProp prop, {}
        should.equal ret.sqft1, 50000
        should.equal ret.sqft2, 50000
        done()

      it 'should handle invalid sqft range for residential property', (done)->
        prop = {
          sqft: 50,
          ptype: 'r'
        }
        ret = impFormat.formatProp prop, {}
        should.not.exist ret.sqft
        should.not.exist ret.sqft1
        should.not.exist ret.sqft2
        done()

      it 'should handle string sqft with range', (done)->
        prop = {
          sqft: '1000-2000',
          ptype: 'r'
        }
        ret = impFormat.formatProp prop, {}
        should.equal ret.sqft1, 1000
        should.equal ret.sqft2, 2000
        done()

      it 'should handle string sqft with invalid format', (done)->
        prop = {
          sqft: 'invalid',
          ptype: 'r'
        }
        ret = impFormat.formatProp prop, {}
        should.not.exist ret.sqft1
        should.not.exist ret.sqft2
        should.not.exist ret.sqft
        done()

      it 'should handle sqft2 less than 10', (done)->
        prop = {
          sqft: '5-8',
          ptype: 'r'
        }
        ret = impFormat.formatProp prop, {}
        should.not.exist ret.sqft
        should.not.exist ret.sqft1
        should.not.exist ret.sqft2
        done()

    describe 'formatTrbtp',->
      it 'should convert trbtp to lowercase', (done)->
        prop = {
          trbtp: ['FREEHOLD', 'CONDO']
        }
        ret = impFormat.formatProp prop, {}
        should.deepEqual ret.trbtp, ['freehold', 'condo']
        done()

    describe 'checkPropPrice',->
      it 'should handle sale property with valid price', (done)->
        prop = {
          ptype: 'r',
          lp: 500000,
          saletp: ['Sale']
        }
        metaInfo = {}
        ret = impFormat.formatProp prop, metaInfo
        should.deepEqual ret.saletp, ['Sale']
        should.not.exist metaInfo.lpOutOfRange
        done()

      it 'should handle lease property with valid price', (done)->
        prop = {
          ptype: 'r',
          lpr: 2000,
          saletp: ['Lease']
        }
        metaInfo = {}
        ret = impFormat.formatProp prop, metaInfo
        should.deepEqual ret.saletp, ['Lease']
        should.not.exist metaInfo.lprOutOfRange
        done()

      it 'should handle sale property with lp equal to lpr', (done)->
        prop = {
          ptype: 'r',
          lp: 500000,
          lpr: 500000,
          saletp: ['Sale']
        }
        metaInfo = {}
        ret = impFormat.formatProp prop, metaInfo
        should.deepEqual ret.saletp, ['Sale']
        should.not.exist ret.lpr
        done()

      it 'should handle lease property with lp equal to lpr', (done)->
        prop = {
          ptype: 'r',
          lp: 2000,
          lpr: 2000,
          saletp: ['Lease']
        }
        metaInfo = {}
        ret = impFormat.formatProp prop, metaInfo
        should.deepEqual ret.saletp, ['Lease']
        should.not.exist ret.lp
        done()

      it 'should handle property with lp and lpr not equal', (done)->
        prop = {
          ptype: 'r',
          lp: 500000,
          lpr: 2000,
          saletp: ['Sale']
        }
        metaInfo = {}
        ret = impFormat.formatProp prop, metaInfo
        should.exist metaInfo.lpNotEqualLpr
        done()

      it 'should handle property with lp only', (done)->
        prop = {
          ptype: 'r',
          lp: 500000,
          saletp: ['Sale']
        }
        metaInfo = {}
        ret = impFormat.formatProp prop, metaInfo
        should.deepEqual ret.saletp, ['Sale']
        done()

      it 'should handle property with lpr only', (done)->
        prop = {
          ptype: 'r',
          lpr: 2000,
          saletp: ['Lease']
        }
        metaInfo = {}
        ret = impFormat.formatProp prop, metaInfo
        should.deepEqual ret.saletp, ['Lease']
        done()

      it 'should handle property with no lp and lpr', (done)->
        prop = {
          ptype: 'r',
          saletp: ['Sale']
        }
        metaInfo = {}
        ret = impFormat.formatProp prop, metaInfo
        should.exist metaInfo.noLpAndLpr
        done()

      it 'should handle non-residential property', (done)->
        prop = {
          ptype: 'b',
          lp: 500000,
          lpr: 2000
        }
        metaInfo = {}
        ret = impFormat.formatProp prop, metaInfo
        should.equal ret.lp, 500000
        should.equal ret.lpr, 2000
        done()

    describe 'setImportDate',->
      it 'should set onD from ld field', (done)->
        prop = {ld: 20230101}
        metaInfo = {}
        ret = impFormat.setImportDate prop, {}, metaInfo
        should.equal ret.onD, 20230101
        done()

      it 'should set onD from ListingContractDate field', (done)->
        prop = {ListingContractDate: 20230101}
        metaInfo = {}
        ret = impFormat.setImportDate prop, {}, metaInfo
        should.equal ret.onD, 20230101
        done()

      it 'should set onD from input_date field', (done)->
        prop = {input_date: 20230101}
        metaInfo = {}
        ret = impFormat.setImportDate prop, {}, metaInfo
        should.equal ret.onD, 20230101
        done()

      it 'should set onD from lstd field', (done)->
        prop = {lstd: 20230101}
        metaInfo = {}
        ret = impFormat.setImportDate prop, {}, metaInfo
        should.equal ret.onD, 20230101
        done()

      it 'should set onD from picts field', (done)->
        prop = {picts: 20230101}
        metaInfo = {}
        ret = impFormat.setImportDate prop, {}, metaInfo
        should.equal ret.onD, 20230101
        done()

      it 'should set onD from lup field', (done)->
        prop = {lup: 20230101}
        metaInfo = {}
        ret = impFormat.setImportDate prop, {}, metaInfo
        should.equal ret.onD, 20230101
        done()

      it 'should set onD from lud field', (done)->
        prop = {lud: 20230101}
        metaInfo = {}
        ret = impFormat.setImportDate prop, {}, metaInfo
        should.equal ret.onD, 20230101
        done()

      it 'should set onD from ts field', (done)->
        prop = {ts: 20230101}
        metaInfo = {}
        ret = impFormat.setImportDate prop, {}, metaInfo
        should.equal ret.onD, 20230101
        done()

      it 'should set onD from timestamp_sql field', (done)->
        prop = {timestamp_sql: 20230101}
        metaInfo = {}
        ret = impFormat.setImportDate prop, {}, metaInfo
        should.equal ret.onD, 20230101
        done()

      it 'should set sldd for sold properties', (done)->
        prop = {
          lst: 'Sld',
          SoldDate: 20230101
        }
        metaInfo = {}
        ret = impFormat.setImportDate prop, {}, metaInfo
        should.equal ret.sldd, 20230101
        done()

      it 'should set sldd from multiple date fields', (done)->
        prop = {
          lst: 'Sld',
          solddate: 20230107,
          Cd: 20230108,
          cd: 20230100,
          sldd: 20230110,
          unavail_dt: 20230115,
          lud: 20230106,
          sremD: 20230107,
          cmpD: 20230108
        }
        metaInfo = {}
        ret = impFormat.setImportDate prop, {}, metaInfo
        should.equal ret.sldd, 20230107 # Should use the first available date
        done()

      it 'should set offD for unavailable properties', (done)->
        prop = {
          status: 'U',
          sldd: 20230101
        }
        metaInfo = {}
        ret = impFormat.setImportDate prop, {}, metaInfo
        should.equal ret.offD, 20230101
        done()

      it 'should set offD from multiple date fields', (done)->
        prop = {
          status: 'U',
          unavail_dt: 20230101,
          dt_ter: 20230102,
          exp: 20230103,
          lud: 20230104,
          lup: 20221205,
          sremD: 20230106,
          cmpD: 20230107
        }
        metaInfo = {}
        ret = impFormat.setImportDate prop, {}, metaInfo
        should.equal ret.offD, 20230101 # Should use the first available date
        done()

      it 'should adjust future dates to today', (done)->
        today = new Date()
        todayDateNum = parseInt(today.getFullYear().toString() + \
          (today.getMonth() + 1).toString().padStart(2, '0') + \
          today.getDate().toString().padStart(2, '0'))
        
        prop = {
          onD: 20990101,
          sldd: 20990101,
          offD: 20990101
        }
        metaInfo = {}
        ret = impFormat.setImportDate prop, {}, metaInfo
        should.equal ret.onD, todayDateNum
        should.equal ret.sldd, todayDateNum
        should.equal ret.offD, todayDateNum
        should.exist metaInfo.onD
        should.exist metaInfo.sldd
        should.exist metaInfo.offD
        done()

      it 'should adjust dates before onD to today', (done)->
        today = new Date()
        todayDateNum = parseInt(today.getFullYear().toString() + \
          (today.getMonth() + 1).toString().padStart(2, '0') + \
          today.getDate().toString().padStart(2, '0'))
        
        prop = {
          onD: 20230101,
          sldd: 20220101,
          offD: 20220101
        }
        metaInfo = {}
        ret = impFormat.setImportDate prop, {}, metaInfo
        should.equal ret.sldd, todayDateNum
        should.equal ret.offD, todayDateNum
        should.exist metaInfo.sldd
        should.exist metaInfo.offD
        done()

      it 'should preserve original sldd when changed', (done)->
        prop = {
          sldd: 20230101,
          SoldDate: 20230102,
          lst: 'Sld'
        }
        metaInfo = {}
        ret = impFormat.setImportDate prop, {}, metaInfo
        should.equal ret.origSldd, 20230101
        should.equal ret.sldd, 20230102
        should.exist metaInfo.sldd
        done()

      it 'should handle offD before 1970-01-01', (done)->
        prop = {
          status: 'U',
          offD: 19690101,
          unavail_dt: 19690101
        }
        metaInfo = {}
        ret = impFormat.setImportDate prop, {}, metaInfo
        should.equal ret.offD, 19700101
        done()

    describe 'checkDom',->
      it 'should delete dom for active properties', (done)->
        prop = {
          status: 'A',
          dom: 30
        }
        ret = impFormat.formatProp prop, {}
        should.not.exist ret.dom
        done()

    describe 'setSstpAndSptp',->
      it 'should set sstp for sale properties', (done)->
        prop = {
          saletp: ['Sale']
        }
        ret = impFormat.formatProp prop, {}
        should.equal ret.sstp, 'S'
        done()

      it 'should set sstp for lease properties', (done)->
        prop = {
          saletp: ['Lease']
        }
        ret = impFormat.formatProp prop, {}
        should.equal ret.sstp, 'L'
        done()

    describe 'formatNumberFields',->
      it 'should format numeric fields', (done)->
        prop = {
          tax: '1000.50',
          taxyr: '2023',
          lvl: '2'
        }
        ret = impFormat.formatProp prop, {}
        should.equal ret.tax, 1000.50
        should.equal ret.taxyr, 2023
        should.equal ret.lvl, 2
        done()

    describe 'deleteRootEmptyFields',->
      it 'should remove empty fields', (done)->
        prop = {
          emptyField: '',
          nullField: null,
          validField: 'value'
        }
        ret = impFormat.formatProp prop, {}
        should.not.exist ret.emptyField
        should.not.exist ret.nullField
        should.exist ret.validField
        done()

    describe 'copyGrForApartment',->
      it 'should copy park_spcs to gr for apartments', (done)->
        prop = {
          ptype2: ['Apartment'],
          park_spcs: 2
        }
        ret = impFormat.formatProp prop, {}
        should.equal ret.gr, 2
        done()

    describe 'formatMfee',->
      it 'should convert semi-annually fee to monthly', (done)->
        record = {
          AssociationFee: 1200,
          AssociationFeeFrequency: 'Semi-Annually'
        }
        impFormat.formatMfee record
        should.equal record.mfee, 200
        should.equal record.mfeeunt, 'Monthly'
        done()

      it 'should convert quarterly fee to monthly', (done)->
        record = {
          AssociationFee: 900,
          AssociationFeeFrequency: 'Quarterly'
        }
        impFormat.formatMfee record
        should.equal record.mfee, 300
        should.equal record.mfeeunt, 'Monthly'
        done()

      it 'should convert bi-monthly fee to monthly', (done)->
        record = {
          AssociationFee: 1000,
          AssociationFeeFrequency: 'Bi-Monthly'
        }
        impFormat.formatMfee record
        should.equal record.mfee, 500
        should.equal record.mfeeunt, 'Monthly'
        done()

      it 'should convert weekly fee to monthly', (done)->
        record = {
          AssociationFee: 100,
          AssociationFeeFrequency: 'Weekly'
        }
        impFormat.formatMfee record
        should.equal record.mfee, 428.57
        should.equal record.mfeeunt, 'Monthly'
        done()

      it 'should convert bi-weekly fee to monthly', (done)->
        record = {
          AssociationFee: 200,
          AssociationFeeFrequency: 'Bi-Weekly'
        }
        impFormat.formatMfee record
        should.equal record.mfee, 428.57
        should.equal record.mfeeunt, 'Monthly'
        done()

      it 'should convert daily fee to monthly', (done)->
        record = {
          AssociationFee: 10,
          AssociationFeeFrequency: 'Daily'
        }
        impFormat.formatMfee record
        should.equal record.mfee, 300
        should.equal record.mfeeunt, 'Monthly'
        done()

      it 'should handle one-time fee', (done)->
        record = {
          AssociationFee: 1000,
          AssociationFeeFrequency: 'One Time'
        }
        impFormat.formatMfee record
        should.equal record.mfee, 1000
        should.equal record.mfeeunt, 'One Time'
        done()

      it 'should handle other frequency', (done)->
        record = {
          AssociationFee: 1000,
          AssociationFeeFrequency: 'Other'
        }
        impFormat.formatMfee record
        should.equal record.mfee, 1000
        should.equal record.mfeeunt, 'Other'
        done()

      it 'should handle missing frequency', (done)->
        record = {
          AssociationFee: 1000
        }
        impFormat.formatMfee record
        should.equal record.mfee, 1000
        should.not.exist record.mfeeunt
        done()
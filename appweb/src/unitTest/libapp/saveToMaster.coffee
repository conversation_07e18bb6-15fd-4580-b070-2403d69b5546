should = require('should')
helpers = require('../00_common/helpers')
MSG_STRINGS = helpers.MSG_STRINGS
saveToMaster = null #require('../../built/libapp/saveToMaster')
# ./unitTest/test.sh -f libapp/saveToMaster.js
helpersObj = require '../../built/lib/helpers_object'
{inputToDateNum} = require '../../built/lib/helpers_date'
libProperties = require '../../built/libapp/properties'
helpersFunction = require '../../built/lib/helpers_function'
{getBoundaryTag} = require '../../built/libapp/propertyTagHelper'
getBoundaryTagAsync = null
addLogs = libProperties.addLogs
gProperties = null
gGeoCache = null
GeoCoder = null
gTrebRecords = null
gDDFRecords = null
gPropertiesImportLog = null
gTransitCities = null
gTransitStop = null
gSchool = null
gBoundary = null
gCensus2016 = null
gCensus2016ReversKeys = null
TRB******** = {
  _id: 'TRB********',
  ac: 'Central Air',
  addr: '1 King St W',
  age: '11-15',
  age1: 11,
  age2: 15,
  amen: [
    'Concierge',
    'Guest Suites',
    'Gym',
    'Visitor Parking'
  ],
  apt_num: '516',
  area_code: '01',
  avgpicsz: 176778.4827586207,
  bdrms: 0,
  blcny: 'None',
  bltYr1: 2007,
  bltYr2: 2011,
  br_plus: 1,
  bsmt: [
    'NON'
  ],
  bsmt1_out: 'None',
  bthrms: 1,
  bths: [
    {
      t: 1,
      l: 'Flat',
      p: 4
    }
  ],
  cac_inc: 'Y',
  cases: [
    'n'
  ],
  city: 'Toronto',
  city_d: 'Toronto C01',
  citycd: '01.C01',
  cmty: 'Bay Street Corridor',
  cmtycd: '01.C01.0900',
  cnty: 'CA',
  comel_inc: 'Y',
  comm: '2.5% + Hst',
  cond_txinc: 'N',
  condo_corp: 'TSCC',
  constr: [
    'Concrete'
  ],
  corp_num: 1703,
  crsst: 'Yonge/King',
  cur: 'CND',
  daddr: 'Y',
  den_fr: 'N',
  ens_lndry: 'N',
  exp: 20220430,
  faddr: '1 King St W,Toronto, ON M5H1A2, CA',
  fce: 'S',
  feat: [
    'Hospital',
    'Marina',
    'Park',
    'Public Transit'
  ],
  fpl: 'N',
  fuel: 'Gas',
  gatp: 'None',
  geoq: 120,
  gr: 0,
  heat: 'Forced Air',
  heat_inc: 'Y',
  his: [
    {
      s: 'New',
      lp: 453000,
      ts: new Date('2022-01-26T17:08:18.176Z'),
      d: 20220126
    },
    {
      s: 'Chg',
      ts: new Date('2022-05-01T04:05:28.000Z'),
      o: 'A',
      n: 'U'
    },
    {
      s: 'chM',
      ts: new Date('2022-05-01T04:05:28.000Z'),
      d: 20220501
    },
    {
      s: 'addPic',
      ts: new Date('2022-05-01T04:05:28.000Z'),
      d: 20220501
    }
  ],
  hydro_inc: 'Y',
  id: 'TRB********',
  idx_dt: '2015-04-02 10:05:15.0',
  input_date: 20220126,
  insur_bldg: 'Y',
  internet: 'Y',
  kch: 1,
  lastupdated: new Date('2022-05-01T04:05:28.000Z'),
  lat: 43.648850109532184,
  lcTp: 'Exp',
  ld: 20220126,
  lkr: 'None',
  lng: -79.37813944370097,
  loc: {
    type: 'Point',
    coordinates: [
      -79.37813944370097,
      43.648850109532184
    ]
  },
  lp: 453000,
  lsrc: 'evow',
  lst: 'Exp',
  ltp: 'MLS',
  lup: new Date('2022-05-01T04:05:28.000Z'),
  lvl: 5,
  m: 'Fabulous Income Property. Currently In The Hotel Rental Pool. Fully Furnished Historic 440 Sqf Suite Minutes From The Financial District & Amp; Direct Building Access To Subway And Path.  Offer On Commercial Form. Luxurious Facilities Including Gym, Pool, And So Much More. Condo Fees Include Tv, Internet, Heat, Hydro, Water! All Existing Chattels, Fixtures, Appliances. Buyer To Verify Taxes. Income Statement Is Available Upon Request. Status Certificate Will Be Available. Factor 1.2',
  maxpicsz: 266880,
  metaInfo: {},
  mfee: 661.68,
  mmap_col: 19,
  mmap_page: 120,
  mmap_row: 'S',
  mt: new Date('2022-05-01T05:11:48.013Z'),
  num_kit: 1,
  olp: 453000,
  onD: 20220126,
  origBsmt: [
    'None'
  ],
  origId: [
    'TRB********'
  ],
  origProv: 'Ontario',
  origSt: 'King',
  park_desig: 'None',
  park_fac: 'None',
  park_spcs: 0,
  pets: 'N',
  pho: 29,
  phodl: new Date('2022-01-26T17:06:46.000Z'),
  phomt: new Date('2022-01-26T17:06:46.000Z'),
  phosrc: 'trb',
  picTrb: {
    picNum: 29,
    maxPicSz: 266880,
    avgPicSz: 176778.4827586207
  },
  pr_lsc: 'New',
  prkg_inc: 'N',
  prop_mgmt: 'Y.L Hendler Property',
  prov: 'ON',
  psn: '30/60',
  pstyl: 'Apartment',
  ptp: 'Condo Apt',
  ptype: 'r',
  ptype2: [
    'Apartment'
  ],
  region: 'Toronto',
  rltr: 'RE/MAX PREMIER INC., BROKERAGE',
  rmSqft: 440,
  rms: [
    {
      t: 'Kitchen',
      l: 'Flat',
      w: 2.37,
      h: 3.75,
      d: [
        'Tile Floor',
        'Granite Counter',
        'Open Concept'
      ]
    },
    {
      t: 'Living',
      l: 'Flat',
      w: 5.4,
      h: 3.65,
      d: [
        'Combined W/Prim Bdrm',
        'Broadloom',
        'Large Window'
      ]
    },
    {
      t: 'Prim Bdrm',
      l: 'Flat',
      w: 5.4,
      h: 3.65,
      d: [
        'Combined W/Living',
        'Broadloom'
      ]
    }
  ],
  saletp: [
    'Sale'
  ],
  showAddr: '1 King St W',
  sid: '********',
  spcts: new Date('2022-05-01T04:05:28.000Z'),
  spec: [
    'Unknown'
  ],
  sqft: '0-499',
  sqft1: 0,
  sqft2: 499,
  sqftQ: 1,
  sqftSrc: 'self',
  src: 'TRB',
  st: 'King St W',
  st_dir: 'W',
  st_num: '1',
  st_sfx: 'St',
  status: 'U',
  synced: new Date('2022-05-01T04:05:28.000Z'),
  tax: 3734.46,
  taxyr: 2021,
  tbdrms: 1,
  tgr: 0,
  trbtp: [
    'evow',
    'idx'
  ],
  trms: 3,
  ts: new Date('2022-01-26T17:08:18.176Z'),
  tv: 'Y',
  type_own_srch: 'C.',
  uaddr: 'CA:ON:TORONTO:1 KING ST W',
  unavail_dt: 20220430,
  unit_num: 16,
  unt: '516',
  vend_pis: 'N',
  water_inc: 'Y',
  xd: '2022-04-30 00:00:00.0',
  zip: 'M5H1A1',
  # _mt: result._mt,
}
DDF24537946 = {
  _id: 'DDF24537946',
  AmmenitiesNearBy: 'Public Transit',
  Board: 114,
  Building: {
    BathroomTotal: 1,
    Amenities: 'Exercise Centre',
    Appliances: [
      'Dishwasher',
      'Dryer',
      'Refrigerator',
      'Stove',
      'Washer',
      'Microwave Built-in',
      'Hood Fan'
    ],
    BasementType: 'None',
    ConstructionStyleAttachment: 'Attached',
    CoolingType: 'Central air conditioning',
    ExteriorFinish: 'Other',
    FireplacePresent: 'False',
    HeatingFuel: 'Natural gas',
    HeatingType: 'Heat Pump',
    SizeInterior: '',
    StoriesTotal: '1',
    Type: 'Apartment',
    UtilityWater: 'Municipal water'
  },
  BuildingType: 'Apartment',
  Business: {
    Franchise: ''
  },
  Features: 'Balcony',
  Land: {
    Acreage: 'false',
    Amenities: 'Public Transit',
    Sewer: 'Municipal sewage system'
  },
  ListingContractDate: 20220614,
  LocationDescription: 'Entrance off of Bay St.',
  MaintenanceFeeType: [
    'Insurance',
    'Heat',
    'Water'
  ],
  OwnershipType: 'Condominium',
  Parking: [
    {
        Name: 'Underground'
    },
    {
        Name: 'None'
    }
  ],
  Price: 499000,
  PropertyType: 'Single Family',
  StorageType: 'Locker',
  TransactionType: 'For sale',
  ZoningDescription: 'unknown',
  addr: '111 Elizabeth St',
  bdrms: 0,
  br_plus: 0,
  bsmt: [
    'NON'
  ],
  bthrms: 1,
  cases: [
    'e',
    'l'
  ],
  city: 'Toronto',
  cmty: 'Bay Street Corridor',
  cnty: 'CA',
  ddfID: 'DDF24537946',
  faddr: '111 Elizabeth St,Toronto, ON M5G1P7, CA',
  fpl: 'False',
  fuel: 'Natural gas',
  geoq: 120,
  heat: 'Heat Pump',
  his: [
    {
        s: 'New',
        lp: 499000,
        ts: new Date('2022-06-14T15:53:26.071Z'),
        d: 20220614
    },
    {
        s: 'Chg',
        ts: new Date('2022-06-20T20:51:20.000Z'),
        o: 'A',
        n: 'U'
    },
    {
        s: 'chM',
        ts: new Date('2022-06-20T20:51:20.000Z'),
        d: 20220620
    },
    {
        s: 'addPic',
        ts: new Date('2022-06-20T20:51:20.000Z'),
        d: 20220620
    }
  ],
  la2: {
    agnt: [
        {
          id: '2131241',
          _id: 'DDF2131241',
          nm: 'ANTHONY DIFRUSCIA',
          pstn: 'Salesperson',
          tel: '(*************,(*************',
          url: 'http://www.boldtrealty.ca',
          office: {
            id: '283653',
            _id: 'DDF283653',
            nm: 'BOLDT REALTY INC., BROKERAGE',
            city: 'St. Catharines',
            prov: 'ON',
            addr: '211 SCOTT STREET',
            zip: 'L2N1H5',
            tel: '(*************',
            fax: '(*************',
            url: 'http://www.boldtrealty.ca'
          }
        }
      ],
    id: '283653',
    _id: 'DDF283653',
    nm: 'BOLDT REALTY INC., BROKERAGE',
    city: 'St. Catharines',
    prov: 'ON',
    addr: '211 SCOTT STREET',
    zip: 'L2N1H5',
    tel: '(*************',
    fax: '(*************',
    url: 'http://www.boldtrealty.ca'
  },
  lat: 43.655100885671736,
  lcTp: 'New',
  link: 'https://www.realtor.ca/real-estate/24537946/111-elizabeth-street-unit-923a-toronto',
  lng: -79.38474046471556,
  loc: {
    type: 'Point',
    coordinates: [
      -79.38474046471556,
      43.655100885671736
    ]
  },
  lp: 499000,
  lst: 'New',
  lup: new Date('2022-06-20T20:51:20.000Z'),
  m: "If location is what you're looking for, look no further! Dundas Square, Grocery Stores, TTC, Eaton Centre, U of T, TMU, Sick Kids Hospital all nearby. This open concept studio unit features a large walkout balcony with views of downtown, laminate flooring throughout the living area, modern kitchen cabinets and a 4 piece bathroom. If you're not sold yet, take advantage of the building amenities: 24 hour concierge, roof top terrace, gym, pool, hot tub, party room, guest suites, visitor parking and business centre. (id:22211)",
  metaInfo: {},
  mfee: 262.79,
  mfeeunt: 'Monthly',
  mt: new Date('2022-06-20T20:51:20.000Z'),
  olp: 499000,
  onD: 20220614,
  origAddr: '111 ELIZABETH Street Unit# 923A',
  origBsmt: [
    'None'
  ],
  origCmty: 'Tcbc - Bay Street Corridor',
  origId: [
    'DDF24537946'
  ],
  origProv: 'Ontario',
  origSt: 'ELIZABETH',
  pho: 28,
  phodl: new Date('2022-06-20T20:51:20.000Z'),
  phomt: new Date('2022-06-20T20:51:20.000Z'),
  phosrc: 'ddf',
  picDdf: {
    picNum: 28,
    maxPicSz: 187060,
    avgPicSz: 103281.***********
  },
  prov: 'ON',
  ptype: 'r',
  ptype2: [
    'Apartment'
  ],
  rltr: 'BOLDT REALTY INC., BROKERAGE',
  rms: [
    {
      t: '4pc Bathroom',
      l: 'Main',
      d: [
          'Measurements not available'
      ]
    },
    {
      t: 'Kitchen',
      l: 'Main',
      d: [
          "11'5'' x 9'7''"
      ],
      h: 3.48,
      w: 2.92
    },
    {
      t: 'Dining',
      l: 'Main',
      d: [
          "12'4'' x 11'9''"
      ],
      h: 3.76,
      w: 3.58
    },
    {
      t: 'Living',
      l: 'Main',
      d: [
          "12'4'' x 11'9''"
      ],
      h: 3.76,
      w: 3.58
    }
  ],
  saletp: [
      'Sale'
  ],
  showAddr: '111 ELIZABETH Street',
  sid: '40275446',
  spcts: new Date('2022-06-20T20:51:20.000Z'),
  src: 'DDF',
  st: 'Elizabeth',
  st_num: '111',
  st_sfx: 'St',
  status: 'U',
  style_attach: 'Attached',
  tbdrms: 0,
  ts: new Date('2022-06-14T15:53:26.071Z'),
  uaddr: 'CA:ON:TORONTO:111 ELIZABETH ST',
  unt: '923a',
  zip: 'M5G1P7',
  # _mt: result._mt,
  }
TRBW5743907 = {
  _id: 'TRBW5743907',
  ac: 'Central Air',
  addr: '1050 Stainton Dr',
  amen: [
    'Bbqs Allowed',
    'Exercise Room',
    'Games Room',
    'Party/Meeting Room',
    'Visitor Parking'
  ],
  apt_num: '223',
  area_code: '05',
  asmt: 191000,
  ass_year: 2021,
  avgpicsz: 113318.96,
  bdrms: 3,
  blcny: 'Open',
  bsmt: [
    'NON'
  ],
  bsmt1_out: 'None',
  bthrms: 2,
  bths: [
    {
      t: 1,
      l: 'Main',
      p: 3
    },
    {
      t: 1,
      l: '2nd',
      p: 4
    }
  ],
  cac_inc: 'N',
  cases: [
    'f'
  ],
  city: 'Mississauga',
  city_d: 'Mississauga',
  citycd: '05.03',
  cmty: 'Erindale',
  cmtycd: '05.03.0180',
  cnty: 'CA',
  comel_inc: 'Y',
  comm: '2.5%. Plus Hst',
  cond_txinc: 'N',
  condo_corp: 'PCC',
  constr: [
    'Brick'
  ],
  corp_num: 51,
  crsst: 'Dundas And Erindale St.',
  cur: 'CND',
  daddr: 'Y',
  den_fr: 'N',
  ens_lndry: 'Y',
  exp: 20221125,
  faddr: '1050 Stainton Dr,Mississauga, ON L5C2T7, CA',
  fce: 'W',
  fpl: 'N',
  fuel: 'Gas',
  gatp: 'Undergrnd',
  geoq: 120,
  gr: 1,
  heat: 'Fan Coil',
  heat_inc: 'N',
  his: [
    {
      s: 'New',
      lp: 680000,
      ts: new Date('2022-08-25T20:09:30.345Z'),
      d: 20220825
    },
    {
      s: 'chM',
      ts: new Date('2022-08-26T14:35:05.000Z'),
      d: 20220826
    },
    {
      s: 'chVturl',
      ts: new Date('2022-08-26T14:35:05.000Z'),
      d: 20220826
    },
    {
      s: 'addPic',
      ts: new Date('2022-08-26T14:35:05.000Z'),
      d: 20220826
    }
  ],
  hydro_inc: 'N',
  id: 'TRBW5743907',
  input_date: 20220825,
  insur_bldg: 'Y',
  internet: 'Y',
  kch: 1,
  lastupdated: new Date('2022-08-26T14:35:05.000Z'),
  lat: 43.55903920666667,
  laundry_lev: 'Upper',
  lcTp: 'Pc',
  ld: 20220825,
  lkr: 'Owned',
  lng: -79.64386913333334,
  loc: {
    type: 'Point',
    coordinates: [
      -79.64386913333334,
      43.55903920666667
    ]
  },
  locker_num: '5-22',
  lp: 595000,
  lsrc: 'evow',
  lst: 'Pc',
  ltp: 'MLS',
  lup: new Date('2022-08-26T14:35:05.000Z'),
  lvl: 2,
  m: "Don't Miss Out. Spacious 2 Level Condo For Sale. One Of The Most Desired Area/Building In The Community Of Erindale, Mississauga. Such A Beauty Offering Approx. 1300 Sq. Ft. Of Living Area, 2 Story, 3 Beds, 2 Bath. Open Concept Dining Area And W/O To Private Terrace (Balcony). And Easy Access To Parking. Prime Bedroom With (3 Pc) & Walk In Closet. 2 Beds On Top Floor . Your Own In-Suite Laundry . Close To Erindale Go Station, Transit, Schools, Grocery, Public Library, Park, Shopping And All Other Amenities. ** Status Certificate Is Available.** Inclusions: Fridge, Stove, Washer, Dryer",
  maxpicsz: 150255,
  metaInfo: {},
  mfee: 765.23,
  mmap_col: 38,
  mmap_page: 472,
  mmap_row: 'M',
  mt: new Date('2022-08-26T15:41:44.447Z'),
  num_kit: 1,
  olp: 680000,
  onD: 20220825,
  origBsmt: [
    'None'
  ],
  origId: [
    'DDF24807807',
    'TRBW5743907'
  ],
  origProv: 'Ontario',
  parcel_id: 190510108,
  park_desig: 'Exclusive',
  park_fac: 'Surface',
  park_spcs: 0,
  pets: 'Restrict',
  pho: 25,
  phodl: new Date('2022-08-26T13:39:29.000Z'),
  phomt: new Date('2022-08-26T13:39:29.000Z'),
  phosrc: 'trb',
  picTrb: {
    picNum: 25,
    maxPicSz: 150255,
    avgPicSz: 113318.96
  },
  pr_lsc: 'New',
  prkg_inc: 'Y',
  prop_mgmt: 'Alba-13A-1235 Queensway',
  prov: 'ON',
  psn: '30-59 Days',
  pstyl: '2-Storey',
  ptp: 'Condo Apt',
  ptype: 'r',
  ptype2: [
    'Apartment'
  ],
  region: 'Peel',
  rltr: 'SHAW REALTY GROUP INC., BROKERAGE',
  rmSqft: 1300,
  rms: [
    {
      t: 'Bathroom',
      l: 'Main',
      w: 1.8,
      h: 1.42,
      d: [
        '3 Pc Bath'
      ]
    },
    {
      t: 'Br',
      l: 'Main',
      w: 4.75,
      h: 2.87
    },
    {
      t: 'Kitchen',
      l: 'Main',
      w: 3.51,
      h: 2.36
    },
    {
      t: 'Bathroom',
      l: 'Main',
      w: 1.6,
      h: 1.35
    },
    {
      t: 'Living',
      l: 'Main',
      w: 6.65,
      h: 3.61
    },
    {
      t: 'Dining',
      l: 'Main',
      w: 3.02,
      h: 2.36
    },
    {
      t: 'Prim Bdrm',
      l: '2nd',
      w: 3.81,
      h: 3.05
    },
    {
      t: 'Br',
      l: '2nd',
      w: 4.32,
      h: 2.82
    },
    {
      t: 'Laundry',
      l: '2nd',
      w: 1.37,
      h: 1.83
    },
    {
      t: 'Bathroom',
      l: '2nd',
      w: 1.52,
      h: 2.9,
      d: [
        '5 Pc Bath'
      ]
    }
  ],
  saletp: [
    'Sale'
  ],
  showAddr: '1050 Stainton Dr',
  sid: 'W5743907',
  spcts: new Date('2022-08-25T20:09:30.345Z'),
  spec: [
    'Unknown'
  ],
  sqft: '1200-1399',
  sqft1: 1200,
  sqft2: 1399,
  sqftQ: 1,
  sqftSrc: 'self',
  src: 'TRB',
  st: 'Stainton',
  st_num: '1050',
  st_sfx: 'Dr',
  status: 'A',
  synced: new Date('2022-08-26T14:35:05.000Z'),
  tax: 1584.8,
  taxyr: 2021,
  tbdrms: 3,
  tgr: 1,
  trbtp: [
    'evow'
  ],
  trms: 11,
  ts: new Date('2022-08-25T20:09:30.345Z'),
  tv: 'N',
  type_own_srch: 'C.',
  uaddr: 'CA:ON:MISSISSAUGA:1050 STAINTON DR',
  uffi: 'No',
  unit_num: 43,
  unt: '223',
  vend_pis: 'N',
  vturl: 'https://tours.digenovamedia.ca/1050-stainton-drive-mississauga-on-l5c-2t7',
  water_inc: 'Y',
  xd: '2022-11-25 00:00:00.0',
  zip: 'L5C2T8',
  AmmenitiesNearBy: [
    'Hospital',
    'Place of Worship',
    'Schools'
  ],
  Building: {
    BathroomTotal: 2,
    BedroomsTotal: 3,
    BedroomsAboveGround: 3,
    Amenities: [
      'Exercise Centre',
      'Party Room'
    ],
    Appliances: [
      'Dryer',
      'Refrigerator',
      'Stove',
      'Washer'
    ],
    ArchitecturalStyle: '2 Level',
    BasementType: 'None',
    ConstructedDate: '1974',
    ConstructionStyleAttachment: 'Attached',
    CoolingType: 'Central air conditioning',
    ExteriorFinish: 'Brick',
    FireplacePresent: 'False',
    FireProtection: 'Smoke Detectors',
    Fixture: 'Ceiling fans',
    FoundationType: 'Poured Concrete',
    HeatingFuel: 'Natural gas',
    SizeInterior: '1300.0000',
    StoriesTotal: '2',
    Type: 'Apartment',
    UtilityWater: 'Municipal water'
  },
  BuildingType: 'Apartment',
  Features: [
    'Conservation/green belt',
    'Balcony'
  ],
  Land: {
    SizeTotalText: 'under 1/2 acre',
    Acreage: 'false',
    Amenities: [
      'Hospital',
      'Place of Worship',
      'Schools'
    ],
    Sewer: 'Municipal sewage system'
  },
  ListingContractDate: 20220825,
  LocationDescription: 'Dundas to Erindale to Stainton',
  MaintenanceFeeType: [
    'Insurance',
    'Water',
    'Parking'
  ],
  OwnershipType: 'Condominium',
  ParkingSpaceTotal: 1,
  Price: 595000,
  PropertyType: 'Single Family',
  StorageType: 'Locker',
  TransactionType: 'For sale',
  ZoningDescription: 'R1',
  ddfID: 'DDF24807807',
  la2: {
    agnt: [
      {
        id: '1941951',
        _id: 'DDF1941951',
        nm: 'SHAW HASYJ',
        pstn: 'Salesperson',
        tel: '(*************,(*************',
        url: 'https://www.youtube.com/embed/TaT4QmOjBQo,http://shawrealtygroup.com,http://www.facebook.com/TheShawRealtyGroup/,http://www.linkedin.com/in/shawhasyj/,https://www.instagram.com/shawrealtygroup/?hl=en',
        office: {
          id: '280721',
          _id: 'DDF280721',
          nm: 'SHAW REALTY GROUP INC.',
          city: 'Cambridge',
          prov: 'ON',
          addr: '135 George St. N. Unit #201',
          zip: 'N1S5C3',
          tel: '(*************',
          fax: '(*************',
          url: 'http://www.shawrealtygroup.com'
        }
      },
      {
        id: '1974070',
        _id: 'DDF1974070',
        nm: 'CAROLINE OHI',
        pstn: 'Broker of Record',
        tel: '(*************,(*************',
        url: 'https://www.youtube.com/embed/TaT4QmOjBQo,http://shawrealtygroup.com,http://www.facebook.com/TheShawRealtyGroup/,http://ca.linkedin.com/in/carolineohi,https://www.instagram.com/carolineohi/',
        office: {
          id: '280721',
          _id: 'DDF280721',
          nm: 'SHAW REALTY GROUP INC.',
          city: 'Cambridge',
          prov: 'ON',
          addr: '135 George St. N. Unit #201',
          zip: 'N1S5C3',
          tel: '(*************',
          fax: '(*************',
          url: 'http://www.shawrealtygroup.com'
        }
      }
    ],
    id: '280721',
    _id: 'DDF280721',
    nm: 'SHAW REALTY GROUP INC.',
    city: 'Cambridge',
    prov: 'ON',
    addr: '135 George St. N. Unit #201',
    zip: 'N1S5C3',
    tel: '(*************',
    fax: '(*************',
    url: 'http://www.shawrealtygroup.com'
  },
  link: 'https://www.realtor.ca/real-estate/24807807/1050-stainton-drive-unit-223-mississauga',
  mfeeunt: 'Monthly',
  origSrc: [
    'DDF',
    'TRB'
  ],
  picDdf: {
    picNum: 26,
    maxPicSz: 111866,
    avgPicSz: 85226.07692307692
  },
  # _mt: result._mt,
}
DDF24807807 = {
  _id: 'DDF24807807',
  AmmenitiesNearBy: [
    'Hospital',
    'Place of Worship',
    'Schools'
  ],
  Board: 20,
  Building: {
    BathroomTotal: 2,
    BedroomsTotal: 3,
    BedroomsAboveGround: 3,
    Amenities: [
      'Exercise Centre',
      'Party Room'
    ],
    Appliances: [
      'Dryer',
      'Refrigerator',
      'Stove',
      'Washer'
    ],
    ArchitecturalStyle: '2 Level',
    BasementType: 'None',
    ConstructedDate: '1974',
    ConstructionStyleAttachment: 'Attached',
    CoolingType: 'Central air conditioning',
    ExteriorFinish: 'Brick',
    FireplacePresent: 'False',
    FireProtection: 'Smoke Detectors',
    Fixture: 'Ceiling fans',
    FoundationType: 'Poured Concrete',
    HeatingFuel: 'Natural gas',
    SizeInterior: '1300.0000',
    StoriesTotal: '2',
    Type: 'Apartment',
    UtilityWater: 'Municipal water'
  },
  BuildingType: 'Apartment',
  Business: {
    Franchise: ''
  },
  Features: [
    'Conservation/green belt',
    'Balcony'
  ],
  Land: {
    SizeTotalText: 'under 1/2 acre',
    Acreage: 'false',
    Amenities: [
      'Hospital',
      'Place of Worship',
      'Schools'
    ],
    Sewer: 'Municipal sewage system'
  },
  ListingContractDate: 20220825,
  LocationDescription: 'Dundas to Erindale to Stainton',
  MaintenanceFeeType: [
    'Insurance',
    'Water',
    'Parking'
  ],
  OwnershipType: 'Condominium',
  Parking: [
    {
      Name: 'Underground'
    },
    {
      Name: 'Visitor Parking'
    }
  ],
  ParkingSpaceTotal: 1,
  Price: 595000,
  PropertyType: 'Single Family',
  StorageType: 'Locker',
  TransactionType: 'For sale',
  ZoningDescription: 'R1',
  addr: '1050 Stainton Dr',
  arch_style: '2 Level',
  bdrms: 3,
  bltYr: 1974,
  br_plus: 0,
  bsmt: [
    'NON'
  ],
  bthrms: 2,
  cases: [
    'e',
    'l'
  ],
  city: 'Mississauga',
  cmty: 'Erindale',
  cnty: 'CA',
  ddfID: 'DDF24807807',
  faddr: '1050 Stainton Dr,Mississauga, ON L5C2T7, CA',
  fpl: 'False',
  fuel: 'Natural gas',
  geoq: 120,
  his: [
    {
      s: 'New',
      lp: 595000,
      ts: new Date('2022-08-25T21:01:14.055Z'),
      d: 20220825
    },
    {
      s: 'chM',
      ts: new Date('2022-08-26T00:24:49.000Z'),
      d: 20220825
    },
    {
      s: 'chVturl',
      ts: new Date('2022-08-26T00:24:49.000Z'),
      d: 20220825
    },
    {
      s: 'addPic',
      ts: new Date('2022-08-26T00:24:49.000Z'),
      d: 20220825
    }
  ],
  la2: {
    agnt: [
      {
        id: '1941951',
        _id: 'DDF1941951',
        nm: 'SHAW HASYJ',
        pstn: 'Salesperson',
        tel: '(*************,(*************',
        url: 'https://www.youtube.com/embed/TaT4QmOjBQo,http://shawrealtygroup.com,http://www.facebook.com/TheShawRealtyGroup/,http://www.linkedin.com/in/shawhasyj/,https://www.instagram.com/shawrealtygroup/?hl=en',
        office: {
          id: '280721',
          _id: 'DDF280721',
          nm: 'SHAW REALTY GROUP INC.',
          city: 'Cambridge',
          prov: 'ON',
          addr: '135 George St. N. Unit #201',
          zip: 'N1S5C3',
          tel: '(*************',
          fax: '(*************',
          url: 'http://www.shawrealtygroup.com'
        }
      },
      {
        id: '1974070',
        _id: 'DDF1974070',
        nm: 'CAROLINE OHI',
        pstn: 'Broker of Record',
        tel: '(*************,(*************',
        url: 'https://www.youtube.com/embed/TaT4QmOjBQo,http://shawrealtygroup.com,http://www.facebook.com/TheShawRealtyGroup/,http://ca.linkedin.com/in/carolineohi,https://www.instagram.com/carolineohi/',
        office: {
          id: '280721',
          _id: 'DDF280721',
          nm: 'SHAW REALTY GROUP INC.',
          city: 'Cambridge',
          prov: 'ON',
          addr: '135 George St. N. Unit #201',
          zip: 'N1S5C3',
          tel: '(*************',
          fax: '(*************',
          url: 'http://www.shawrealtygroup.com'
        }
      }
    ],
    id: '280721',
    _id: 'DDF280721',
    nm: 'SHAW REALTY GROUP INC.',
    city: 'Cambridge',
    prov: 'ON',
    addr: '135 George St. N. Unit #201',
    zip: 'N1S5C3',
    tel: '(*************',
    fax: '(*************',
    url: 'http://www.shawrealtygroup.com'
  },
  lat: 43.55903920666667,
  lcTp: 'New',
  link: 'https://www.realtor.ca/real-estate/24807807/1050-stainton-drive-unit-223-mississauga',
  lng: -79.64386913333334,
  loc: {
    type: 'Point',
    coordinates: [
      -79.64386913333334,
      43.55903920666667
    ]
  },
  lp: 595000,
  lst: 'New',
  lup: new Date('2022-08-26T00:24:49.000Z'),
  m: "Don't Miss Out. Spacious 2 LEVEL Condo for Sale. One of the  Most Desired Area/Building in The Community of Erindale, Mississauga. Such A Beauty Offering Approx. 1300 Sq. Ft. Of Living Area, 2 Story, 3 Beds, 2 Bath. Open Concept Dining area and W/O To Private Terrace (Balcony). And Easy Access To Parking. Prime Bedroom With (3 Pc) &  Walk In Closet. 2 Beds on top floor .  Your Own In-Suite Laundry . Close To Erindale Go Station, Transit, Schools, Grocery,  Public Library, Park, Shopping And All Other Amenities. ** Status Certificate Is Available.** (id:22211)",
  merged: 'TRBW5743907',
  metaInfo: {},
  mfee: 765.23,
  mfeeunt: 'Monthly',
  mt: new Date('2022-08-26T00:24:49.000Z'),
  olp: 595000,
  onD: 20220825,
  origAddr: '1050 STAINTON Drive Unit# 223',
  origBsmt: [
    'None'
  ],
  origCmty: '0180 - Erindale',
  origId: [
    'DDF24807807'
  ],
  origProv: 'Ontario',
  origSt: 'STAINTON',
  pho: 26,
  phodl: new Date('2022-08-25T21:24:52.000Z'),
  phomt: new Date('2022-08-25T21:24:52.000Z'),
  phosrc: 'ddf',
  picDdf: {
    picNum: 26,
    maxPicSz: 111866,
    avgPicSz: 85226.07692307692
  },
  prov: 'ON',
  ptype: 'r',
  ptype2: [
    'Apartment'
  ],
  rltr: 'SHAW REALTY GROUP INC.',
  rms: [
    {
      t: '4pc Bathroom',
      l: 'Second',
      d: [
        "9'6'' x 5'0''"
      ],
      h: 2.9,
      w: 1.52
    },
    {
      t: 'Laundry',
      l: 'Second',
      d: [
        "6'0'' x 4'6''"
      ],
      h: 1.83,
      w: 1.37
    },
    {
      t: 'Bedroom',
      l: 'Second',
      d: [
        "9'3'' x 14'2''"
      ],
      h: 2.82,
      w: 4.32
    },
    {
      t: 'Primary Bedroom',
      l: 'Second',
      d: [
        "10'0'' x 12'6''"
      ],
      h: 3.05,
      w: 3.81
    },
    {
      t: '3pc Bathroom',
      l: 'Main',
      d: [
        "4'5'' x 5'3''"
      ],
      h: 1.35,
      w: 1.6
    },
    {
      t: 'Living',
      l: 'Main',
      d: [
        "11'10'' x 21'10''"
      ],
      h: 3.61,
      w: 6.65
    },
    {
      t: 'Dining',
      l: 'Main',
      d: [
        "7'9'' x 9'11''"
      ],
      h: 2.36,
      w: 3.02
    },
    {
      t: 'Kitchen',
      l: 'Main',
      d: [
        "7'9'' x 11'6''"
      ],
      h: 2.36,
      w: 3.51
    },
    {
      t: 'Bedroom',
      l: 'Main',
      d: [
        "9'5'' x 15'7''"
      ],
      h: 2.87,
      w: 4.75
    }
  ],
  saletp: [
    'Sale'
  ],
  showAddr: '1050 STAINTON Drive',
  sid: '40313967',
  spcts: new Date('2022-08-25T21:01:14.055Z'),
  sqft: 1300,
  sqft1: 1300,
  sqft2: 1300,
  src: 'DDF',
  st: 'Stainton',
  st_num: '1050',
  st_sfx: 'Dr',
  status: 'A',
  style_attach: 'Attached',
  tbdrms: 3,
  tgr: 1,
  ts: new Date('2022-08-25T21:01:14.055Z'),
  uaddr: 'CA:ON:MISSISSAUGA:1050 STAINTON DR',
  unt: '223',
  vturl: 'https://tours.digenovamedia.ca/1050-stainton-drive-mississauga-on-l5c-2t7?branded=1',
  zip: 'L5C2T7',
  # _mt: result._mt,
}

describe 'saveToMaster',->
  before (done) ->
    @timeout(3000000)
    request = helpers.getServer()
    saveToMaster = helpers.INCLUDE('libapp.saveToMaster')
    gProperties =  helpers.COLLECTION('vow', 'properties')
    getBoundaryTagAsync = helpersFunction.addAsyncSupport getBoundaryTag
    # TODO: fix this using geoCoder model
    GeoCoder = helpers.MODEL 'GeoCoder'
    gGeoCache = helpers.COLLECTION('vow', 'geo_cache')
    gTrebRecords = helpers.COLLECTION('rni', 'mls_treb_master_records')
    gDDFRecords = helpers.COLLECTION('rni', 'mls_crea_ddf_records')
    gPropertiesImportLog = helpers.COLLECTION('tmp', 'properties_import_log')
    gBoundary = helpers.COLLECTION('vow', 'boundary')
    dbs = [
      { db: 'vow', table: 'geo_cache' },
      { db: 'vow', table: 'properties' },
      { db: 'tmp', table: 'properties_import_log' },
    ]
    # TODO: 1 naming, 2 deprecate path array, use 'dbs.tmp.host'(object-path), 3 on testfs its not localhost, its ca12
    # tmpColExists =  helpers.checkHasConfig ['dbs','tmp','host'],'localhost'
    # tmpColExists.should.be.exactly(true)
    helpers.cleanDBs dbs, () ->
      helpers.checkAndsetUpCommonFixture (err)->
        console.error err if err
        helpers.checkAndsetUpFixture { folder:__dirname, useMongoImport:true }, ()->
          done()
    return

  xdescribe 'convertAndSaveRecord', ->
    @timeout(20000)
    it 'import from treb master', () ->
      return new Promise((resolve,reject)->
        try
          prop = await gTrebRecords.findOne { _id: 'TRB********' }
          await new Promise((resolve2, reject) ->
            saveToMaster.convertAndSaveRecord {
              srcType: 'treb',
              GeoCoder,
              targetProperties: gProperties,
              PropertiesImportLog: gPropertiesImportLog,
              record: prop,
              transitCities: gTransitCities,
              TransitStop: gTransitStop,
              School: gSchool,
              Boundary: gBoundary,
              Census2016: gCensus2016,
              Census2016ReversKeys: gCensus2016ReversKeys,
            }, (err,record,prop) ->
              should.not.exists err
              return throw err if err
              # return reject err if err
              resolve2(record)
          )
          result = await gProperties.findOne { _id: 'TRB********' }
          console.log '+++++',result
          should.exists result
          TRB********._mt = result._mt
          should.deepEqual result,TRB********
          return resolve()
        catch err
          return reject err
      )
    it 'import from crea ddf', () ->
      return new Promise((resolve,reject)->
        try
          prop = await gDDFRecords.findOne { _id: 'DDF24537946' }
          await new Promise((resolve2, reject2) ->
            saveToMaster.convertAndSaveRecord {
              srcType: 'ddf',
              # GeoCache: gGeoCache,
              GeoCoder,
              targetProperties: gProperties,
              PropertiesImportLog: gPropertiesImportLog,
              record: prop,
              transitCities: gTransitCities,
              TransitStop: gTransitStop,
              School: gSchool,
              Boundary: gBoundary,
              Census2016: gCensus2016,
              Census2016ReversKeys: gCensus2016ReversKeys,
            }, (err) ->
              should.not.exists err
              return throw err if err
              resolve2()
          )
          result = await gProperties.findOne { _id: 'DDF24537946' }
          should.exists result
          DDF24537946._mt = result._mt
          should.deepEqual result,DDF24537946
          return resolve()
        catch err
          return reject err
      )

    it 'import same prop from treb master then crea ddf', () ->
      return new Promise((resolve,reject)->
        try
          prop = await gTrebRecords.findOne { _id: 'TRBW5743907' }
          await new Promise((resolve2, reject) ->
            saveToMaster.convertAndSaveRecord {
              srcType: 'treb',
              GeoCoder,
              targetProperties: gProperties,
              PropertiesImportLog: gPropertiesImportLog,
              record: prop,
              transitCities: gTransitCities,
              TransitStop: gTransitStop,
              School: gSchool,
              Boundary: gBoundary,
              Census2016: gCensus2016,
              Census2016ReversKeys: gCensus2016ReversKeys,
            }, (err) ->
              return reject err if err
              resolve2()
          )
          prop = await gDDFRecords.findOne { _id: 'DDF24807807' }
          await new Promise((resolve3, reject) ->
            saveToMaster.convertAndSaveRecord {
              srcType: 'ddf',
              GeoCoder,
              targetProperties: gProperties,
              PropertiesImportLog: gPropertiesImportLog,
              record: prop,
              transitCities: gTransitCities,
              TransitStop: gTransitStop,
              School: gSchool,
              Boundary: gBoundary,
              Census2016: gCensus2016,
              Census2016ReversKeys: gCensus2016ReversKeys,
            }, (err) ->
              return reject err if err
              resolve3()
          )
          result = await gProperties.findOne { _id: 'TRBW5743907' }
          should.exists result
          TRBW5743907._mt = result._mt
          should.deepEqual result,TRBW5743907
          
          result = await gProperties.findOne { _id: 'DDF24807807' }
          should.exists result
          DDF24807807._mt = result._mt
          should.deepEqual result,DDF24807807
          return resolve()
        catch err
          return reject err
      )
      
  describe 'addLog to PropertiesImportLog',  ->
    it 'import from treb master', () ->
      return new Promise((resolve,reject)->
        try
          _id = 'TRB********'
          # prop = await gTrebRecords.findOne { _id }
          prop = helpersObj.deepCopyObject TRB********
          prop.Lp_dol = 499000
          prop.Lsc = 'U'
          prop.lSrc = 'treb'
          srcType = 'treb'
          await new Promise((resolve2, reject) ->
            log = {status:prop.status,m:"#{srcType}: _changeStatusOnly"}
            addLogs {id:"#{_id}",log,prop,collPropertyImportLog:gPropertiesImportLog},(err)->
              should.not.exists err
              return throw err if err
              # return reject err if err
              resolve2()
          )
          result = await gPropertiesImportLog.findOne { _id }
          should.exists result
          delete result._mt
          delete result.logs[0].ts
          should.deepEqual result,{
            "_id": "TRB********"
            "logs": [
              {
                "m": "treb: _changeStatusOnly"
                "status": "U"
                "step": "watch"
                "Lsc": "U"
                "Lp_dol": 499000
                "lSrc": "treb"
              }
            ]
          }
          return resolve()
        catch err
          return reject err
      )
  
  # NOTE: 目前ut测试服Boundary只有Toronto,tp:city,暂时不重新导入Boundary数据
  describe 'setPropGeoQAndBoundaryTags', ->
    tests = [
      {
        desc:'should not skip geoCoding when prop has no lat&lng',
        prop:{_id:'testNoLoc'},
        expected:{_id:'testNoLoc'}
      },
      {
        desc:'should not skip geoCoding when prop has no lat',
        prop:{_id:'testNoLoc',lat:43.64828},
        expected:{_id:'testNoLoc',lat:43.64828}
      },
      {
        desc:'should not skip geoCoding when prop has no lng',
        prop:{_id:'testNoLoc',lng:-79.486605},
        expected:{_id:'testNoLoc',lng:-79.486605}
      },
      { # city应该是Toronto
        desc:'should not skip geoCoding when prop has loc but city isnt Boundary.city',
        prop:{_id:'testNoLoc',lat:43.64828411774102,lng:-79.48660530491702,city:'Vancouver'},
        expected:{_id:'testNoLoc',lat:43.64828411774102,lng:-79.48660530491702,city:'Vancouver'}
      },
      {
        desc:'should skip geoCoding and set boundary tags when prop has valid loc and city matches Boundary.city',
        prop:{_id:'testNoLoc',lat:43.64828411774102,lng:-79.48660530491702,city:'Toronto'},
        expected:{
          _id:'testNoLoc',
          lat:43.64828411774102,
          lng:-79.48660530491702,
          city:'Toronto',
          keepPropGeocoding:true,
          geoq:100,
          loc:{type:'Point',coordinates:[-79.48660530491702,43.64828411774102]},
          bndCity:{ _id: 'CA:ON:TORONTO', nm: 'Toronto', tp: 'C', sz: 1 }
        }
      },
      {
        desc:'should skip geoCoding and set geoq to 90 when no boundary tags found',
        prop:{_id:'testNoLoc',lat:40.64828411774102,lng:-75.48660530491702,city:'RemoteArea'},
        expected:{
          _id:'testNoLoc',
          lat:40.64828411774102,
          lng:-75.48660530491702,
          city:'RemoteArea',
          keepPropGeocoding:true,
          geoq:90,
          loc:{type:'Point',coordinates:[-75.48660530491702,40.64828411774102]}
        }
      },
      {
        desc:'should use existing geo info when address not changed',
        prop:{_id:'TRB********', zip:'M5H1A1', city:'Toronto', prov:'ON',addr:'1 King St W'},
        oldProp:true,
        expected:{
          _id:'TRB********',
          prov:'ON',
          city:'Toronto',
          addr:'1 King St W',
          uaddr:'CA:ON:TORONTO:1 KING ST W',
          zip:'M5H1A1',
          keepPropGeocoding:true,
          geoq:120,
          lat:43.648850109532184,
          lng:-79.37813944370097,
          loc:{type:'Point',coordinates:[-79.37813944370097,43.648850109532184]}
        }
      },
      {
        desc:'should use existing geo info when address not changed and params has oldProp',
        prop:{_id:'TRB********', zip:'M5H1A1', city:'Toronto', prov:'ON',addr:'1 King St W'},
        oldProp:true,
        paramOldProp:true,
        expected:{
          _id:'TRB********',
          prov:'ON',
          city:'Toronto',
          addr:'1 King St W',
          uaddr:'CA:ON:TORONTO:1 KING ST W',
          zip:'M5H1A1',
          keepPropGeocoding:true,
          geoq:120,
          lat:43.648850109532184,
          lng:-79.37813944370097,
          loc:{type:'Point',coordinates:[-79.37813944370097,43.648850109532184]}
        }
      },
      {
        desc:'should update uaddr when geocache exists',
        prop:{_id:'testNoLoc',lat:43.64828411774102,lng:-79.48660530491702,city:'Toronto',uaddr:'CA:ON:TORONTO:123 MAIN ST E'},
        geocache:{_id:'CA:ON:TORONTO:123 MAIN ST',aUaddr:['CA:ON:TORONTO:123 MAIN ST','CA:ON:TORONTO:123 MAIN ST E']},
        expected:{
          _id:'testNoLoc',
          lat:43.64828411774102,
          lng:-79.48660530491702,
          city:'Toronto',
          uaddr:'CA:ON:TORONTO:123 MAIN ST',
          keepPropGeocoding:true,
          geoq:100,
          loc:{type:'Point',coordinates:[-79.48660530491702,43.64828411774102]},
          bndCity:{ _id: 'CA:ON:TORONTO', nm: 'Toronto', tp: 'C', sz: 1 }
        }
      }
    ]
    tests.forEach (test)->
      it test.desc, ()->
        if test.oldProp
          try
            await gProperties.deleteOne {_id:'TRB********'}
            await gProperties.insertOne(TRB********)
          catch err
            console.error err
            should.not.exists(err)
        if test.geocache
          try
            await gGeoCache.deleteOne {_id:test.geocache._id}
            await gGeoCache.insertOne(test.geocache)
          catch err
            console.error err
            should.not.exists(err)
        if test.paramOldProp
          oldProp = await gProperties.findOne {_id:'TRB********'}
        prop = Object.assign {},test.prop
        try
          if test.noGeoCoder
            await saveToMaster.setPropGeoQAndBoundaryTags {prop,Boundary:gBoundary,Properties:gProperties,oldProp}
          else
            await saveToMaster.setPropGeoQAndBoundaryTags {prop,Boundary:gBoundary,GeoCoder,Properties:gProperties,oldProp}
        catch err
          console.error err
          should.not.exists(err)
        should.deepEqual(prop,test.expected)
        if test.expected.bndCity
          # 第二次跳过Boundary查找
          tags = await getBoundaryTagAsync {
            Boundary:gBoundary,
            prop: test.expected
          }
          should.deepEqual(tags,{})
        return

  describe 'Test addHistoryForOldProp', ->
    tests = [
      {
        desc:'should not reset his when oldProp has no his',
        input:[
          {
            _id:'TRBE9373054'
            saletp:['Sale'],
            lp:588000,
            lup:new Date('2024-09-29T19:10:13.000Z'),
            mt:new Date('2024-09-30T08:04:25.994Z'),
            _mt:new Date('2024-09-30T08:20:13.029Z'),
            lst:'New',
            status:'A',
            ts:new Date('2024-09-30T06:40:53.779Z'),
            m:'Bright and Modern 1 Bedroom + Den',
            vturl:'https://www.youtube.com/watch?v=sl2DYO1IMVw',
            pho:27,
            ohz:[{
              f:new Date('2024-10-08T13:30:00.000Z'),
              t:new Date('2024-10-08T13:30:00.000Z'),
              tp:'P'
            }]
          },
          {
            _id:'TRBE9373054'
            saletp:['Sale'],
            lp:598000,
            lup:new Date('2024-09-29T19:10:13.000Z'),
            mt:new Date('2024-09-30T08:04:25.994Z'),
            _mt:new Date('2024-09-30T08:10:43.269Z'),
            lst:'New',
            status:'A',
            ts:new Date('2024-09-30T06:40:53.779Z'),
            m:'Bright and Modern 1 Bedroom + Den, 2 Bathroom Apartment',
          },
          'treb'
        ],
        expectedResult:{
          his: [
            {
              s: 'Pc',
              ts: new Date(),
              d: 20240929,
              o: 598000,
              n: 588000,
              c: -10000
            },
            { s: 'chM', ts: new Date(), d: 20240929 },
            { s: 'chVturl', ts: new Date(), d: 20240929 },
            { s: 'addPic', ts: new Date(), d: 20240929 },
            { s: 'chOh', ts: new Date(), d: 20240929 }
          ],
          spcts: new Date('2024-09-29T19:10:13.000Z'),
          pcts: new Date('2024-09-29T19:10:13.000Z'),
          lcTp: 'Pc',
          lcO: 598000,
          lcN: 588000,
          pc: -10000,
          pcPct: -0.016722408026755852
        },
        expectedMetaInfo:{unset:{}}
      },
      {
        desc:'should push his when oldProp has his',
        input:[
          {
            _id:'TRBE9373054'
            saletp:['Sale'],
            lp:588000,
            lup:new Date('2024-09-29T19:10:13.000Z'),
            mt:new Date('2024-09-30T08:04:25.994Z'),
            _mt:new Date('2024-09-30T08:20:13.029Z'),
            lst:'New',
            status:'A',
            ts:new Date('2024-09-30T06:40:53.779Z'),
            m:'Bright and Modern 1 Bedroom + Den',
            vturl:'https://www.youtube.com/watch?v=sl2DYO1IMVw',
            pho:27,
            ohz:[{
              f:new Date('2024-10-08T13:30:00.000Z'),
              t:new Date('2024-10-08T13:30:00.000Z'),
              tp:'P'
            }]
          },
          {
            _id:'TRBE9373054'
            saletp:['Sale'],
            lp:598000,
            lup:new Date('2024-09-29T19:10:13.000Z'),
            mt:new Date('2024-09-30T08:04:25.994Z'),
            _mt:new Date('2024-09-30T08:10:43.269Z'),
            lst:'New',
            status:'A',
            ts:new Date('2024-09-30T06:40:53.779Z'),
            m:'Bright and Modern 1 Bedroom + Den, 2 Bathroom Apartment',
            his:[{ s: 'New', lp:598000, ts: new Date('2024-09-29T19:10:13.000Z'), d: 20240929}]
          },
          'treb'
        ],
        expectedResult:{
          his: [
            { s: 'New', lp:598000, ts: new Date('2024-09-29T19:10:13.000Z'), d: 20240929},
            {
              s: 'Pc',
              ts: new Date(),
              d: 20240929,
              o: 598000,
              n: 588000,
              c: -10000
            },
            { s: 'chM', ts: new Date(), d: 20240929 },
            { s: 'chVturl', ts: new Date(), d: 20240929 },
            { s: 'addPic', ts: new Date(), d: 20240929 },
            { s: 'chOh', ts: new Date(), d: 20240929 }
          ],
          spcts: new Date('2024-09-29T19:10:13.000Z'),
          pcts: new Date('2024-09-29T19:10:13.000Z'),
          lcTp: 'Pc',
          lcO: 598000,
          lcN: 588000,
          pc: -10000,
          pcPct: -0.016722408026755852
        },
        expectedMetaInfo:{unset:{}}
      },
      {
        desc:'should push his when oldProp has his and sp changed',
        hasSp:true,
        input:[
          {
            _id:'TRBE9373054'
            saletp:['Sale'],
            lp:588000,
            sp:578000,
            lup:new Date('2024-09-29T19:10:13.000Z'),
            mt:new Date('2024-09-30T08:04:25.994Z'),
            _mt:new Date('2024-09-30T08:20:13.029Z'),
            lst:'Sld',
            status:'U',
            offD:20241020,
            ts:new Date('2024-09-30T06:40:53.779Z'),
            m:'Bright and Modern 1 Bedroom + Den',
            vturl:'https://www.youtube.com/watch?v=sl2DYO1IMVw',
            pho:27,
            ohz:[{
              f:new Date('2024-10-08T13:30:00.000Z'),
              t:new Date('2024-10-08T13:30:00.000Z'),
              tp:'P'
            }],
            his:[{ s: 'New', lp:598000, ts: new Date('2024-09-29T19:10:13.000Z'), d: 20240929}]
          },
          {
            _id:'TRBE9373054'
            saletp:['Sale'],
            lp:598000,
            lup:new Date('2024-09-29T19:10:13.000Z'),
            mt:new Date('2024-09-30T08:04:25.994Z'),
            _mt:new Date('2024-09-30T08:10:43.269Z'),
            lst:'New',
            status:'A',
            ts:new Date('2024-09-30T06:40:53.779Z'),
            m:'Bright and Modern 1 Bedroom + Den, 2 Bathroom Apartment',
            his:[{ s: 'New', lp:598000, ts: new Date('2024-09-29T19:10:13.000Z'), d: 20240929}]
          },
          'treb'
        ],
        expectedResult:{
          his: [
            { s: 'New', lp:598000, ts: new Date('2024-09-29T19:10:13.000Z'), d: 20240929},
            {
              s: 'Pc',
              ts: new Date(),
              d: 20240929,
              o: 598000,
              n: 588000,
              c: -10000
            },
            { s: 'sp', ts: new Date(), d: 20240929, n: 578000 },
            { s: 'chM', ts: new Date(), d: 20240929 },
            { s: 'chVturl', ts: new Date(), d: 20240929 },
            { s: 'addPic', ts: new Date(), d: 20240929 },
            { s: 'chOh', ts: new Date(), d: 20240929 },
            { s: 'Sld', ts: new Date(), o: 'New', n: 'Sld', sldd: 20241020 }
          ],
          spcts: new Date('2024-10-20T09:16:06.173Z'),
          pcts: new Date('2024-09-29T19:10:13.000Z'),
          lcTp: 'Sld',
          lcO: 'New',
          lcN: 'Sld',
          pc: -10000,
          pcPct: -0.016722408026755852
        },
        expectedMetaInfo:{unset:{}}
      },
      {
        desc:'test status change but not lst change',
        hasChg: true,
        input:[
          {
            _id:'TRBE9373054'
            saletp:['Sale'],
            lp:598000,
            lup:new Date('2024-09-29T19:10:13.000Z'),
            mt:new Date('2024-09-30T08:04:25.994Z'),
            _mt:new Date('2024-09-30T08:20:13.029Z'),
            lst:'Cld',
            status:'U',
            offD:20241020
            ts:new Date('2024-09-30T06:40:53.779Z'),
            m:'Bright and Modern 1 Bedroom + Den',
            his:[{ s: 'New', lp:598000, ts: new Date('2024-09-29T19:10:13.000Z'), d: 20240929}]
          },
          {
            _id:'TRBE9373054'
            saletp:['Sale'],
            lp:598000,
            lup:new Date('2024-09-29T19:10:13.000Z'),
            mt:new Date('2024-09-30T08:04:25.994Z'),
            _mt:new Date('2024-09-30T08:20:13.029Z'),
            lst:'Cld',
            status:'A',
            offD:20241020
            ts:new Date('2024-09-30T06:40:53.779Z'),
            m:'Bright and Modern 1 Bedroom + Den',
            his:[{ s: 'New', lp:598000, ts: new Date('2024-09-29T19:10:13.000Z'), d: 20240929}]
          },
          'treb'
        ],
        expectedResult:{
          his: [
            { s: 'New', lp: 598000, ts: new Date('2024-09-29T19:10:13.000Z'), d: 20240929 },
            { s: 'Chg', ts: new Date(), o: 'A', n: 'U' }
          ],
          spcts: new Date('2024-10-20T09:16:06.173Z'),
          lcTp: 'Chg',
          lcO: 'A',
          lcN: 'U'
        },
        expectedMetaInfo:{unset:{}}
      },
      {
        desc:'test lst changed but status not changed',
        input:[
          {
            _id:'TRBE9373054'
            saletp:['Sale'],
            lp:598000,
            lup:new Date('2024-09-29T19:10:13.000Z'),
            mt:new Date('2024-09-30T08:04:25.994Z'),
            _mt:new Date('2024-09-30T08:20:13.029Z'),
            lst:'New',
            status:'A',
            ts:new Date('2024-09-30T06:40:53.779Z'),
            m:'Bright and Modern 1 Bedroom + Den'
          },
          {
            _id:'TRBE9373054'
            saletp:['Sale'],
            lp:598000,
            lup:new Date('2024-09-29T19:10:13.000Z'),
            mt:new Date('2024-09-30T08:04:25.994Z'),
            _mt:new Date('2024-09-30T08:20:13.029Z'),
            lst:'Sld',
            status:'A',
            offD:20241020,
            sp: 578000
            ts:new Date('2024-09-30T06:40:53.779Z'),
            m:'Bright and Modern 1 Bedroom + Den'
          },
          'treb'
        ],
        expectedResult:{
          his: [ { s: 'New', ts: new Date(), o: 'Sld', n: 'New' } ],
          spcts: new Date('2024-09-29T19:10:13.000Z'),
          lcTp: 'New',
          lcO: 'Sld',
          lcN: 'New'
        },
        expectedMetaInfo:{unset:{sp: 1}}
      },
      {
        desc:'test no change and src is trebMan',
        input:[
          {
            _id:'TRBE9373054'
            saletp:['Sale'],
            lp:598000,
            lup:new Date('2024-09-29T19:10:13.000Z'),
            mt:new Date('2024-09-30T08:04:25.994Z'),
            _mt:new Date('2024-09-30T08:20:13.029Z'),
            lst:'Sld',
            offD:20241020,
            status:'U',
            ts:new Date('2024-09-30T06:40:53.779Z'),
            m:'Bright and Modern 1 Bedroom + Den'
          },
          {
            _id:'TRBE9373054'
            saletp:['Sale'],
            lp:598000,
            lup:new Date('2024-09-29T19:10:13.000Z'),
            mt:new Date('2024-09-30T08:04:25.994Z'),
            _mt:new Date('2024-09-30T08:20:13.029Z'),
            lst:'Sld',
            offD:20241020,
            status:'U',
            ts:new Date('2024-09-30T06:40:53.779Z'),
            m:'Bright and Modern 1 Bedroom + Den'
          },
          'trebMan'
        ],
        expectedResult:{spcts:new Date('2024-10-20T04:00:00.000Z')}, # trebMan会根据offD来计算spcts
        expectedMetaInfo:{unset:{}}
      },
      {
        desc:'test sp changed but lst not changed',
        input:[
          {
            _id:'TRBE9373054'
            saletp:['Sale'],
            lp:598000,
            lup:new Date('2024-09-29T19:10:13.000Z'),
            mt:new Date('2024-09-30T08:04:25.994Z'),
            _mt:new Date('2024-09-30T08:20:13.029Z'),
            lst:'Sld',
            sp: 588000,
            offD:20241020,
            status:'U',
            ts:new Date('2024-09-30T06:40:53.779Z'),
            m:'Bright and Modern 1 Bedroom + Den'
          },
          {
            _id:'TRBE9373054'
            saletp:['Sale'],
            lp:598000,
            lup:new Date('2024-09-29T19:10:13.000Z'),
            mt:new Date('2024-09-30T08:04:25.994Z'),
            _mt:new Date('2024-09-30T08:20:13.029Z'),
            lst:'Sld',
            sp: 598000,
            offD:20241020,
            status:'U',
            ts:new Date('2024-09-30T06:40:53.779Z'),
            m:'Bright and Modern 1 Bedroom + Den'
          },
          'treb'
        ],
        expectedResult:{},
        expectedMetaInfo:{unset:{}}
      },
      {
        desc:'test sp changed and lst changed',
        hasSp:true,
        input:[
          {
            _id:'TRBE9373054'
            saletp:['Sale'],
            lp:598000,
            lup:new Date('2024-09-29T19:10:13.000Z'),
            mt:new Date('2024-09-30T08:04:25.994Z'),
            _mt:new Date('2024-09-30T08:20:13.029Z'),
            lst:'Sld',
            sp: 588000,
            offD:20241020,
            status:'U',
            ts:new Date('2024-09-30T06:40:53.779Z'),
            m:'Bright and Modern 1 Bedroom + Den'
          },
          {
            _id:'TRBE9373054'
            saletp:['Sale'],
            lp:598000,
            lup:new Date('2024-09-29T19:10:13.000Z'),
            mt:new Date('2024-09-30T08:04:25.994Z'),
            _mt:new Date('2024-09-30T08:20:13.029Z'),
            lst:'Cld',
            sp: 598000,
            offD:20241020,
            status:'U',
            ts:new Date('2024-09-30T06:40:53.779Z'),
            m:'Bright and Modern 1 Bedroom + Den'
          },
          'treb'
        ],
        expectedResult:{
          his: [
            {
              s: 'sp',
              ts: new Date(),
              d: 20240929,
              n: 588000,
              o: 598000
            },
            { s: 'Sld', ts: new Date(), o: 'Cld', n: 'Sld', sldd: 20241020 }
          ],
          spcts: new Date('2024-10-20T04:00:00.000Z'),
          lcTp: 'Sld',
          lcO: 'Cld',
          lcN: 'Sld'
        },
        expectedMetaInfo:{unset:{}}
      }
    ]
    tests.forEach (test)->
      it test.desc, ()->
        testMetaInfo = {unset:{}}
        result = saveToMaster.addHistoryForOldProp test.input...,testMetaInfo
        if test.expectedResult.his
          test.expectedResult.his.length.should.be.exactly(result?.his?.length)
        # NOTE: his中ts设置为new Date(),只比较年月日
          for his,idx in result.his
            expChgTs = inputToDateNum test.expectedResult.his[idx]?.ts
            retChgTs = inputToDateNum his.ts
            retChgTs.should.be.exactly(expChgTs)
            delete his.ts
            delete test.expectedResult.his[idx].ts
        # spcts存在new Date()的情况,只比较年月日
        if test.expectedResult.spcts
          expSpcts = inputToDateNum test.expectedResult.spcts
          retSpcts = inputToDateNum result.spcts
          retSpcts.should.be.exactly(expSpcts)
          delete test.expectedResult.spcts
          delete result.spcts
        should.deepEqual(result,test.expectedResult)
        should.deepEqual(testMetaInfo,test.expectedMetaInfo)
        return

  describe 'isImportPropHigherPriority', ->
    tests = [
      {
        desc:'RHB should be merged into other board'
        importProp:{src:'RHB',prov:'ON',city:'Toronto'},
        oldProp:{src:'DDF',prov:'ON',city:'Toronto'},
        expected: 'No'
      },
      {
        desc:'DDF should be merged into board outside of DDF,RHB'
        importProp:{src:'DDF',prov:'ON',city:'Toronto'},
        oldProp:{src:'TRB',prov:'ON',city:'Toronto'},
        expected: 'No'
      },
      {
        desc:'No pho TRB should be merged into has pho TRB'
        importProp:{src:'TRB',prov:'ON',city:'Toronto'},
        oldProp:{src:'TRB',prov:'ON',city:'Toronto',pho:1},
        expected: 'No'
      },
      {
        desc:'DTA TRB should be merged into notDTA TRB'
        importProp:{src:'TRB',prov:'ON',city:'Toronto',trbtp:['dta']},
        oldProp:{src:'TRB',prov:'ON',city:'Toronto',trbtp:['evow']},
        expected: 'No'
      },
      {
        desc:'BC TRB should be merged into BRE'
        importProp:{src:'BRE',prov:'BC',city:'SURREY'},
        oldProp:{src:'TRB',prov:'BC',city:'SURREY'},
        expected: 'Yes'
      },
      {
        desc:'ON BRE should be merged into TRB'
        importProp:{src:'BRE',prov:'ON',city:'Toronto'},
        oldProp:{src:'TRB',prov:'ON',city:'Toronto'},
        expected: 'No'
      },
      {
        desc:'Ottawa OTW should be merged into TRB when both no pho'
        importProp:{src:'OTW',prov:'ON',city:'Ottawa'},
        oldProp:{src:'TRB',prov:'ON',city:'Ottawa'},
        expected: 'No'
      },
      {
        desc:'AB TRB should be merged into CLG'
        importProp:{src:'CLG',prov:'AB',city:'Rural Camrose County'},
        oldProp:{src:'TRB',prov:'AB',city:'Rural Camrose County'},
        expected: 'Yes'
      },
      {
        desc:'AB TRB should be merged into EDM'
        importProp:{src:'EDM',prov:'AB',city:'Rural Camrose County'},
        oldProp:{src:'TRB',prov:'AB',city:'Rural Camrose County'},
        expected: 'Yes'
      },
      {
        desc:'Calgary EDM should be merged into CLG'
        importProp:{src:'EDM',prov:'AB',city:'Calgary'},
        oldProp:{src:'CLG',prov:'AB',city:'Calgary'},
        expected: 'No'
      },
      {
        desc:'Edmonton CLG should be merged into CLG'
        importProp:{src:'EDM',prov:'AB',city:'Edmonton'},
        oldProp:{src:'CLG',prov:'AB',city:'Edmonton'},
        expected: 'Yes'
      },
      {
        desc:'Mississauga CAR should be merged into TRB when both no pho'
        importProp:{src:'CAR',prov:'ON',city:'Mississauga'},
        oldProp:{src:'TRB',prov:'ON',city:'Mississauga'},
        expected: 'No'
      },
      {
        desc:'Hamilton CAR should be merged into TRB when both no pho'
        importProp:{src:'CAR',prov:'ON',city:'Hamilton'},
        oldProp:{src:'TRB',prov:'ON',city:'Hamilton'},
        expected: 'No'
      },
      {
        desc:'Burlington CAR should be merged into TRB when both no pho'
        importProp:{src:'CAR',prov:'ON',city:'Burlington'},
        oldProp:{src:'TRB',prov:'ON',city:'Burlington'},
        expected: 'No'
      },
      {
        desc:'Waterloo CAR should be merged into TRB when both no pho'
        importProp:{src:'CAR',prov:'ON',city:'Waterloo'},
        oldProp:{src:'TRB',prov:'ON',city:'Waterloo'},
        expected: 'No'
      },
      {
        desc:'Toronto CAR should be merged into TRB'
        importProp:{src:'CAR',prov:'ON',city:'Toronto'},
        oldProp:{src:'TRB',prov:'ON',city:'Toronto'},
        expected: 'No'
      },
      {
        desc:'Richmond Hill CAR should be merged into TRB'
        importProp:{src:'CAR',prov:'ON',city:'Richmond Hill'},
        oldProp:{src:'TRB',prov:'ON',city:'Richmond Hill'},
        expected: 'No'
      },
      {
        desc:'Markham CAR should be merged into TRB'
        importProp:{src:'CAR',prov:'ON',city:'Markham'},
        oldProp:{src:'TRB',prov:'ON',city:'Markham'},
        expected: 'No'
      },
      {
        desc:'Aurora CAR should be merged into TRB'
        importProp:{src:'CAR',prov:'ON',city:'Aurora'},
        oldProp:{src:'TRB',prov:'ON',city:'Aurora'},
        expected: 'No'
      },
      {
        desc:'Same source should not merge'
        importProp:{_id:'TEST_A',src:'TRB',prov:'ON',city:'Aurora'},
        oldProp:{_id:'TEST_B',src:'TRB',prov:'ON',city:'Aurora'},
        expected: 'Same'
      },
      {
        desc:'Same source should not merge'
        importProp:{_id:'TEST_A',src:'DDF',prov:'ON',city:'Aurora'},
        oldProp:{_id:'TEST_B',src:'DDF',prov:'ON',city:'Aurora'},
        expected: 'Same'
      },
      {
        desc:'当都在ON省且都有图片时,TRB优先级高于OTW'
        importProp:{_id:'TEST_A',src:'TRB',prov:'ON',city:'Ottawa',pho:1},
        oldProp:{_id:'TEST_B',src:'OTW',prov:'ON',city:'Ottawa',pho:1},
        expected: 'Yes'
      },
      {
        desc:'当都在ON省且都没有图片时,TRB优先级高于CAR'
        importProp:{_id:'TEST_A',src:'TRB',prov:'ON',city:'Mississauga'},
        oldProp:{_id:'TEST_B',src:'CAR',prov:'ON',city:'Mississauga'},
        expected: 'Yes'
      },
      {
        desc:'当TRB没有图片而OTW有图片时,OTW优先级更高'
        importProp:{_id:'TEST_A',src:'TRB',prov:'ON',city:'Ottawa'},
        oldProp:{_id:'TEST_B',src:'OTW',prov:'ON',city:'Ottawa',pho:1},
        expected: 'No'
      },
      {
        desc:'当都有图片时,在Ottawa市OTW优先级高于CAR'
        importProp:{_id:'TEST_A',src:'OTW',prov:'ON',city:'Ottawa',pho:1},
        oldProp:{_id:'TEST_B',src:'CAR',prov:'ON',city:'Ottawa',pho:1},
        expected: 'Yes'
      },
      {
        desc:'当TRB没有图片而CAR有图片时,CAR优先级更高'
        importProp:{_id:'TEST_A',src:'TRB',prov:'ON',city:'Toronto'},
        oldProp:{_id:'TEST_B',src:'CAR',prov:'ON',city:'Toronto',pho:1},
        expected: 'No'
      },
      {
        desc:'当都没有图片时,在Waterloo市CAR优先级高于OTW'
        importProp:{_id:'TEST_A',src:'CAR',prov:'ON',city:'Waterloo'},
        oldProp:{_id:'TEST_B',src:'OTW',prov:'ON',city:'Waterloo'},
        expected: 'Yes'
      }
    ]
    tests.forEach (test)->
      it test.desc, ()->
        try
          ret = saveToMaster.isImportPropHigherPriority test.importProp,test.oldProp
        catch err
          console.error err
          should.not.exists(err)
        if test.equal
          should.not.exists(ret)
        else
          ret.should.be.exactly(test.expected)
        return

  describe 'checkProp', ->
    tests = [
      {
        desc: '应该通过所有必填字段验证'
        input: {
          _id: 'TEST123'
          ptype: 'r'
          saletp: ['Sale']
          src: 'TRB'
          cnty: 'CA'
          prov: 'ON'
          city: 'Toronto'
          lp: 500000
          status: 'A'
          lst: 'New'
          lat: 43.6532
          lng: -79.3832
        }
        expected: {}
      }
      {
        desc: '应该检测到缺失的必填字段'
        input: {
          _id: 'TEST123'
          ptype: 'r'
          src: 'TRB'
        }
        expected: {
          criticalError: [
            { fld: 'cnty', val: undefined, msg: 'cnty is not canada' }
            {fld: 'saletp', msg: 'missing required fields saletp'}
            {fld: 'cnty', msg: 'missing required fields cnty'}
            {fld: 'prov', msg: 'missing required fields prov'}
            {fld: 'city', msg: 'missing required fields city'}
            {fld: 'lp&lpr', msg: 'no lp and lpr'}
            {fld: 'status&lst', msg: 'no status or lst, status=undefined:undefined'}
            {fld: 'lp || lpr',msg: 'price:undefined is not in range for undefined'}
          ]
        }
      }
      {
        desc: '应该检测到无效的国家代码'
        input: {
          _id: 'TEST123'
          ptype: 'r'
          saletp: ['Sale']
          src: 'TRB'
          cnty: 'US'
          prov: 'ON'
          city: 'Toronto'
          lp: 500000
          status: 'A'
          lst: 'New'
        }
        expected: {
          criticalError: [
            {fld: 'cnty', val: 'US', msg: 'cnty is not canada'}
          ]
        }
      }
      {
        desc: '应该检测到无效的省份代码'
        input: {
          _id: 'TEST123'
          ptype: 'r'
          saletp: ['Sale']
          src: 'TRB'
          cnty: 'CA'
          prov: 'XX'
          city: 'Toronto'
          lp: 500000
          status: 'A'
          lst: 'New'
        }
        expected: {
          criticalError: [
            {fld: 'prov', val: 'XX', msg: 'prov not in canada'}
          ]
        }
      }
      {
        desc: '应该检测到Sale类型但缺少lp'
        input: {
          _id: 'TEST123'
          ptype: 'r'
          saletp: ['Sale']
          src: 'TRB'
          cnty: 'CA'
          prov: 'ON'
          city: 'Toronto'
          status: 'A'
          lst: 'New'
        }
        expected: {
          criticalError: [
            {fld: 'lp&lpr', msg: 'no lp and lpr'}
            {fld: 'lp', msg: 'sale price is not set'}
            {fld: 'lp || lpr',msg: 'price:undefined is not in range for Sale'}
          ]
        }
      }
      {
        desc: '应该检测到Lease类型但缺少lpr'
        input: {
          _id: 'TEST123'
          ptype: 'r'
          saletp: ['Lease']
          src: 'TRB'
          cnty: 'CA'
          prov: 'ON'
          city: 'Toronto'
          status: 'A'
          lst: 'New'
        }
        expected: {
          criticalError: [
            {fld: 'lp&lpr', msg: 'no lp and lpr'}
            {fld: 'lpr', msg: 'lease price is not set'}
            {fld: 'lp || lpr',msg: 'price:undefined is not in range for Lease'}
          ]
        }
      }
      {
        desc: '应该检测到无效的地理位置信息'
        input: {
          _id: 'TEST123'
          ptype: 'r'
          saletp: ['Sale']
          src: 'TRB'
          cnty: 'CA'
          prov: 'ON'
          city: 'Toronto'
          lp: 500000
          status: 'A'
          lst: 'New'
          uaddr: 'out of area'
        }
        expected: {
          criticalError: [
            {fld: 'loc', msg: 'no loc and zip and out of area'}
          ]
        }
      }
      {
        desc: '应该检测到无效的在市时间'
        input: {
          _id: 'TEST123'
          ptype: 'r'
          saletp: ['Sale']
          src: 'TRB'
          cnty: 'CA'
          prov: 'ON'
          city: 'Toronto'
          lp: 500000
          status: 'A'
          lst: 'New'
          dom: -1
        }
        expected: {
          criticalError: [
            {fld: 'dom', msg: 'dom:-1 is less than 0'}
          ]
        }
      }
      {
        desc: '应该检测到无效的日期关系'
        input: {
          _id: 'TEST123'
          ptype: 'r'
          saletp: ['Sale']
          src: 'TRB'
          cnty: 'CA'
          prov: 'ON'
          city: 'Toronto'
          lp: 500000
          status: 'A'
          lst: 'New'
          onD: 20240320
          offD: 20240310
        }
        expected: {
          criticalError: [
            {fld: 'onD&offD', msg: 'offD:20240310 is less than onD:20240320'}
          ]
        }
      }
      {
        desc: '应该检测到无效的ld相关日期'
        input: {
          _id: 'TEST123'
          ptype: 'r'
          saletp: ['Sale']
          src: 'TRB'
          cnty: 'CA'
          prov: 'ON'
          city: 'Toronto'
          lp: 500000
          status: 'A'
          lst: 'New'
          ld: 20240320
          sldd: 20240310
        }
        expected: {
          criticalError: [
            {fld: 'sldd', msg: 'sldd:20240310 is less than ld:20240320'}
          ]
        }
      }
    ]

    tests.forEach (test)->
      it test.desc, ()->
        metaInfo = {}
        try
          {prop, metaInfo} = await saveToMaster.checkProp test.input, metaInfo
        catch err
          console.error err
          should.not.exists(err)
        should.deepEqual(metaInfo, test.expected)
        return
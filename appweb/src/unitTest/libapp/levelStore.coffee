# ./unitTest/test.sh -f libapp/levelStore.js -b

should = require('should')
libLevelStore = require('../../built/libapp/levelStore')
helpers = require('../00_common/helpers')

describe 'Properties Helper function tests',->
  before (done) ->
    @timeout(300000)
    done()

  describe 'getFullFilePathForProp', ->
    tests = [
      {
        desc: '应该为有效的TRB板块和2024年日期生成正确路径'
        input: {
          propTs: new Date('2024-06-15')
          board: 'TRB'
          sid: 'W8432924'
        }
        expected: '/1223/53c43'
      },
      {
        desc: '应该为有效的DDF板块和2023年日期生成正确路径'
        input: {
          propTs: new Date('2023-12-01')
          board: 'DDF'
          sid: '25585654'
        }
        expected: '/1197/e1948'
      },
      {
        desc: '应该为有效的USER板块生成正确路径'
        input: {
          propTs: new Date('2025-01-01')
          board: 'USER'
          sid: 'USER123'
        }
        expected: '/1200/6a73f'
      },
      {
        desc: '应该为BRE板块生成正确路径'
        input: {
          propTs: new Date('2024-03-15')
          board: 'BRE'
          sid: 'R2712462'
        }
        expected: '/1210/8b95a'
      },
      {
        desc: '当board为空字符串时应返回undefined'
        input: {
          propTs: new Date('2024-06-15')
          board: ''
          sid: 'W8432924'
        }
        expected: undefined
      },
      {
        desc: '当board为null时应返回undefined'
        input: {
          propTs: new Date('2024-06-15')
          board: null
          sid: 'W8432924'
        }
        expected: undefined
      },
      {
        desc: '当board为undefined时应返回undefined'
        input: {
          propTs: new Date('2024-06-15')
          board: undefined
          sid: 'W8432924'
        }
        expected: undefined
      },
      {
        desc: '当board为无效值时应返回undefined'
        input: {
          propTs: new Date('2024-06-15')
          board: 'INVALID'
          sid: 'W8432924'
        }
        expected: undefined
      },
      {
        desc: '当board只包含空格时应返回undefined'
        input: {
          propTs: new Date('2024-06-15')
          board: '   '
          sid: 'W8432924'
        }
        expected: undefined
      },
      {
        desc: '当propTs为1970年之前的日期时应返回undefined'
        input: {
          propTs: new Date('1969-12-31')
          board: 'TRB'
          sid: 'W8432924'
        }
        expected: undefined
      },
      {
        desc: '应该正确处理1970年1月1日的边界日期,应该返回undefined'
        input: {
          propTs: new Date('1970-01-01')
          board: 'TRB'
          sid: 'W8432924'
        }
        expected: undefined
      },
      {
        desc: '应该正确处理2015年之前的日期（L1应为750）'
        input: {
          propTs: new Date('2014-12-31')
          board: 'TRB'
          sid: 'W8432924'
        }
        expected: '/750/699db'
      }
    ]

    tests.forEach (test) ->
      it test.desc, (done) ->
        result = libLevelStore.getFullFilePathForProp(
          test.input.propTs,
          test.input.board,
          test.input.sid
        )

        should.equal(result, test.expected)

        done()
###
coffee ./setup.coffee
./mate4.sh compile
./test.sh -m property

###
should = require('should')
i18n  = require('../../built/lib/i18n')
l10n = i18n.getFun 'zh-cn'
_ab = (a,b)-> l10n abbreviator.ab(a,b),b
helpers = null
helpersLib = require('../../built/lib/helpers')
debug = null
Properties = null
User = null
user = null
request = null
additionalConfig =
  devType: 'app',
  isChinaIP: false, # localhost is false
  locale: 'zh-cn',
  isPad: false,
  isAllowedPropAdmin: true,
  isAllowedShowSoldPrice: true,
  isVipRealtor: false,
  isDevGroup: true,
  _ab:_ab,
  l10n:l10n

LISTING_NEED_LOGIN_FIELDS =
  TREB:
    VOW:['cd','comm','dt_ter','perc_dif','pay_freq','pr_lsc','ld','lease_term','lst','sp',\
    'td','unavail_dt','cert_lvl','internet','green_pis','handi_equipped','energy_cert','poss_date','cond']
  BCRE:
    IDX:['_id','id','addr','bltYr','area','city','depth','disp_addr','FloorAreaFinMainFlr','taxyr','front_ft','FrontageMetres','FullBaths',\
    'tax','HalfBaths','LastTransDate','ld','lp','LotSzsf','LotSzsqft','LotSzsqmtrs','zip','PropType','m','boardName','lst',\
    'mfee','style','bthrms','tbdrms','tpdwel','View','ViewSpecify','vturl']
    
describe 'test filter properties',->
  before (done) ->
    @timeout(30000)
    helpers = require('../00_common/helpers')
    debug = helpers.DEBUG()
    request = helpers.getServer()

    Properties = helpers.MODEL 'Properties'
    User = helpers.COLLECTION 'chome','user'

    dbs = [
      { db: 'chome', table: 'user' },
      { db: 'vow', table: 'properties' },
    ]
    helpers.cleanDBs dbs, () ->
      helpers.checkAndsetUpFixture {folder:__dirname}, ()->
        User.findOne {eml:'<EMAIL>'},(err,ret)->
          user = ret
          helpers.login request,{email:'<EMAIL>',pwd:'123456'},(err,cookie)->
            helpers.userCookies.adminCookie = cookie
            setTimeout(done, 2500)
    return
  
  describe 'test find property detail',->
    it 'should return all fields', ()->
      @timeout(30000)
      params = {
        user,
        id:'TRBC5907165',
        disKeyOnly:true,
        trans:'zh-cn',
        additionalConfig
      }
      ret = await Properties.getTranslatedPropDetailByID params
      should.exist(ret.detail)
      should.exist(ret.dis)
      return

    it 'should not return field of dis', ()->
      @timeout(30000)
      params = {
        user,
        id:'TRBC5907165',
        trans:'zh-cn',
        additionalConfig
      }
      ret = await Properties.getTranslatedPropDetailByID params
      should.exist(ret.detail)
      should.not.exist(ret.dis)
      return

    it 'should return field of needLogin', ()->
      @timeout(30000)
      params = {
        id:'TRBC5907165',
        trans:'zh-cn',
        additionalConfig
      }
      ret = await Properties.getTranslatedPropDetailByID params
      should.exist(ret.detail.needLogin)
      should.not.exist(ret.detail.needLoginFields)
      return

    it 'should return field of needLoginFields', ()->
      @timeout(30000)
      params = {
        id:'TRBC5906795',
        trans:'zh-cn',
        additionalConfig
      }
      ret = await Properties.getTranslatedPropDetailByID params
      should.not.exist(ret.detail.needLogin)
      should.exist(ret.detail.needLoginFields)
      return

    it 'should not return field of needLoginFields and needLogin', ()->
      @timeout(30000)
      params = {
        id:'TRBC5872207',
        trans:'zh-cn',
        additionalConfig
      }
      ret = await Properties.getTranslatedPropDetailByID params
      should.not.exist(ret.detail.needLogin)
      should.not.exist(ret.detail.needLoginFields)
      return
    
    it 'rm listing should not needLogin', ()->
      @timeout(30000)
      params = {
        id:'RM1-61218',
        trans:'zh-cn',
        additionalConfig
      }
      ret = await Properties.getTranslatedPropDetailByID params
      should.not.exist(ret.detail.needLogin)
      should.not.exist(ret.detail.needLoginFields)
      return
    
    delPropTests = [
      {
        desc:'when vip user view details of deleted prop,should return all fields'
        permission:{
          isAllowedAdmin:false
          isRealGroup:false
          isAllowedVipUser:true
        },
        allowed:true
      },
      {
        desc:'when inReal user view details of deleted prop,should return all fields'
        permission:{
          isAllowedAdmin:false
          isRealGroup:false
          isAllowedVipUser:true
        },
        allowed:true
      },
      {
        desc:'when admin user view details of deleted prop,should return all fields'
        permission:{
          isAllowedAdmin:false
          isRealGroup:false
          isAllowedVipUser:true
        },
        allowed:true
      },
      {
        desc:'when user other than vip,inReal,admin view details of deleted prop, should return not found'
        permission:{
          isAllowedAdmin:false
          isRealGroup:false
          isAllowedVipUser:false
        },
        allowed:false
      }
    ]
    delPropTests.forEach (test)->
      it test.desc, ()->
        @timeout(30000)
        tmpConfig = Object.assign test.permission,additionalConfig
        params = {
          user,
          id:'TRBC5991064',
          disKeyOnly:true,
          trans:'zh-cn',
          additionalConfig:tmpConfig
        }
        ret = await Properties.getTranslatedPropDetailByID params
        if test.allowed
          should.exist(ret.detail)
          should.exist(ret.dis)
        else
          should.deepEqual(ret,{err:'Not Found'})
        return
      return
     


  describe 'new API compares with source API',->
    testCases = ['BRER2752228','DDF25270081','TRBC5872207','RM1-57025']
    testCases.forEach (test)->
      it test+': the return fields of new detail API should be equal with source detail API',()->
        @timeout(30000)
        return new Promise((resolve,reject)->
          new_ret = await Properties.getTranslatedPropDetailByID {user,id:test,disKeyOnly:true,trans:'zh-cn',additionalConfig}
          # console.log '+++++new_ret',new_ret
          new_ret.ok.should.be.exactly(1)
          new_ret = new_ret.detail
          body = {_id:test}
          request
            .post('1.5/props/detail')
            .set('Accept', 'application/json')
            .set('Cookie', helpers.userCookies['adminCookie'])
            .expect('Content-Type', /json/)
            .expect(200)
            .send(body)
            .end (err, res)->
              {ok,detail} = res.body
              ok.should.be.exactly(1)
              for k,v of detail
                if k in ['trademarkDesc']
                  continue
                # console.log '++++',k,v,new_ret[k]
                if v
                  should.exist(new_ret[k])
                if k in ['his','oh','picUrls','trnst','schools']
                  should.deepEqual(v.length,new_ret[k].length)
                  continue
                if k in ['_mt','vc','vcd','vcappr'] # 'EntryDate','LastTransDate'
                  continue
                # console.log '----val',k,v,new_ret[k]
                # NOTE: request return date string, but new API return date object
                if new_ret[k] instanceof Date
                  continue
                if helpersLib.isObjectIDString(new_ret[k]) or helpersLib.isObjectIDString(v)
                  continue
                should.deepEqual(v,new_ret[k])
                return resolve()
        )

  xdescribe 'test findAddrHistory',->
    tests = [
      {
        desc:'should not needVerify when address is similar'
        input:{
          'addr': '197 Kirk Dr',
          'prov': 'Ontario',
          'city': 'Markham',
          'id': 'TRBN8315344',
          'lat': 43.826,
          'lng': -79.41277,
          'uaddr': 'CA:ON:MARKHAM:197 KIRK DR',
          'st_num': '197',
          'zip': 'L3T3L7',
          'type': 'Detached'
        },
        expectedNum:4,
        expected:{
          'TRBN8154542':{
            'warn':false
          },
          'TRBN8082494':{
            'warn':false
          },
          'TRBN4372908':{
            'warn':false
          },
          'TRBN5588357':{
            'warn':false
          }
        }
      },
      {
        desc:'should return props when distance within 20 meters',
        input:{
          'addr': '471 Silken Laumann Dr',
          'prov': 'Ontario',
          'city': 'Newmarket',
          'id': 'TRBN8455766',
          'lat': 44.0337499777934,
          'lng': -79.4524898931367,
          'uaddr': 'CA:ON:NEWMARKET:471 SILKEN LAUMANN DR',
          'st_num': '471',
          'zip': 'L3X2H9',
          'type': 'Detached'
        },
        expectedNum:5,
        expected:{
          'TRBN8415150':{
            'warn':false
          },
          'TRBN8369288':{
            'warn':false
          },
          'TRBN5432156':{
            'warn':false
          },
          'TRBN1667980':{
            'warn':false
          },
          'TRBN1305244':{
            'warn':false
          }
        }
      }
    ]
    tests.forEach (test)->
      it test.desc, ()->
        @timeout(30000)
        ret = await Properties.findAddrHistory test.input
        should.exists(ret)
        ret.ok?.should.be.exactly(1)
        ret.l?.length.should.be.exactly(test.expectedNum)
        for prop in ret.l
          expectProp = test.expected[prop._id]
          should.exists(expectProp)
          if expectProp.warn
            prop.warn?.should.be.exactly(true)
          else
            should.not.exists(prop.warn)
        return
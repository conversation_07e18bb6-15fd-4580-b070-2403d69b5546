###
coffee setup.coffee
./mate4.sh compile
./unitTest/test.sh -e -f elasticsearch/elasticSearch.js

###

should = require('should')
os = require('os')
helpers = require('../00_common/helpers')
batchBase = require('../../built/lib/batchBase')
{getIndicesByAlias,remove<PERSON>lia<PERSON>} = require('../../built/lib/elasticsearch_async')
libEsProp = require('../../built/libapp/propElasticSearch')
{dayDiff} = require('../../built/lib/helpers_date')
{
  DEFAULT_PROP_FIELDS,
  LIST_SIMPLE_FIELDS,
  LIST_PROP_FIELDS,
  validateFields,
  handleExistPropertyIndex,
  migrateMongo2ES,
  sleep
} = require('../00_common/esHelper')

PropertiesCol = null
SysDataCol = null
debug = null
request = null
existPropertyIndex = null
Properties = null

describe 'ElasticSearch',->
  before (done) ->
    @timeout(300000)
    # TODO: fix index_not_found_exception: no such index [properties_alias]
    # server启动时读取ES的置顶等房源，但是还没有创建索引，migrateMongo2ES 才会创建
    # NOTE: mocha直接启动server 暂时不做修改
    request = helpers.getServer()
    debug = helpers.DEBUG()
    dbs = [
      { db: 'chome', table: 'user' }
      { db: 'chome', table: 'login' }
      { db: 'chome', table: 'rm_group' }
      { db: 'vow', table: 'properties' }
    ]
    useSearchEngine = helpers.CONFIG().propertiesBase?.useSearchEngine
    # console.log '------',useSearchEngine
    should.equal(useSearchEngine,'ES')
    Properties = helpers.MODEL 'Properties'
    PropertiesCol = helpers.COLLECTION 'vow','properties'
    SysDataCol = helpers.COLLECTION 'vow', 'sysdata'
    helpers.cleanDBs dbs, () ->
      helpers.checkAndsetUpFixture {folder:__dirname}, ()->
        helpers.login request,{email:'<EMAIL>',pwd:'123456'},(err,adminCookie)->
          if err or not adminCookie
            debug.error err
            return done err
          adminCookie.push 'apsv=appDebug'
          helpers.userCookies.userAdmin = adminCookie
          helpers.login request,{email:'<EMAIL>',pwd:'123456'},(err,cookie)->
            if err or not cookie
              debug.error err
              return done err
            cookie.push 'apsv=appDebug'
            helpers.userCookies.user = cookie
            helpers.login request,{email:'<EMAIL>',pwd:'123456'},(err,inRealCookie)->
              if err or not cookie
                debug.error err
                return done err
              inRealCookie.push 'apsv=appDebug'
              helpers.userCookies.userInReal = inRealCookie
              helpers.login request,{email:'<EMAIL>',pwd:'123456'},(err,vipCookie)->
                if err or not cookie
                  debug.error err
                  return done err
                vipCookie.push 'apsv=appDebug'
                helpers.userCookies.userVip = vipCookie
                setTimeout(done, 60000)
                # migrateMongo2ES did not invokee cb
                migrateMongo2ES debug, (code,err)->
                  should.not.exist(err)
    return

  after (done) ->
    @timeout(300000)
    # kill watchPropAndUpdateElasticSearch?
    process.kill(process.pid, 'SIGUSR1')
    _id = 'propertiesToElastic@'+os.hostname
    SysDataCol.findOne {_id},(err,ret)->
      indice = 'properties_'+ret.majorV
      setTimeout(done, 30000)
      batchBase.executeBatch 'batch/prop/adminElasticSearch.js','delete',indice,(ecode,err)->
        should.not.exist(err)
    return

  describe 'test esPropSearch func -> search',->
    tests = [
      {
        desc:'options no searchTxt and propIds,should return merged and del props',
        param:{
          label: 1,
          src: 'mls',
          saletp: 'sale',
          saleDesc: 'Sale',
          ptype: 'Residential',
          no_mfee: false,
          sort: 'auto-ts',
          oh: false,
          soldOnly: true,
          ts: new Date('2022-01-01'),
          page: 0,
          hasWechat: false,
          locale: 'zh-cn',
          isAssignAdmin: true,
          searchMergeProps: true,
          searchDelProps: true,
        },
        expected:{count:15,ids:[
          'TRBN5431845','TRBN5398754','TRBE6053703','DDF25585713','BRER2880516','TRBX10845526','CLGA2159918',\
          'TRBE8225374','TRBE5991012','TRBW10403056','TRBW10407385','DDF27609637','DDF27609700','TRBN8455766','TRBN8415150'
        ]}
      },
      {
        desc:'options no searchTxt and propIds,should return del props and not return merged props',
        param:{
          label: 1,
          src: 'mls',
          saletp: 'sale',
          saleDesc: 'Sale',
          ptype: 'Residential',
          no_mfee: false,
          sort: 'auto-ts',
          oh: false,
          soldOnly: true,
          ts: new Date('2022-01-01'),
          page: 0,
          hasWechat: false,
          locale: 'zh-cn',
          isAssignAdmin: true,
          searchMergeProps: false,
          searchDelProps: true,
        },
        expected:{count:11,ids:[
          'TRBN5431845','TRBN5398754','TRBE6053703','BRER2880516','CLGA2159918','TRBE8225374',\
          'TRBE5991012','TRBW10403056','TRBX10845526','TRBN8455766','TRBN8415150'
        ]}
      },
      {
        desc:'options no searchTxt and propIds,should return merged props and not return del props',
        param:{
          label: 1,
          src: 'mls',
          saletp: 'sale',
          saleDesc: 'Sale',
          ptype: 'Residential',
          no_mfee: false,
          sort: 'auto-ts',
          oh: false,
          soldOnly: true,
          ts: new Date('2022-01-01'),
          page: 0,
          hasWechat: false,
          locale: 'zh-cn',
          isAssignAdmin: true,
          searchMergeProps: true,
          searchDelProps: false,
        },
        expected:{count:14,ids:[
          'TRBN5431845','TRBN5398754','DDF25585713','BRER2880516','CLGA2159918','TRBE8225374','TRBN8455766',\
          'TRBE5991012','TRBW10403056','TRBW10407385','DDF27609637','DDF27609700','TRBX10845526','TRBN8415150'
        ]}
      },
      {
        desc:'options no searchTxt and propIds,should not return merged and del props',
        param:{
          label: 1,
          src: 'mls',
          saletp: 'sale',
          saleDesc: 'Sale',
          ptype: 'Residential',
          no_mfee: false,
          sort: 'auto-ts',
          oh: false,
          soldOnly: true,
          ts: new Date('2022-01-01'),
          page: 0,
          hasWechat: false,
          locale: 'zh-cn',
          isAssignAdmin: true,
          searchMergeProps: false,
          searchDelProps: false,
        },
        expected:{count:10,ids:[
          'TRBN5431845','TRBN5398754','BRER2880516','CLGA2159918','TRBE8225374',\
          'TRBE5991012','TRBW10403056','TRBX10845526','TRBN8455766','TRBN8415150'
        ]}
      },
      {
        desc:'options has searchTxt of addr,should return merged and del props',
        param:{
          searchTxt: '2787 Eglinton Ave E',
          limit: 20,
          skip: 0,
          sort: '',
          isAssignAdmin: true,
          searchMergeProps: true,
          searchDelProps: true,
        },
        expected:{count:3,ids:['TRBE6053703','TRBE5991012','DDF25585713']}
      },
      {
        desc:'options has searchTxt of addr,should return merged props and not return del props',
        param:{
          searchTxt: '2787 Eglinton Ave E',
          limit: 20,
          skip: 0,
          sort: '',
          isAssignAdmin: true,
          searchMergeProps: true,
          searchDelProps: false,
        },
        expected:{count:2,ids:['TRBE5991012','DDF25585713']}
      },
      {
        desc:'options has searchTxt of addr,should return del props and not return merged props',
        param:{
          searchTxt: '2787 Eglinton Ave E',
          limit: 20,
          skip: 0,
          sort: '',
          isAssignAdmin: true,
          searchMergeProps: false,
          searchDelProps: true,
        },
        expected:{count:2,ids:['TRBE6053703','TRBE5991012']}
      },
      {
        desc:'options has searchTxt of addr,should not return merged and del props',
        param:{
          searchTxt: '2787 Eglinton Ave E',
          limit: 20,
          skip: 0,
          sort: '',
          isAssignAdmin: true,
          searchMergeProps: false,
          searchDelProps: false,
        },
        expected:{count:1,ids:['TRBE5991012']}
      },
      {
        desc:'options has searchTxt of ids,should return merged and del props',
        param:{
          searchTxt: 'E5991012,E6053703',
          limit: 20,
          skip: 0,
          sort: '',
          isAssignAdmin: true,
          searchMergeProps: true,
          searchDelProps: true,
        },
        expected:{count:3,ids:['TRBE6053703','TRBE5991012','DDF25585713']}
      },
      {
        desc:'options has searchTxt of ids,should return del props and not return merged props',
        param:{
          searchTxt: 'E5991012,E6053703',
          limit: 20,
          skip: 0,
          sort: '',
          isAssignAdmin: true,
          searchMergeProps: false,
          searchDelProps: true,
        },
        expected:{count:2,ids:['TRBE6053703','TRBE5991012']}
      },
      {
        desc:'options has searchTxt of ids,should return del props and not return merged props',
        param:{
          searchTxt: 'E6053703',
          limit: 20,
          skip: 0,
          sort: '',
          isAssignAdmin: false,
          searchMergeProps: false,
          searchDelProps: true,
        },
        expected:{count:1,ids:['TRBE6053703']}
      },
      {
        desc:'options has searchTxt of ids,should not return del props and not return merged props',
        param:{
          searchTxt: 'E6053703',
          limit: 20,
          skip: 0,
          sort: '',
          isAssignAdmin: false,
          searchMergeProps: false,
          searchDelProps: false,
        },
        expected:{count:0,ids:[]}
      },
      {
        desc:'options has searchTxt of ids,should not return del props and not return merged props',
        param:{
          searchTxt: 'TRBE6053703',
          limit: 20,
          skip: 0,
          sort: '',
          isAssignAdmin: false,
          searchMergeProps: false,
          searchDelProps: false,
        },
        expected:{count:0,ids:[]}
      },
      {
        desc:'options has searchTxt of ids,should return merged props and not return del props',
        param:{
          searchTxt: 'E5991012,E6053703',
          limit: 20,
          skip: 0,
          sort: '',
          isAssignAdmin: true,
          searchMergeProps: true,
          searchDelProps: false,
        },
        expected:{count:2,ids:['TRBE5991012','DDF25585713']}
      },
      {
        desc:'options has searchTxt of ids,should not return merged and del props',
        param:{
          searchTxt: 'E5991012,E6053703',
          limit: 20,
          skip: 0,
          sort: '',
          isAssignAdmin: true,
          searchMergeProps: false,
          searchDelProps: false,
        },
        expected:{count:1,ids:['TRBE5991012']}
      },
      {
        desc:'options has propIds,should return merged and del props',
        param:{
          propIds: ['TRBE6053703','TRBE5991012','DDF25585713'],
          isAssignAdmin: true,
          searchMergeProps: true,
          searchDelProps: true,
        },
        expected:{count:3,ids:['TRBE6053703','TRBE5991012','DDF25585713']}
      },
      {
        desc:'options has propIds,should return del props and not return merged props',
        param:{
          propIds: ['TRBE6053703','TRBE5991012','DDF25585713'],
          isAssignAdmin: true,
          searchMergeProps: false,
          searchDelProps: true,
        },
        expected:{count:2,ids:['TRBE6053703','TRBE5991012']}
      },
      {
        desc:'options has propIds,should return merged props and not return del props',
        param:{
          propIds: ['TRBE6053703','TRBE5991012','DDF25585713'],
          isAssignAdmin: true,
          searchMergeProps: true,
          searchDelProps: false,
        },
        expected:{count:2,ids:['TRBE5991012','DDF25585713']}
      },
      {
        desc:'options has propIds,should not return merged and del props',
        param:{
          propIds: ['TRBE6053703','TRBE5991012','DDF25585713'],
          isAssignAdmin: true,
          searchMergeProps: false,
          searchDelProps: false,
        },
        expected:{count:1,ids:['TRBE5991012']}
      },
      {
        desc:'options has searchTxt of RMId,should not return private prop when not AssignAdmin',
        param:{
          searchTxt: 'RM1-48649',
          limit: 20,
          skip: 0,
          sort: '',
          isAssignAdmin: false,
          searchMergeProps: false,
          searchDelProps: false,
        },
        expected:{count:0,ids:[]}
      },
      {
        desc:'options has searchTxt of RMId,should return private prop when AssignAdmin',
        param:{
          searchTxt: 'RM1-48649',
          limit: 20,
          skip: 0,
          sort: '',
          isAssignAdmin: true,
          searchMergeProps: false,
          searchDelProps: false,
        },
        expected:{count:1,ids:['61e8dbbbf6a1b3182e826f74']}
      },
      {
        desc:'options has searchTxt of RMId,should return noprivate prop when not AssignAdmin',
        param:{
          searchTxt: 'RM1-48643',
          limit: 20,
          skip: 0,
          sort: '',
          isAssignAdmin: false,
          searchMergeProps: false,
          searchDelProps: false,
        },
        expected:{count:1,ids:['61e8dbbbf6a1b3182e826f72']}
      }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        @timeout(60000)
        libEsProp.search test.param,(err,ret)->
          should.not.exist(err)
          ret?.cnt?.should.be.exactly(test.expected.count)
          for prop in ret.props
            isExpect = test.expected.ids.includes(prop._id)
            isExpect.should.be.exactly(true)
          done()
        return

  describe 'api /1.5/props/autocompleteGetNext test',->
    tests = [
      {
        desc:'admin search by addr should return merged and del props',
        post:{s:'2787 Eglinton Ave E'},
        userAdmin:true,
        expectedNum:3,
        expectedIds:['TRBE6053703','TRBE5991012','DDF25585713']
      },
      {
        desc:'admin search by addr should return merged and del props when input length is 12',
        post:{s:'2787 Eglinto'},
        userAdmin:true,
        expectedNum:3,
        expectedIds:['TRBE6053703','TRBE5991012','DDF25585713']
      },
      {
        desc:'admin search by addr should return merged and del props when input length is 11',
        post:{s:'2787 Eglint'},
        userAdmin:true,
        expectedNum:3,
        expectedIds:['TRBE6053703','TRBE5991012','DDF25585713']
      },
      {
        desc:'admin search by addr should return merged and del props when input length is 13',
        post:{s:'2787 Eglinton'},
        userAdmin:true,
        expectedNum:3,
        expectedIds:['TRBE6053703','TRBE5991012','DDF25585713']
      },
      {
        desc:'inRealGroup search by addr should not return merged props',
        post:{s:'2787 Eglinton Ave E'},
        userInReal:true,
        expectedNum:2,
        expectedIds:['TRBE5991012','TRBE6053703']
      },
      {
        desc:'vipUser search by addr should not return merged and del props',
        post:{s:'2787 Eglinton Ave E'},
        userVip:true,
        expectedNum:1,
        expectedIds:['TRBE5991012']
      },
      {
        desc:'user search by addr should not return merged and del props',
        post:{s:'2787 Eglinton Ave E'},
        user:true,
        expectedNum:1,
        expectedIds:['TRBE5991012']
      },
      {
        desc:'admin search by sid should return merged and del props',
        post:{s:'E5991012,E6053703'},
        userAdmin:true,
        expectedNum:3,
        expectedIds:['TRBE6053703','TRBE5991012','DDF25585713']
      },
      {
        desc:'inRealGroup search by sid should not return merged props',
        post:{s:'E5991012,E6053703'},
        userInReal:true,
        expectedNum:2,
        expectedIds:['TRBE6053703','TRBE5991012']
      },
      {
        desc:'vipUser search by sid should not return merged and deleted props',
        post:{s:'E5991012,E6053703'},
        userVip:true,
        expectedNum:1,
        expectedIds:['TRBE5991012']
      },
      {
        desc:'vipUser search by id should not return merged and deleted props',
        post:{s:'DDF25585713'},
        userVip:true,
        expectedNum:0,
        expectedIds:[]
      },
      {
        desc:'user search by sid should not return merged and del props',
        post:{s:'E5991012,E6053703'},
        user:true,
        expectedNum:1,
        expectedIds:['TRBE5991012']
      },
      {
        desc:'user search oreb by sid should return prop',
        post:{s:'731271'},
        user:true,
        expectedNum:1,
        expectedIds:['OTW731271']
      },
      {
        desc:'user search oreb by id should return prop',
        post:{s:'OTW731271'},
        user:true,
        expectedNum:1,
        expectedIds:['OTW731271']
      },
      {
        desc:'user search creb by sid should return prop',
        post:{s:'A2159918'},
        user:true,
        expectedNum:1,
        expectedIds:['CLGA2159918']
      },
      {
        desc:'user search creb by id should return prop',
        post:{s:'CLGA2159918'},
        user:true,
        expectedNum:1,
        expectedIds:['CLGA2159918']
      }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        @timeout(300000)
        userCookie = null
        if test.user
          userCookie = helpers.userCookies.user
        else if test.userAdmin
          userCookie = helpers.userCookies.userAdmin
        else if test.userInReal
          userCookie = helpers.userCookies.userInReal
        else if test.userVip
          userCookie = helpers.userCookies.userVip
        else
          userCookie = null
        request
          .post('1.5/props/autocompleteGetNext')
          .set('Accept', 'application/json')
          .set('Cookie', userCookie)
          .expect('Content-Type', /json/)
          .send(test.post)
          .end (err, res)->
            res.body.ok.should.be.exactly(1)
            res.body.l.length.should.be.exactly(test.expectedNum)
            for prop in res.body.l
              isExpect = test.expectedIds.includes(prop._id)
              isExpect.should.be.exactly(true)
            # 验证字段
            validateFields(res.body.l, DEFAULT_PROP_FIELDS, PropertiesCol).then ->
              done()
        return

  describe 'api /1.5/search/prop/list test',->
    tests = [
      {
        desc:'admin search by addr should return merged and del props',
        post:{id:'2787 Eglinton Ave E'},
        userAdmin:true,
        expectedNum:3,
        expectedIds:['TRBE6053703','TRBE5991012','DDF25585713']
      },
      {
        desc:'admin search by addr should return merged and del props when input length is 12',
        post:{id:'2787 Eglinton Ave E'},
        userAdmin:true,
        expectedNum:3,
        expectedIds:['TRBE6053703','TRBE5991012','DDF25585713']
      },
      {
        desc:'admin search by addr should return merged and del props when input length is 11',
        post:{id:'2787 Eglint'},
        userAdmin:true,
        expectedNum:3,
        expectedIds:['TRBE6053703','TRBE5991012','DDF25585713']
      },
      {
        desc:'admin search by addr should return merged and del props when input length is 13',
        post:{id:'2787 Eglinton'},
        userAdmin:true,
        expectedNum:3,
        expectedIds:['TRBE6053703','TRBE5991012','DDF25585713']
      },
      {
        desc:'inRealGroup search by addr should not return merged props',
        post:{id:'2787 Eglinton Ave E'},
        userInReal:true,
        expectedNum:2,
        expectedIds:['TRBE6053703','TRBE5991012']
      },
      {
        desc:'vipUser search by addr should not return merged and del props',
        post:{id:'2787 Eglinton Ave E'},
        userVip:true,
        expectedNum:1,
        expectedIds:['TRBE5991012']
      },
      {
        desc:'user search by addr should not return merged and del props',
        post:{id:'2787 Eglinton Ave E'},
        user:true,
        expectedNum:1,
        expectedIds:['TRBE5991012']
      },
      {
        desc:'admin search by sid should return merged and del props',
        post:{id:'E5991012,E6053703'},
        userAdmin:true,
        expectedNum:3,
        expectedIds:['TRBE6053703','TRBE5991012','DDF25585713']
      },
      {
        desc:'inRealGroup search by sid should not return merged props',
        post:{id:'E5991012,E6053703'},
        userInReal:true,
        expectedNum:2,
        expectedIds:['TRBE6053703','TRBE5991012']
      },
      {
        desc:'vipUser search by sid should not return merged and deleted props',
        post:{id:'E5991012,E6053703'},
        userVip:true,
        expectedNum:1,
        expectedIds:['TRBE5991012']
      },
      {
        desc:'vipUser search by id should not return merged and deleted props',
        post:{id:'DDF25585713'},
        userVip:true,
        expectedNum:0,
        expectedIds:[]
      },
      {
        desc:'user search by sid should not return merged and del props',
        post:{id:'E5991012,E6053703'},
        user:true,
        expectedNum:1,
        expectedIds:['TRBE5991012']
      },
      {
        desc:'user search oreb by sid should return prop',
        post:{id:'731271'},
        user:true,
        expectedNum:1,
        expectedIds:['OTW731271']
      },
      {
        desc:'user search oreb by id should return prop',
        post:{id:'OTW731271'},
        user:true,
        expectedNum:1,
        expectedIds:['OTW731271']
      },
      {
        desc:'user search creb by sid should return prop',
        post:{id:'A2159918'},
        user:true,
        expectedNum:1,
        expectedIds:['CLGA2159918']
      },
      {
        desc:'user search creb by id should return prop',
        post:{id:'CLGA2159918'},
        user:true,
        expectedNum:1,
        expectedIds:['CLGA2159918']
      }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        @timeout(300000)
        userCookie = null
        if test.user
          userCookie = helpers.userCookies.user
        else if test.userAdmin
          userCookie = helpers.userCookies.userAdmin
        else if test.userInReal
          userCookie = helpers.userCookies.userInReal
        else if test.userVip
          userCookie = helpers.userCookies.userVip
        else
          userCookie = null
        request
          .post('1.5/search/prop/list')
          .set('Accept', 'application/json')
          .set('Cookie', userCookie)
          .expect('Content-Type', /json/)
          .send(test.post)
          .end (err, res)->
            res.body.ok.should.be.exactly(1)
            res.body.resultList.length.should.be.exactly(test.expectedNum)
            for prop in res.body.resultList
              isExpect = test.expectedIds.includes(prop._id)
              isExpect.should.be.exactly(true)
            # 验证字段
            validateFields(res.body.resultList, LIST_PROP_FIELDS, PropertiesCol).then ->
              done()
        return

  describe 'api /1.5/props/search test',->
    postData = {
      page: 0,
      tsDiff: 2592000000,
      isAllowedVipUser: false,
      ptype: 'r',
      label: 1,
      no_mfee: false,
      soldOnly: true,
      oh: false,
      saletp: 'Sale',
      sort: 'auto-ts',
      src: 'mls',
      discover: 1,
      ts: new Date('2022-01-01'),
      city: 'Toronto',
      prov: null
    }
    tests = [
      {
        desc:'user should not return deleted and merged props'
        expectedNum:2,
        expectedIds:['TRBE5991012','TRBE8225374']
        user:true
      },
      {
        desc:'inreal user should not return deleted merged props'
        expectedNum:2,
        expectedIds:['TRBE5991012','TRBE8225374']
        userInReal:true
      },
      {
        desc:'admin user should not return merged deleted props'
        expectedNum:2,
        expectedIds:['TRBE5991012','TRBE8225374']
        userAdmin:true
      },
      {
        desc:'vip user should not return deleted merged props'
        expectedNum:2,
        expectedIds:['TRBE5991012','TRBE8225374']
        userVip:true
      }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        @timeout(300000)
        userCookie = null
        if test.user
          userCookie = helpers.userCookies.user
        else if test.userAdmin
          userCookie = helpers.userCookies.userAdmin
        else if test.userInReal
          userCookie = helpers.userCookies.userInReal
        else if test.userVip
          userCookie = helpers.userCookies.userVip
        else
          userCookie = null
        request
          .post('1.5/props/search')
          .set('Accept', 'application/json')
          .set('Cookie', userCookie)
          .expect('Content-Type', /json/)
          .send(postData)
          .end (err, res)->
            res.body.ok.should.be.exactly(1)
            res.body.items.length.should.be.exactly(test.expectedNum)
            for prop in res.body.items
              isExpect = test.expectedIds.includes(prop._id)
              isExpect.should.be.exactly(true)
            # 验证字段
            validateFields(res.body.items, LIST_SIMPLE_FIELDS, PropertiesCol).then ->
              done()
        return

  describe 'test del prop',->
    it 'should find del prop',()->
      @timeout(60000)
      await PropertiesCol.updateOne {_id:'TRBN5398754'},{$set:{del:1}}
      await sleep 2000
      requestData = {s:'TRBN5398754'}
      request
        .post('1.5/props/autocompleteGetNext')
        .set('Accept', 'application/json')
        .set('Cookie', helpers.userCookies.userAdmin)
        .expect(200)
        .expect('Content-Type', /json/)
        .send(requestData)
        .end (err, res)->
          res.body.l.length.should.be.exactly(1)
          return
      await sleep 2000
      return
    
    it 'should not find prop',()->
      @timeout(60000)
      await PropertiesCol.deleteOne {_id:'TRBN5398754'}
      await sleep 2000
      requestData = {s:'TRBN5398754'}
      request
        .post('1.5/props/autocompleteGetNext')
        .set('Accept', 'application/json')
        .set('Cookie', helpers.userCookies.userAdmin)
        .expect(200)
        .expect('Content-Type', /json/)
        .send(requestData)
        .end (err, res)->
          res.body.l.length.should.be.exactly(0)
          return
      await sleep 2000
      return

  describe 'test autocompleteGetNext return v(origUnt + addr)',->
    it 'E8225374 searchByIds return prop.v should be composed origUnt and addr', (done)->
      @timeout(60000)
      userCookie = helpers.userCookies.userAdmin
      expectedV = '59 22 Cardwell Ave'
      request
        .post('1.5/props/autocompleteGetNext')
        .set('Accept', 'application/json')
        .set('Cookie', userCookie)
        .expect('Content-Type', /json/)
        .send({s:'E8225374'})
        .end (err, res)->
          # console.log '++++++E8225374:',res.body.l[0].v,expectedV is res.body.l[0].v
          res.body.ok.should.be.exactly(1)
          res.body.l.length.should.be.exactly(1)
          res.body.l[0].v?.should.be.exactly(expectedV)
          done()
      return
    
    it '59 22 Cardwell Ave searchAddr return prop.v should be composed origUnt and addr', (done)->
      @timeout(60000)
      userCookie = helpers.userCookies.userAdmin
      expectedV = '59 22 Cardwell Ave'
      request
        .post('1.5/props/autocompleteGetNext')
        .set('Accept', 'application/json')
        .set('Cookie', userCookie)
        .expect('Content-Type', /json/)
        .send({s:'22 Cardwell Ave'})
        .end (err, res)->
          # console.log '++++++22 Cardwell Ave:',res.body.l[0].v
          res.body.ok.should.be.exactly(1)
          res.body.l.length.should.be.exactly(1)
          res.body.l[0].v?.should.be.exactly(expectedV)
          done()
      return

  describe 'Test addr search sort',->
    # NOTE: addr输入全部,es查询按最匹配的结果返回
    it 'return props should sort by similarity and spcts when esQuery',(done)->
      testInput = {
        searchTxt: '32 Nottingham Dr',
        limit: 20,
        skip: 0,
        isAssignAdmin: true,
        searchMergeProps: true,
        searchDelProps: true,
      }
      libEsProp.search testInput,(err,ret)->
        should.not.exist(err)
        ret?.cnt?.should.be.exactly(4)
        # status:A优先
        # saletp:Sale优先
        # _score:addr检索时1.从前往后match;2.前面多出\w+\s;3.只match中间部分;id检索时长度升序
        # spcts:降序
        expectedIds = ['TRBN9119039','TRBW8298526','TRBW8246520','TRBN5860927']
        for prop,idx in ret.props
          prop._id.should.be.exactly(expectedIds[idx])
        done()
      return

    # NOTE: addr输入部分,es查询按最匹配的结果返回
    it 'return props should sort by similarity and spcts when esQuery',(done)->
      testInput = {
        searchTxt: '71 mcnic',
        limit: 20,
        skip: 0,
        isAssignAdmin: true,
        searchMergeProps: true,
        searchDelProps: true,
      }
      libEsProp.search testInput,(err,ret)->
        should.not.exist(err)
        ret?.cnt?.should.be.exactly(5)
        expectedIds = ['TRBE9240937','TRBE9240829','TRBC2099578','TRBC2092902','DDF21653146']
        for prop,idx in ret.props
          prop._id.should.be.exactly(expectedIds[idx])
        done()
      return
    
    # NOTE: es查询结束之后,id/addr检索不进行重新排序
    # src/model/properties.coffee#416 processRet-> addrList.sort
    it 'return props should sort by status,saletp and spcts when search addr by api',(done)->
      userCookie = helpers.userCookies.userAdmin
      request
        .post('1.5/props/autocompleteGetNext')
        .set('Accept', 'application/json')
        .set('Cookie', userCookie)
        .expect('Content-Type', /json/)
        .send({s:'32 Nottingham Dr'})
        .end (err, res)->
          res.body.ok.should.be.exactly(1)
          res.body.l.length.should.be.exactly(4)
          expectedIds = ['TRBN9119039','TRBW8298526','TRBW8246520','TRBN5860927']
          for prop,idx in res.body.l
            prop._id.should.be.exactly(expectedIds[idx])
          done()
      return

    # id/addr检索以外会进行二次排序,按照saletp,year排序
    it 'return props should sort by ts when search by api',(done)->
      userCookie = helpers.userCookies.userAdmin
      body = {
        page: 0,
        isAllowedVipUser: false,
        ptype: 'r',
        label: 1,
        no_mfee: false,
        soldOnly: true,
        oh: false,
        saletp: 'Sale',
        sort: 'auto-ts',
        src: 'mls',
        discover: 1,
        ts: new Date('2022-01-01')
      }
      request
        .post('1.5/props/search')
        .set('Accept', 'application/json')
        .set('Cookie', userCookie)
        .expect('Content-Type', /json/)
        .send(body)
        .end (err, res)->
          res.body.ok.should.be.exactly(1)
          res.body.items.length.should.be.exactly(8)
          expectedIds = ['TRBN5431845','BRER2880516','TRBW10403056','CLGA2159918','TRBN8455766','TRBN8415150','TRBE8225374','TRBE5991012']
          for prop,idx in res.body.items
            prop._id.should.be.exactly(expectedIds[idx])
          done()
      return
  
  describe 'test autocompleteGetNext by sid',->
    tests = [
      {
        desc:'test search related prop by user',
        post:{s:'W10407385'},
        user:true,
        expectedNum:1,
        expectedIds:['TRBW10403056']
      },
      {
        desc:'test search related prop by user',
        post:{s:'40672568'},
        user:true,
        expectedNum:1,
        expectedIds:['TRBW10403056']
      },
      {
        desc:'test search related prop by user',
        post:{s:'W10403056'},
        user:true,
        expectedNum:1,
        expectedIds:['TRBW10403056']
      },
      {
        desc:'test search related prop by user',
        post:{s:'1421250'},
        user:true,
        expectedNum:1,
        expectedIds:['TRBX10845526']
      },
      {
        desc:'test search related prop by user',
        post:{s:'X10845526'},
        user:true,
        expectedNum:1,
        expectedIds:['TRBX10845526']
      },
      {
        desc:'test search related prop by userAdmin',
        post:{s:'W10407385'},
        userAdmin:true,
        expectedNum:2,
        expectedIds:['TRBW10403056','TRBW10407385']
      },
      {
        desc:'test search related prop by userAdmin',
        post:{s:'40672568'},
        userAdmin:true,
        expectedNum:2,
        expectedIds:['TRBW10403056','DDF27609637']
      },
      {
        desc:'test search related prop by userAdmin',
        post:{s:'W10403056,W10407385'},
        userAdmin:true,
        expectedNum:3,
        expectedIds:['TRBW10403056','TRBW10407385','DDF27609700']
      }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        @timeout(300000)
        userCookie = null
        if test.user
          userCookie = helpers.userCookies.user
        else if test.userAdmin
          userCookie = helpers.userCookies.userAdmin
        else
          userCookie = null
        request
          .post('1.5/props/autocompleteGetNext')
          .set('Accept', 'application/json')
          .set('Cookie', userCookie)
          .expect('Content-Type', /json/)
          .send(test.post)
          .end (err, res)->
            res.body.ok.should.be.exactly(1)
            res.body.l.length.should.be.exactly(test.expectedNum)
            for prop in res.body.l
              isExpect = test.expectedIds.includes(prop._id)
              isExpect.should.be.exactly(true)
            # 验证字段
            validateFields(res.body.l, DEFAULT_PROP_FIELDS, PropertiesCol).then ->
              done()
        return
  
  describe 'test search/prop/list by sid',->
    tests = [
      {
        desc:'test search related prop by user',
        post:{id:'W10407385'},
        user:true,
        expectedNum:1,
        expectedIds:['TRBW10403056']
      },
      {
        desc:'test search related prop by user',
        post:{id:'40672568'},
        user:true,
        expectedNum:1,
        expectedIds:['TRBW10403056']
      },
      {
        desc:'test search related prop by user',
        post:{id:'W10403056'},
        user:true,
        expectedNum:1,
        expectedIds:['TRBW10403056']
      },
      {
        desc:'test search related prop by user',
        post:{id:'1421250'},
        user:true,
        expectedNum:1,
        expectedIds:['TRBX10845526']
      },
      {
        desc:'test search related prop by user',
        post:{id:'X10845526'},
        user:true,
        expectedNum:1,
        expectedIds:['TRBX10845526']
      },
      {
        desc:'test search related prop by userAdmin',
        post:{id:'W10407385'},
        userAdmin:true,
        expectedNum:2,
        expectedIds:['TRBW10403056','TRBW10407385']
      },
      {
        desc:'test search related prop by userAdmin',
        post:{id:'40672568'},
        userAdmin:true,
        expectedNum:2,
        expectedIds:['TRBW10403056','DDF27609637']
      },
      {
        desc:'test search related prop by userAdmin',
        post:{id:'W10403056,W10407385'},
        userAdmin:true,
        expectedNum:3,
        expectedIds:['TRBW10403056','TRBW10407385','DDF27609700']
      }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        @timeout(300000)
        userCookie = null
        if test.user
          userCookie = helpers.userCookies.user
        else if test.userAdmin
          userCookie = helpers.userCookies.userAdmin
        else
          userCookie = null
        request
          .post('1.5/search/prop/list')
          .set('Accept', 'application/json')
          .set('Cookie', userCookie)
          .expect('Content-Type', /json/)
          .send(test.post)
          .end (err, res)->
            res.body.ok.should.be.exactly(1)
            res.body.resultList.length.should.be.exactly(test.expectedNum)
            for prop in res.body.resultList
              isExpect = test.expectedIds.includes(prop._id)
              isExpect.should.be.exactly(true)
            # 验证字段
            validateFields(res.body.resultList, LIST_PROP_FIELDS, PropertiesCol).then ->
              done()
        return
    
  describe 'test findAddrHistory',->
    tests = [
      {
        desc:'should not needVerify when address is similar'
        input:{
          'addr': '197 Kirk Dr',
          'prov': 'Ontario',
          'city': 'Markham',
          'id': 'TRBN8315344',
          'lat': 43.826,
          'lng': -79.41277,
          'uaddr': 'CA:ON:MARKHAM:197 KIRK DR',
          'st_num': '197',
          'zip': 'L3T3L7',
          'type': 'Detached'
        },
        expectedNum:4,
        expected:{
          'TRBN8154542':{
            'warn':false
          },
          'TRBN8082494':{
            'warn':false
          },
          'TRBN4372908':{
            'warn':false
          },
          'TRBN5588357':{
            'warn':false
          }
        }
      },
      {
        desc:'should return props when distance within 20 meters',
        input:{
          'addr': '471 Silken Laumann Dr',
          'prov': 'Ontario',
          'city': 'Newmarket',
          'id': 'TRBN8455766',
          'lat': 44.0337499777934,
          'lng': -79.4524898931367,
          'uaddr': 'CA:ON:NEWMARKET:471 SILKEN LAUMANN DR',
          'st_num': '471',
          'zip': 'L3X2H9',
          'type': 'Detached'
        },
        expectedNum:5,
        expected:{
          'TRBN8415150':{
            'warn':false
          },
          'TRBN8369288':{
            'warn':false
          },
          'TRBN5432156':{
            'warn':false
          },
          'TRBN1667980':{
            'warn':false
          },
          'TRBN1305244':{
            'warn':false
          }
        }
      }
    ]
    tests.forEach (test)->
      it test.desc, ()->
        @timeout(30000)
        ret = await Properties.findAddrHistory test.input
        should.exists(ret)
        ret.ok?.should.be.exactly(1)
        ret.l?.length.should.be.exactly(test.expectedNum)
        for prop in ret.l
          expectProp = test.expected[prop._id]
          should.exists(expectProp)
          if expectProp.warn
            prop.warn?.should.be.exactly(true)
          else
            should.not.exists(prop.warn)
        return
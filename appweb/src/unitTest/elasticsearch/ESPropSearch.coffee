###

NOTE: this file should be tested under test ES setup, eg another cfg file with following cfgs:
NOTE: run watchPropAndUpdateElasticSearch for this config
add under app_config
  useSearchEngine: 'ES'#ES/mongo/sql
  elastic:
    host: 'http://[username]:[password]@localhost:9200'
    verbose: 2

coffee ./setup.coffee -> ./start.sh -t unit_test -e <EMAIL>
./mate4.sh compile -> ./unitTest/compile.sh -s /opt/testfs/realmaster-appweb/src
./test.sh -m elasticsearch ->  ./unitTest/test.sh -f elasticsearch/ESPropSearch.js
###
should = require('should')
os = require('os')
helpers = require('../00_common/helpers')
batchBase = require('../../built/lib/batchBase')
{getIndicesByAlias,createAlias,removeAlias} = require('../../built/lib/elasticsearch_async')
{dayDiff} = require('../../built/lib/helpers_date')
{
  DEFAULT_PROP_FIELDS,
  LIST_SIMPLE_FIELDS,
  LIST_PROP_FIELDS,
  validateFields,
  handleExistPropertyIndex,
  migrateMongo2ES,
  sleep
} = require('../00_common/esHelper')

PropertiesCol = null
SysDataCol = null
debug = null
request = null
webRequest = null
existPropertyIndex = null
beforeDone = null

describe 'Elasticsearch',->
  before (done) ->
    @timeout(300000)
    request = helpers.getServer()
    webRequest = helpers.getServer(true)
    debug = helpers.DEBUG()
    dbs = [
      { db: 'chome', table: 'user' }
      { db: 'chome', table: 'login' }
      { db: 'chome', table: 'rm_group' }
      { db: 'vow', table: 'properties' }
    ]
    useSearchEngine = helpers.CONFIG().propertiesBase?.useSearchEngine
    should.equal(useSearchEngine,'ES')
    PropertiesCol = helpers.COLLECTION 'vow','properties'
    SysDataCol = helpers.COLLECTION 'vow', 'sysdata'
    helpers.cleanDBs dbs, () ->
      helpers.checkAndsetUpFixture {folder:__dirname}, ()->
        setTimeout((-> process.kill(process.pid, "SIGUSR1")),10000)
        PropertiesCol.updateOne {_id:'TRBW5475032'},{$set:{mt:new Date(Date.now() - 30*24 * 3600000)}},(err)->
          migrateMongo2ES debug, (code,err)->
            should.not.exist(err)
            if err
              debug.error err
            # 防止watch时多次触发return done()
            unless beforeDone
              beforeDone = true
              return done()
            return
    return
  
  after (done) ->
    # TODO: @sam
    # describe 'should create a dummy indice for other tests',->
    #   it "should create new indice",(done)->
    #     _id = 'propertiesToElastic@'+os.hostname
    #     SysDataCol.findOne {_id},(err,ret)->
    #       indice = 'properties_'+ret.majorV
    #       batchBase.executeBatch 'batch/prop/adminElasticSearch.js','create',indice,(err,ret)->
    #         done()
    #     return
    PropertiesCol.deleteOne {_id:'TRBW5475031'},(err)->
      try
        await createAlias {index:existPropertyIndex,alias:'properties_alias'}
      catch err
        debug.error err
      done()
    return
  
  describe 'Test search prop',->
    # tsDiff = Date.now()-new Date('2121-11-14T05:00:00.000Z')
    testCases = [
      {
        desc: 'list search'
        params:{
          pho: 1,
          lpChg: 'off',
          sch: 'best',
          bdrms: '2+',
          bsmt: 'Fin',
          ptype2:['House','Detached'],
          src:'mls',
          saletp:'sale',
          saleDesc:'Sale',
          city:'Markham',
          cmty: 'Angus Glen',
          prov:'ON',
          ptype:'Residential',
          no_mfee:true,
          sort:'auto-ts',
          oh:true,
          soldOnly:true,
          ts:'new',
          tsDiff: 3600*1000*24*30,
          min_lp: 1000,
          max_lp: 10000000,
          page:0
        },
        output:[ 'TRBN5431845' ]
      },
      {
        desc:'Test pagination',
        params:{
          src:'rm',
          saletp:'sale',
          ptype:'Residential',
          sort:"lp-desc",
          max_lp: 10000000,
          limit: 1,
          page:1
        },
        output:['61b121cc08b0bb4a4f75ab1f']
      },
      {
        desc: 'Sold search',
        params:{
          label:1,
          src:"mls",
          status: 'U',
          gr: '1+',
          saletp:"sale",
          saleDesc:"Sold",
          ptype:"Residential",
          # ptype2:"Bungalow",
          no_mfee:false,
          sort:"ts-desc",
          dom:"-#{dayDiff 1642478400000,new Date()}",
          soldOnly:true,
          sold:"fast",
          recent:"sold",
          sldd : 20220115,
          cmstn:false,
          ts:1643384315383,
          page:0,
          hasWechat:false
        },
        output: [ 'TRBW5475032','BRER2877679','TRBN5588357' ],
      },
      {
        desc: 'map search'
        params:{
          bbox:[-79.41737691585561,43.59927944234184,-79.31822194317732,43.68740311355461],
          src:'rm',
          sq_f: 100,
          sq_t: 10000,
          ltp: 'assignment',
          saletp:'sale',
          saleDesc:'Sale',
          ptype:'Residential',
          neartype: 'mtr',
          max_mfee: 1000,
          no_mfee:false,
          sort:"lp-desc",
          soldOnly:true,
          ts:1641518325672,
          page:0
        },
        output:[ '619fe39808b0bb4a4f48a15d', '61b121cc08b0bb4a4f75ab1f' ]
      },
      {
        desc: 'exclusive rent rm listing search'
        params:{
          src:'rm',
          bthrms: 1,
          ltp: 'rent',
          cmstn: true,
          saletp:'lease',
          saleDesc:'Sale',
          ptype:'Residential',
          no_mfee:false,
          sort:"mt-asc",
          soldOnly:true,
          ts:1641518325672,
          page:0
        },
        output:[ '61e8dbbbf6a1b3182e826f72' ]
      },
      {
        desc: 'landlord rm listing search'
        params:{
          src:'rm',
          ltp: 'rent',
          saletp:'lease',
          sort:"mt-asc",
          dom:"-30",
          oh:'21220210',
          soldOnly:true,
          page:0
        },
        output:[]
      },
      {
        desc: 'Edm search'
        params:{
          src: 'mls',
          saletp:'sale',
          saleDesc:'Sale',
          ptype:'Residential',
          no_mfee:false,
          sort:"mt-asc",
          soldOnly:true,
          ts:new Date(Date.now() - 3600*1000*24),
        },
        output:[
          'TRBN5431845',  # mt: 2021-11-18
          'TRBN5398754',  # mt: 2021-12-10
          'BRER2880516'   # mt: 2024-05-08
          # '61b121cc08b0bb4a4f75ab1f',
          # '619fe39808b0bb4a4f48a15d',
        ]
      }
    ]
    testCases.forEach (test)->
      requestData = test.params
      it "should get result of: #{test.desc}",(done)->
        @timeout(20000)
        request
          .post('1.5/props/search')
          .set('Accept', 'application/json')
          .expect(200)
          .expect('Content-Type', /json/)
          .send(requestData)
          .end (err, res)->
            propIds = []
            for prop in res.body.items
              propId = prop._id?.toString()
              propIds.push(propId) if not (propId in propIds)
            propIds.should.deepEqual(test.output)
            validateFields(res.body.items, LIST_SIMPLE_FIELDS, PropertiesCol).then ->
              done()
        return

  describe 'Test share props page',->
    testCases = [
      {
        input:{"cache":true,"id":"619fe39808b0bb4a4f48a15d,61b121cc08b0bb4a4f75ab1f","p":0,"share":true,"lang":"zh-cn"},
        output:2
        expectedField:'_id'
      },
      {
        input:{"cache":true,"id":"RM1-47813,TRBN5398754","p":0,"share":true,"lang":"zh-cn"},
        output:2
        expectedField:'id'
      }
    ]
    testCases.forEach (test)->
      requestData = test.input
      it "should get result of #{test.input}",(done)->
        @timeout(20000)
        request
          .post('1.5/search/prop/list')
          .set('Accept', 'application/json')
          .expect(200)
          .expect('Content-Type', /json/)
          .send(requestData)
          .end (err, res)->
            res.body.cnt.should.be.exactly(test.output)
            ids = test.input.id.split(',')
            list = res.body.resultList
            for id,i in ids
              list[i][test.expectedField].should.be.exactly(id)
            validateFields(res.body.resultList, LIST_PROP_FIELDS, PropertiesCol).then ->
              done()
        return

  describe 'Test search prop ids',->
    testCases = [
      {input: 'RM1-',output:3}, # not assignAdmin, 不返回private房源
      {input: 'RM1-47813',output:1},
      {input: 'RM1-47613',output:0},
      {input: 'RM1-48649',output:0},
      {input: 'RM1-47813,TRBN5398754',output:2},
      {input: 'RM1-47813,TRBN5398754,N5431845',output:3},
      {input: 'TRBN5398754,TRBN5431845',output:2},
      {input: 'TRBN5398758', output:0}
      {input: 'TRBN5398754,N5431845,RM1-47813',output:3},
      {input: 'n5398754',output:1},
      {input: 'Royal Troon Cres',output:1},
      {input: 'l Troon Cres',output:1},
      {input: 'Troon Cres',output:1},
      {input: 'al Troon Cres',output:1},
      {input: 'TRBN5398754', output:1}
    ]
    testCases.forEach (test)->
      requestData = {s:test.input}
      it "should get result of #{test.input}",(done)->
        @timeout(20000)
        request
          .post('1.5/props/autocompleteGetNext')
          .set('Accept', 'application/json')
          .expect(200)
          .expect('Content-Type', /json/)
          .send(requestData)
          .end (err, res)->
            res.body.l.length.should.be.exactly(test.output)
            validateFields(res.body.l, DEFAULT_PROP_FIELDS, PropertiesCol).then ->
              done()
        return
  
  describe 'Test BC new status search prop',->
    tests = [
      {
        desc: 'closed prop search',
        params:{
          label: 1,
          src: 'mls',
          saletp: 'sale',
          saleDesc: 'Sold',
          city: 'Surrey',
          prov: 'BC',
          ptype: 'Residential',
          no_mfee: false,
          sort: 'auto-ts',
          oh: false,
          soldOnly: true,
          cmstn: false,
          ts: 1715674039112,
          page: 0,
          hasWechat: false
        },
        output:{
          cnt:1,
          fields:{
            id:'BRER2877679'
            lst:'Cld',
            acceD:'2024-04-28',
            sremD:'2024-04-28',
            cmpD:'2024-04-28'
          }
        }
      },
      {
        desc: 'pending prop search',
        params:{
          label: 1,
          src: 'mls',
          saletp: 'sale',
          saleDesc: 'Sold',
          city: 'Cultus Lake',
          prov: 'BC',
          ptype: 'Residential',
          no_mfee: false,
          sort: 'auto-ts',
          oh: false,
          soldOnly: true,
          cmstn: false,
          ts: 1715674039112,
          page: 0,
          hasWechat: false
        },
        output:{
          cnt:1,
          fields:{
            id:'BRER2877564'
            lst:'Pnd',
            acceD:'2024-05-01',
            sremD:'2024-05-01',
            cmpD:false
          }
        }
      },
      {
        desc: 'Active Under Contract prop search',
        params:{
          label: 1,
          src: 'mls',
          saletp: 'sale',
          saleDesc: 'Sale',
          city: 'Vancouver',
          prov: 'BC',
          ptype: 'Residential',
          no_mfee: false,
          sort: 'auto-ts',
          oh: false,
          soldOnly: true,
          cmstn: false,
          ts: 1715674471100,
          page: 0,
          hasWechat: false
        },
        output:{
          cnt:1,
          fields:{
            id:'BRER2880516'
            lst:'Auc',
            acceD:'2024-05-08',
            sremD:false,
            cmpD:false
          }
        }
      }
    ]
    tests.forEach (test)->
      requestData = test.params
      it "should get result of: #{test.desc}",(done)->
        @timeout(20000)
        request
          .post('1.5/props/search')
          .set('Accept', 'application/json')
          .expect(200)
          .expect('Content-Type', /json/)
          .send(requestData)
          .end (err, res)->
            should.not.exists(err)
            res.body?.items?.length.should.be.exactly(test.output.cnt)
            prop = res.body.items[0]
            for key,val in test.output.fields
              if val
                val.should.exactly(prop[key])
              else
                should.not.exists(prop[key])
            validateFields(res.body.items, LIST_SIMPLE_FIELDS, PropertiesCol).then ->
              done()
        return

  describe 'status',->
    it "should get elasticsearch status",(done)->
      batchBase.executeBatch 'batch/prop/adminElasticSearch.js','status',(code,err,ret)->
        code.should.be.exactly(0)
        ret.count.should.be.exactly('43\n')
        done()
      return

  describe 'Delete elasticsearch indice',->
    # this cmd deleted my ES cur indice which took 3 hr to build, diff mongodb but only one ES, if name confilit will remove cur index
    it "should delete old indice",(done)->
      _id = 'propertiesToElastic@'+os.hostname
      SysDataCol.findOne {_id},(err,ret)->
        indice = 'properties_'+ret.majorV
        batchBase.executeBatch 'batch/prop/adminElasticSearch.js','delete',indice,(err,ret)->
          done()
      return



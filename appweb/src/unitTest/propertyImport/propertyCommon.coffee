exports.deletDateFields=deletDateFields = (prop)->
  ignoreFields = ['spcts','_id','mt','rmmt','pcTs','_mt','ts','cases','dom','lcTp','trnst','dlTs',\
  'lup','metaInfo','offerD','rmPsn','rmPsnDate','saveResult','sptp','sstp','useCache','geoq',\
  'offD','bndProv','bndCmty','bndCity','bndRegion','bndSubCity','cs16','Media','media','phoUrls','pho']
  # NOTE:可能会清除boundary表导致'bndProv','bndCmty','bndCity','bndRegion','bndSubCity'获取不到
  # NOTE:cs16会因为本地数据库原因导致存在差异
  for fld in ignoreFields
    delete prop[fld]

  # TODO: 由于当前数据与本地数据有关,暂时先删除,后续需要添加boundary和sch_rm_merged的mock数据
  delete prop.bnds
  delete prop.schs
  delete prop.schools
  
  if prop.lat
    prop.lat = prop.lat.toFixed(5)
  else
    delete prop.lat
  if prop.lng
    prop.lng = prop.lng.toFixed(5)
  else
    delete prop.lng
  if prop.loc?.coordinates
    prop.loc.coordinates[0] = prop.loc.coordinates[0].toFixed(5)
    prop.loc.coordinates[1] = prop.loc.coordinates[1].toFixed(5)
  # prop.bnds.sort() if prop.bnds
  if prop.his
    for his in prop.his
      delete his.ts
      delete his.d # if new .it will be new date.
  # NOTE:deepEqual会受array的顺序影响
  for fld of prop
    if Array.isArray prop[fld]
      if fld is 'schools'
        # NOTE: 学校id可能为number或者string
        prop[fld].sort((a, b) ->
          return a.nm.localeCompare(b.nm)
        )
      else
        prop[fld].sort()
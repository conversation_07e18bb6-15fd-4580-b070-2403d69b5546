###
Description:    修复房源rmSqft字段执行顺序问题，恢复被错误unset的rmSqft相关字段
Usage:          ./start.sh -n fix_rmSqft -cmd "lib/batchBase.coffee batch/prop/fix_rmSqft.coffee dryrun -d [YYYY-MM-DD]"
Create date:    2025-07-07
Author:         <PERSON><PERSON>iaowei
Run frequency:  Once
Background:     由于saveToMaster.coffee中getUnsetForRequiredFileds和addRmSqft的执行顺序问题，
                导致rmSqft相关字段被错误unset。此脚本用于修复历史数据。
###

debug = DEBUG()
speed = INCLUDE 'lib.speed'
helpers = INCLUDE 'lib.helpers'
PropertiesCol = COLLECTION 'vow', 'properties'
{findSqftAsync} = INCLUDE 'libapp.propSqft'

# 获取sqft相关的collection
SqftMiddle = COLLECTION 'tmp', 'sqft_middle'
SqftsAggregate = COLLECTION 'vow', 'sqfts_aggregate'
SqftToProcess = COLLECTION 'vow', 'sqft_to_process'

sqftCols = {
  SqftMiddle,
  SqftsAggregate,
  SqftToProcess
}

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 1000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

yargs = require('yargs')(AVGS)
yargs.option 'date', { alias: 'd', description: 'query date gte ts,formate:YYYY-MM-DD', required: false, type: 'string'}

dryRun = AVGS.indexOf('dryrun') >= 0
startTs = new Date()

###
# @description 重新计算rmSqft相关字段
# @param {Object} prop - 房源对象
# @returns {Object} 包含rmSqft相关字段的对象，如果没有数据则返回null
###
recalculateRmSqft = (prop) ->
  debug.debug 'recalculateRmSqft', prop._id, prop.sqft, prop.sqft1, prop.sqft2
  
  rmSqftRet = await findSqftAsync {
    prop,
    CollMiddle: sqftCols.SqftMiddle,
    CollAggregated: sqftCols.SqftsAggregate,
    CollQueue: sqftCols.SqftToProcess
  }
  
  debug.debug 'rmSqftRet', prop._id, rmSqftRet
  
  result = {}
  hasData = false
  
  if rmSqftRet?.rmSqft
    for fld in ['rmSqft', 'rmSqft1', 'rmSqft2', 'sqftQ']
      if rmSqftRet[fld]
        result[fld] = rmSqftRet[fld]
        hasData = true
    
    if rmSqftRet.src
      result.sqftSrc = rmSqftRet.src
      hasData = true
  
  return if hasData then result else null

main = () ->
  # 构建查询条件
  query = {
    src: {$ne: 'RM'}, # 排除RM房源
    rmSqft: null
  }
  
  # 如果指定了_mt参数，添加时间范围过滤
  if yargs.argv.date
    query._mt = {$gte: new Date(yargs.argv.date)}
  
  debug.info 'Starting rmSqft fix process...'
  debug.info 'Query:', JSON.stringify(query, null, 2)
  debug.info 'DryRun mode:', dryRun
  
  cur = await PropertiesCol.find query
  cur.maxTimeMS 3600000 # 设置查询超时时间为1小时
  stream = cur.stream()
  
  obj =
    verbose: 1
    stream: stream
    high: 30
    end: (err) ->
      processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
      if err
        debug.error "Error exit. Total process time #{processTs} mins. ", speedMeter.toString()
        return EXIT 1, err
      debug.info "Completed, Total process time #{processTs} mins, #{speedMeter.toString()}"
      EXIT 0
    process: (prop, cb) ->
      speedMeter.check { processed: 1 }

      # 重新计算rmSqft相关字段
      try
        rmSqftData = await recalculateRmSqft(prop)
      catch err
        speedMeter.check { error: 1 }
        debug.error "Error recalculating rmSqft for #{prop._id}", err
        return cb()
      
      if not rmSqftData
        debug.debug "No rmSqft data found for #{prop._id}"
        speedMeter.check { noData: 1 }
        return cb()
      
      update = {
        noModifyMt: true,
        $set: rmSqftData
      }
      
      if dryRun
        debug.info "dryRun mode, skip update, propId:#{prop._id}, update:", update
        speedMeter.check { dryRun: 1 }
        return cb()
      
      try
        await PropertiesCol.updateOne {_id: prop._id}, update
        speedMeter.check { updated: 1 }
        debug.debug "Updated rmSqft for #{prop._id}", rmSqftData
      catch err
        debug.error "Failed to update prop #{prop._id}", err
        speedMeter.check { updateFailed: 1 }
      
      return cb()
  
  helpers.streaming obj

main()

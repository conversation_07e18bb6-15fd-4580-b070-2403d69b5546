###
Description:    Fix bsmt field by regenerating from origBsmt field data
Usage:          ./start.sh -n fix_bsmt -cmd "lib/batchBase.coffee batch/prop/fix_bsmt.coffee dryrun"
                ./start.sh -n fix_bsmt -cmd "lib/batchBase.coffee batch/prop/fix_bsmt.coffee -mt 2025-01-01"
Create date:    2025-06-26
Author:         <PERSON><PERSON><PERSON>
Run frequency:  Once
###

debug = DEBUG()
speed = INCLUDE 'lib.speed'
helpers = INCLUDE 'lib.helpers'
{formatBasement} = INCLUDE 'libapp.impFormat'
PropertiesCol = COLLECTION 'vow', 'properties'

yargs = require('yargs')(AVGS)
yargs.option 'mt', { description: 'filter records by mt date (YYYY-MM-DD format)', type: 'string'}

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 5000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

dryRun = AVGS.indexOf('dryrun') >= 0
startTs = new Date()

main = () ->
  # Build query filter
  query = {
    src: {$ne: 'RM'},
    origBsmt: {$exists: true}
  }
  
  # Add mt filter if provided
  if yargs.argv.mt
    query.mt = {$gte: new Date(yargs.argv.mt)}
    debug.info "Filtering records with mt >= #{yargs.argv.mt}"
  
  projection = {
    origBsmt: 1,
    bsmt: 1
  }
  
  debug.info "Starting batch with query:", query
  cur = await PropertiesCol.find query, {projection}
  cur.maxTimeMS 3600000 # Set query timeout to 1 hour
  stream = cur.stream()
  
  obj =
    verbose: 1
    stream: stream
    high: 30
    end: (err) ->
      processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
      if err
        debug.error "Error exit. Total process time #{processTs} mins. ", speedMeter.toString()
        return EXIT 1, err
      debug.info "Completed, Total process time #{processTs} mins, #{speedMeter.toString()}"
      EXIT 0
    process: (prop, cb) ->
      speedMeter.check { processed: 1 }

      # Skip if no origBsmt field
      unless prop.origBsmt
        debug.debug "propId: #{prop._id} has no origBsmt field, skipping"
        speedMeter.check { noOrigBsmt: 1 }
        return cb()

      # Create a temporary prop object for formatBasement function
      tempProp = {
        bsmt: prop.origBsmt
      }
      
      # Regenerate bsmt field using formatBasement function
      newBsmt = formatBasement(tempProp)
      
      # Check if the new bsmt is different from current bsmt
      currentBsmtStr = JSON.stringify(prop.bsmt or [])
      newBsmtStr = JSON.stringify(newBsmt or [])
      
      if currentBsmtStr is newBsmtStr
        debug.debug "propId: #{prop._id} bsmt field unchanged, skipping update"
        speedMeter.check { unchanged: 1 }
        return cb()
      
      # Prepare update object
      update = {
        noModifyMt: true,
        $set: {}
      }
      
      if newBsmt and newBsmt.length > 0
        update.$set.bsmt = newBsmt
        debug.debug "propId: #{prop._id} updating bsmt from #{currentBsmtStr} to #{newBsmtStr}"
      else
        # If formatBasement returns empty array, remove the bsmt field
        update.$unset = { bsmt: 1 }
        debug.debug "propId: #{prop._id} removing bsmt field (empty result from origBsmt: #{JSON.stringify(prop.origBsmt)})"
      
      if dryRun
        debug.info "dryRun mode, skip update, propId:#{prop._id}, update:", update
        speedMeter.check { dryRun: 1 }
        return cb()

      try
        # Perform the update
        await PropertiesCol.updateOne {_id: prop._id}, update
        speedMeter.check { updated: 1 }
        
      catch err
        debug.error "Failed to process prop #{prop._id}", err
        speedMeter.check { error: 1 }

      return cb()

  helpers.streaming obj
      
main()

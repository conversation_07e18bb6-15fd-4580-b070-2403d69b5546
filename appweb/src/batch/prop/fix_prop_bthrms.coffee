###
Description:    修复房源bthrms字段mapping错误问题,将错误mapping到bthrms的BathroomsTotalInteger字段修复到tbthrms字段
Usage:          ./start.sh -n fix_prop_bthrms -cmd "lib/batchBase.coffee batch/prop/fix_prop_bthrms.coffee dryrun"
                ./start.sh -n fix_prop_bthrms -cmd "lib/batchBase.coffee batch/prop/fix_prop_bthrms.coffee -mt 2025-06-01 dryrun"
Create date:    2025-06-25
Author:         xiaoweiLuo
Run frequency:  Once
###

debug = DEBUG()
speed = INCLUDE 'lib.speed'
helpers = INCLUDE 'lib.helpers'
yargs = require 'yargs'
PropertiesCol = COLLECTION 'vow', 'properties'

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 5000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

dryRun = AVGS.indexOf('dryrun') >= 0
startTs = new Date()

# 根据src确定处理逻辑
getUpdateLogic = (prop) ->
  src = prop.src
  update = {noModifyMt: true, $set: {}, $unset: {}}
  hasUpdate = false

  # 如果tbthrms和bthrms都存在或都不存在时,跳过
  if (prop.tbthrms? and prop.bthrms?) or (not prop.tbthrms? and not prop.bthrms?)
    return null

  switch src
    when 'BRE', 'CAR', 'EDM', 'RHB'
      # 将原有的bthrms改为tbthrms,FullBaths/BathroomsFull改为bthrms,HalfBaths/BathroomsHalf改为bthhlf
      if prop.bthrms? and not prop.tbthrms?
        update.$set.tbthrms = prop.bthrms
        hasUpdate = true

      # 处理FullBaths/BathroomsFull -> bthrms
      if prop.FullBaths? or prop.BathroomsFull?
        update.$set.bthrms = prop.FullBaths or prop.BathroomsFull or 0
        if prop.FullBaths?
          update.$unset.FullBaths = 1
        if prop.BathroomsFull?
          update.$unset.BathroomsFull = 1
        hasUpdate = true

      # 处理HalfBaths/BathroomsHalf -> bthhlf
      if prop.HalfBaths? or prop.BathroomsHalf?
        update.$set.bthhlf = prop.HalfBaths or prop.BathroomsHalf or 0
        if prop.HalfBaths?
          update.$unset.HalfBaths = 1
        if prop.BathroomsHalf?
          update.$unset.BathroomsHalf = 1
        hasUpdate = true

    when 'OTW'
      # NumberofEnsuiteBathrooms -> bthrms, NumberofPartialBathrooms -> bthhlf
      if prop.NumberofEnsuiteBathrooms?
        update.$set.bthrms = prop.NumberofEnsuiteBathrooms
        update.$unset.NumberofEnsuiteBathrooms = 1
        hasUpdate = true
      
      if prop.NumberofPartialBathrooms?
        update.$set.bthhlf = prop.NumberofPartialBathrooms
        update.$unset.NumberofPartialBathrooms = 1
        hasUpdate = true
      
      # 如果没有bthrms时,将tbthrms赋值到bthrms上
      if not prop.bthrms? and prop.tbthrms?
        update.$set.bthrms = prop.tbthrms
        hasUpdate = true

    when 'TRB', 'DDF'
      # 将原有的bthrms改为tbthrms,bthrms保持不变
      if prop.bthrms? and not prop.tbthrms?
        update.$set.tbthrms = prop.bthrms
        # bthrms保持原值不变,不需要unset
        hasUpdate = true

  # 清理空的$set和$unset对象
  if Object.keys(update.$set).length is 0
    delete update.$set
  if Object.keys(update.$unset).length is 0
    delete update.$unset

  return if hasUpdate then update else null

main = () ->
  # 查询非RM房源
  query = {
    src: {$ne: 'RM'}
  }
  
  # 支持mt参数传入,修复mt之后的历史数据
  if yargs.argv.mt
    query.mt = {$gte: new Date(yargs.argv.mt)}
    debug.info "Processing properties with mt >= #{yargs.argv.mt}"

  projection = {
    src: 1,
    bthrms: 1,
    tbthrms: 1,
    bthhlf: 1,
    BathroomsFull: 1,
    BathroomsHalf: 1,
    NumberofEnsuiteBathrooms: 1,
    NumberofPartialBathrooms: 1,
    FullBaths: 1,
    HalfBaths: 1
  }
  
  cur = await PropertiesCol.find query, {projection}
  cur.maxTimeMS 3600000 # 设置查询超时时间为1小时
  stream = cur.stream()
  
  obj =
    verbose: 1
    stream: stream
    high: 30
    end: (err) ->
      processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
      if err
        debug.error "Error exit. Total process time #{processTs} mins. ", speedMeter.toString(),err
        return EXIT 1, err
      debug.info "Completed, Total process time #{processTs} mins, #{speedMeter.toString()}"
      EXIT 0
    process: (prop, cb) ->
      speedMeter.check { processed: 1 }

      # 获取更新逻辑
      update = getUpdateLogic(prop)
      
      if not update
        speedMeter.check { skipped: 1 }
        return cb()

      if dryRun
        debug.info "dryRun mode, skip update, propId:#{prop._id}, src:#{prop.src}, update:", update
        speedMeter.check { dryRun: 1 }
        return cb()

      try
        await PropertiesCol.updateOne {_id: prop._id}, update
        speedMeter.check { updated: 1 }
        debug.debug "Updated prop #{prop._id}, src:#{prop.src}"
      catch err
        debug.error "Failed to update prop #{prop._id}", err
        speedMeter.check { error: 1 }

      return cb()

  helpers.streaming obj
      
main()


###
watch properties change and sync to ElasticSearch
update resumeToken every 5min.

Options:
-i --init:       reimport all data and upgrade index major version to next version
-m --minor :    upgrade index minor version to next version
-r --routine:    watch Mongo properties collection, sync from last resumtoken
-h hour:    query back hours,only effective when routine mode
test : test ping client

Usage:
./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee test force
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init force > ./logs/watchEs.log 2>&1 &
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee upgrade minor > ./logs/watchEs.log 2>&1 &
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee routine > ./logs/watchEs.log 2>&1 &
New config:
  ./start.sh  -t batch -n watchPropAndUpdateElasticSearch -cmd "lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init force"

New config:
./start.sh -t batch -n watchPropAndUpdateElasticSearch -cmd "lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee routine"

Author:         Sam
Run duration:   init may need 3 hours. 500/0.5s 500w 5000s
Run frequency:  depends
Created:        2021/11/1


Procedure:

-n newIndex


每个索引有不同的版本号，从1开始
eg:properties_1 ,2,3,4


upgrade minorVer:
  eg: add fields
  should do init, import all,no change to index name
  update esMapping, append one version
upgrade majorVersion:
  need rebuild index, eg: change index type
  start a new watch with new code,3 hours.
  build index with new index name,when build finished, update esMapping append version
  restart server.

create collection:
ESmAPPISNG
  _ID:properties
    ver：[{ver:1,},{},{majorVer:, minorVer:,},{m:1,mior:2},{m:2,minor:1}]

properties_1, propties_2

1. 生成新的索引，
  读当前索引name，version，生成新索引名字name_(version+1)
  创建新索引，导入全部数据
  Save Mapping 版本号。config里设置当时使用的索引名。索引名要有版本号。
	               
  batch管理版本号。索引名。找到最新的版本号，然后batch修改版本号（when mapping change）
	app 读版本号。在driver里得到当前的索引。

  读sysData里当前索引name，version，生成新索引名字name_(version+1)
  创建新索引，导入全部数据

2. if not redoMapping
  导入全部数据到alias为properties的index里。

Routine:
  从sysdata里read watch token, import data from resume token.


read es config from config
###


watchHelper = require '../../libapp/watchHelper'
{
  PROPERTY_INDEX,
  PROPERTY_ALIAS,
  ES_MAPPING,
  createPropertiesIndex,
  removePropertiesAlias,
  createPropertiesAlias,
  upsertProp,
  bulkInsertProps,
  bulkOperationProps,
  deleteProp,
  updateMapping,
  updateIndexSetting,
  allESFields,
  SYSDATA_ID
} = INCLUDE '../../libapp/propElasticSearch'
{ ping, getIndices, getAliases,updateStoredScript } = INCLUDE '../../lib/elasticsearch_async'
helpers = require '../../lib/helpers'
debug = DEBUG(2)
speed = INCLUDE 'lib.speed'
PropertiesCol = COLLECTION('vow','properties')
PropertiesCol.createIndex({mt:-1},{background:true})
SysData = COLLECTION('vow','sysdata')
ProcessStatusCol =  COLLECTION('vow','processStatus')
INDEX_NAME='properties'
PROCESS_STATUS_ID = 'watchPropAndUpdateES'
UPDATE_PROCESS_STATE_INTERNAL = 10 * 60 * 1000
availablePropsCounter = 0
gPropsCounter = 0
gWatchedObject = null

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 10000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

{
  processKillSignal,
  updateProcessState,
  getProcessHandler,
  RUNNING_NORMAL,
  EXIT_STRING,
  HAS_RUNNING_PROCESS,
  NORMAL_STRING
} = require '../../libapp/processHelper'

yargs = require('yargs')(AVGS)
yargs.option 'hour', { alias: 'h', description: 'back hours', required: false, type: 'number'}
gBackSeconds = 0
if yargs.argv.hour
  gBackSeconds = yargs.argv.hour * 60 * 60 * 1000
gResumeMt = 0

gUpdateSysData = helpers.addAsyncSupport (watchHelper.getUpdateSysdataFunction({
  mongo:1,
  SysData:SysData,
  sysDataId:SYSDATA_ID
  }))

updateEsMapping = ({mappingVer,upgradeMinor})->
  try
    q = {_id:SYSDATA_ID}
    set = {}
    set.majorV = mappingVer if mappingVer
    set.minorV = upgradeMinor if upgradeMinor
    await SysData.findOneAndUpdate q,{$set:set},{upsert:true}
  catch error
    debug.error error
    EXIT(error)

getEsMappingVer = ()->
  try
    propIndex = await SysData.findOne {_id:SYSDATA_ID}
    curVer = if propIndex?.majorV then propIndex else {majorV:1,minorV:0}
    return curVer
  catch e
    debug.error e
    EXIT(e)

process.on 'SIGUSR1', ->
  # debugHelper.increseModuleThreshhold(1)
  before_exit ''

process.on 'warning', (e) ->
  debug.warn e.stack

process.on 'SIGUSR2', ->
  debugHelper.increseModuleThreshhold(-1)

{
  handleProcessStatus,
  checkRunningProcess,
  handleExit,
} = getProcessHandler {
  ProcessStatusCol,
  id: PROCESS_STATUS_ID,
  filePath: BATCH_FILE_NAME,
  exitFn: EXIT
  updateSysData:gUpdateSysData
}

before_exit = (errMsg)->
  debug.warn 'before_exit errMsg', errMsg
  if gWatchedObject
    gWatchedObject.end()
  handleExit {error:errMsg,watchedObject: gWatchedObject,exitFn:EXIT}

processKillSignal 'watchPropAndUpdateEs',before_exit

perror = (err, code)->
  if err
    debug.critical 'perror', err
  EXIT code, err

process.on 'uncaughtException', (err)->
  before_exit err

# watch
# query sql, if exist, watch. update Token, setInterval.
# no token, watch得到token，关闭watch，init prop，start with token， save token

gLastInitTime = Date.now()

_updateOne= (prop,cb)->
  unless prop?._id
    debug.error 'invalid prop', prop
    return cb 'no id'
  debug.debug '_updateOne',prop._id
  if prop.offD and ('number' isnt typeof prop.offD)
    debug.error 'invalid prop. bad offD.', prop.offD, prop
    return cb 'invalid prop'
  try
    await upsertProp {prop}
  catch err
    debug.error err
    if err is 'prop is too old'
      return cb()
    return perror err,1
  return cb()

_deleteOne = (id,cb)->
  #TODO: id might be prop object
  return cb 'no id' unless _id = (id?._id or id)
  prop = {_id}
  try
    await deleteProp prop
  catch err
    if /not_found/.test err.toString()
      debug.error 'delete prop fail', err, prop._id
    else
      return perror err,1
  return cb()

_replaceOne = (prop,callback)->
  _deleteOne prop, (err, cb)->
    _updateOne(prop,callback)

gNeedChange = []  # 需要修改的房源数组(批量更新，先缓存)
BULK_UPDATE_NUMBER = 100  # 批量更新数量限制
gChangeTimer = null # 批量修改定时器
BULK_UPDATE_INTERVAL_MILLISECONDS = 1000 # 批量更新间隔时间(ms)
RESET_COUNTER = 10  # 进行10次批量更新之后，reset speedmeter

# 停止watch或者发生error时,取消批量修改定时器
_onClose = ()->
  if gChangeTimer
    clearInterval gChangeTimer
    gChangeTimer = null

# 对需要修改的缓存房源进行批量更新，修改routine时的token
bulkUpdate = ()->
  if not gNeedChange.length
    return
  props = gNeedChange.splice(0)
  try
    ret = await bulkOperationProps {props}
  catch err
    debug.error err
  speedMeter.check {'bulkUpdated':1}
  if speedMeter.values.bulkUpdated >= RESET_COUNTER
    debug.info speedMeter.toString()
    speedMeter.reset()
  if ret?.errors
    # 记录error详细信息
    for item in ret.items
      if item.index?.error or item.delete?.error or item.update?.error
        debug.error item
  # 记录token更新时间
  lastChanged = props.pop()
  if gWatchedObject and lastChanged.token and lastChanged.clusterTime
    gWatchedObject.currentToken = lastChanged.token
    gWatchedObject.currentTokenClusterTs = new Date(lastChanged.clusterTime?.getHighBits() * 1000)
    gWatchedObject.currentTokenChangeTs = new Date()
  return

###
# @description 提高es watch时批量更新效率问题
# @param  {object} changedObj {
  _id : object  # 新的token,eg:{_data:""}
  fullDocument  : object  # 变更房源的详情数据
  operationType : string  # 变更类型
  clusterTime   : date
}
# @param  {object}  watchedColl - collection of properties
###
onChangeV2 = (changedObj,watchedColl,cb)->
  error = null
  isValidType = changedObj.operationType in ['insert','update','delete','replace']
  if not isValidType
    debug.verbose "ignore: #{changedObj.operationType}",changedObj
    return cb()
  id = changedObj.documentKey?._id
  if not id
    debug.error 'no id',changedObj
    return cb()
  # NOTE:del房源同样进入es
  doDelete = (changedObj.operationType is 'delete') # or (changedObj?.fullDocument?.del is 1)
  # 缓存需要修改的数据
  if doDelete
    speedMeter.check {'delete':1}
    gNeedChange.push {
      type:'delete',
      _id:id,
      token:changedObj._id,
      clusterTime:changedObj.clusterTime
    }
  else if changedObj.fullDocument
    debug.debug 'change fullDocument',changedObj.fullDocument
    speedMeter.check {'upsert':1}
    gNeedChange.push {
      type:'index',
      _id:id,
      prop:changedObj.fullDocument,
      token:changedObj._id,
      clusterTime:changedObj.clusterTime
    }
  else
    try
      prop = await watchedColl.findOne {_id:id}
    catch err
      error = helpers.stringify err
      debug.error err," propId:#{id}"
    if prop
      speedMeter.check {'upsert':1}
      gNeedChange.push {
        type:'index',
        _id:id,
        prop,
        token:changedObj._id,
        clusterTime:changedObj.clusterTime
      }
    else
      debug.error "no fullDocument,can not find #{id} in watchedColl"
  
  # 更新累计超过100个时,直接更新
  if gNeedChange.length > BULK_UPDATE_NUMBER
    try
      await bulkUpdate()
    catch err
      error += helpers.stringify err
      debug.error err
  # 每秒更新一次
  if not gChangeTimer
    gChangeTimer = setInterval bulkUpdate,BULK_UPDATE_INTERVAL_MILLISECONDS
  if error
    return cb(error)
  return cb()

lastTokenPrintTs = new Date()
gStat = {}
SEVEN_DAY = 7*24*3600*1000
onTokenUpdate = ({token,tokenClusterTs,tokenChangeTs,end,resumeMt})->
  debug.info 'onTokenUpdate:',token,', gStat:',gStat,\
    ', from:',lastTokenPrintTs,', resumeMt:',resumeMt
  lastTokenPrintTs = new Date(tokenClusterTs) if tokenClusterTs
  gStat = {}
  try
    await gUpdateSysData {token,tokenClusterTs,tokenChangeTs,end,resumeMt}
  catch error
    debug.critical error
    return before_exit error

onChange = (changedObj,watchedColl, cb)->
  watchHelper.processChangedObject {
    changedObj,
    watchedColl,
    deleteOneFn:_deleteOne,
    replaceOneFn:_replaceOne,
    updateOneFn:_updateOne,
    watchedStream: gWatchedObject
    }, (err, ret)->
      if err
        debug.critical 'processChangedObject error',err
        return perror err
      gStat[changedObj.operationType]?=0
      gStat[changedObj.operationType]++
      return cb(err, ret)

onWatchError = (err)->
  if err
    debug.error 'watchProp error', err
    _onClose()
    return before_exit err

updateProcessStatusForWatch = (startTs)->
  debug.info 'update Process Status, nextTs:',new Date(Date.now()+UPDATE_PROCESS_STATE_INTERNAL)
  try
    await handleProcessStatus {
      startTs: startTs
      status: RUNNING_NORMAL,
      nextTs: Date.now()+UPDATE_PROCESS_STATE_INTERNAL,
    }
  catch error
    debug.error 'updateProcessStatusForWatch',error

# watch and process one when change.
# if token is invalid, query back 12 hours from lastupdateTs
watchProp = (token,tokenClusterTs) ->
  # 优先级：-h > resumeMt > token
  if gBackSeconds
    startTs = new Date(Date.now() - gBackSeconds)
  else if gResumeMt
    startTs = new Date(gResumeMt)
  else if tokenClusterTs
    # NOTE: fred -> tokenClusterTs不需要往前推12个小时
    startTs = new Date(tokenClusterTs)
  else
    startTs = new Date(new Date() - 12*60*60*1000)
  query ={_mt:{$gte:startTs}}
  opt =
    watchedColl: PropertiesCol,
    onChange: onChangeV2, #onChange,
    onTokenUpdate: onTokenUpdate,
    queryWhenInvalidToken:query,
    tokenDelayMs: 60000*5
    savedToken: token
    lowWaterMark: 2
    highWaterMark: 5
    onError:onWatchError
    updateProcessStatusFn: updateProcessStatusForWatch
    onClose:_onClose
  
  debug.info 'start watch',opt

  watchHelper.watchTarget opt, (err, retObject)->
    if err
      debug.critical 'watchProp error',err
      return perror err,1
    gWatchedObject = retObject if retObject

timeStamp = new Date()

# est:#{calcRemaining(gPropsCounter,fiveKSecond)}hour
# calcRemaining = (gPropsCounter,fiveKSecond)->
#   total = 1000*10000 #1kw records
#   remain = total - gPropsCounter
#   return (remain/5000)*fiveKSecond/3600

# log ts and upsert prop(insert or update)
syncOneProp2ES = ({p,mappingVer},cb)->
  try
    res = await upsertProp {prop:p,mappingVer}
  catch err
    debug.error err
    return cb(err)
  if res?.errors
    return cb(res?.errors)
  gPropsCounter++
  if gPropsCounter%5000 is 0
    fiveKSecond = (new Date()-timeStamp)/1000
    debug.info "records processed #{gPropsCounter}, used: #{fiveKSecond}s"
    timeStamp = new Date()
  cb()

gBulkProps = [] # 批量写入房源列表
BULK_INSERT_NUMBER = 500  # 每次写入es记录数
gBulkCounter = 0  # 写入次数
# 批量写入房源数据
syncBulkProps2ES = ({p,mappingVer,isEnd})->
  isEnd = isEnd or false
  if p
    gPropsCounter++
    gBulkProps.push p
  if (gPropsCounter%BULK_INSERT_NUMBER is 0) or isEnd
    props = gBulkProps.splice(0)
    res = await bulkInsertProps {props,mappingVer}
    gBulkCounter++
    if res?.errors
      errInfo = []
      # 记录error详细信息
      for item in res.items
        if item.index?.error
          debug.error item
          errInfo.push item
      throw errInfo
    if gBulkCounter%10 is 0
      fiveKSecond = (new Date()-timeStamp)/1000
      debug.info "records processed #{gBulkCounter*BULK_INSERT_NUMBER}, used: #{fiveKSecond}s"
      timeStamp = new Date()
  return

# find and stream to ES
syncProps2ES = ({col,mappingVer}, cb)->
  startTs = new Date()
  searchFields = ['trnst','schools','bndCmty','lat','lng','unt','onD','market','phoUrls']
  fields = Object.keys(allESFields).concat searchFields
  projection = {}
  debug.info 'syncProps2ES started: ',startTs
  for value in fields
    projection[value] = 1

  col.find {},{projection}, (err,cur)->
    cur.sort({mt:-1})
    if err then return err
    stream = cur.stream()
    obj =
      verbose:1
      high:10
      stream:stream
      end:(err)->
        return cb(err) if err
        if gBulkProps.length
          # 存在部分房源未写入es
          try
            await syncBulkProps2ES {mappingVer,isEnd:true}
          catch err
            debug.error 'syncBulkProps2ES error:',err
            return cb(err)
        debug.info "##############"
        debug.info "Avaliable Props #{availablePropsCounter}"
        debug.info "Migrate #{gPropsCounter}"
        endTs = new Date()
        processTs = (endTs.getTime() - startTs.getTime())/(1000*60*60)
        debug.info 'Total process time', processTs, 'hrs'
        debug.info "##############"
        return cb()
      process: (p,callback)->
        availablePropsCounter++
        # syncOneProp2ES {p,mappingVer}, callback
        try
          await syncBulkProps2ES {p,mappingVer}
        catch err
          debug.error 'syncBulkProps2ES error:',err
          return callback(err)
        callback()
    helpers.streaming obj

# NOTE: unnecessary wraper
# migrateMongo2ES = ({mappingVer},cb) ->
#   syncProps2ES {col:PropertiesCol,mappingVer}, (err)->
#     cb(err)

createIndexAndMoveProps2ES = ({index,mappingVer,isInit},cb)->
  debug.info "createESPropertiesIndex, index:#{index},mappingVer:#{mappingVer},isInit:#{isInit}"
  try
    await createPropertiesIndex {index,mappingVer,isInit}
  catch err
    return cb(err)
  syncProps2ES {col:PropertiesCol,mappingVer}, (err)->
    cb(err)

# @input
# type opt {
#   index?: string, # = properties_+mappingVer
#   mappingVer: number, # majorVer+1
#   isInit: boolean, # true if init ES
# }
upgradeMappingMajor = ({index,mappingVer,isInit},cb)->
  createIndexAndMoveProps2ES {index,mappingVer,isInit},(err)->
    if err
      debug.error 'createIndexAndMoveProps2ES', err
      return cb(err)
    debug.info "successfull update ES mapping,index:#{index},mappingVer:#{mappingVer}"
    debug.warn '--------###############------------'
    debug.warn 'Admin should delete the last unused properties index'
    debug.warn '--------###############------------'
    # 将index settings 改回默认值
    try
      debug.info 'updateIndexSetting change index settings backto default',mappingVer
      await updateIndexSetting {mappingVer}
      debug.info 'createPropertiesAlias -> properties_NUM; mappingVer:',mappingVer
      await createPropertiesAlias {mappingVer}
      debug.info 'removePropertiesAlias -> properties_NUM-1; mappingVer-1:',mappingVer-1
      await removePropertiesAlias {mappingVer:mappingVer - 1}
      await updateEsMapping {mappingVer,upgradeMinor:1}#reset minorV
    catch err
      debug.error 'mappingVer:',mappingVer-1,err
      return cb(err)
    # debug.error 'removePropertiesAlias',mappingVer-1,err
    cb()

upgradeMappingMinor = ({mappingVer,minorV},cb)->
  try
    await updateMapping {mappingVer}
  catch err
    return cb(err)
  syncProps2ES {col:PropertiesCol,mappingVer},(err,ret)->
    await updateEsMapping {mappingVer,upgradeMinor:minorV}
    cb(err,ret)


import2ES = (action,cb)->
  availablePropsCounter = 0
  gPropsCounter = 0
  {majorV,minorV} = await getEsMappingVer()
  if action is 'init'
    mappingVer = majorV + 1
    upgradeMappingMajor {mappingVer, isInit:true}, cb
  # else if action is 'upgrade-major' # makie init same as upgrade-major
  #   mappingVer = majorV + 1
  #   upgradeMappingMajor {mappingVer}, cb
  else if action is 'upgrade-minor'
    minorV += 1
    upgradeMappingMinor {mappingVer:majorV,minorV}, cb
  else
    cb("Invalid action: #{action}")

getTokenAndDoImport=({action,lastUpdateTs})->
  watchHelper.getToken PropertiesCol,(err, {token,tokenClusterTs})->
    return perror err, 1 if err
    # resumeToken = token
    import2ES action,(err)->
      return perror err, 1 if err
      onTokenUpdate({token,tokenClusterTs})
      watchProp(token,tokenClusterTs)

main = ()->
  isForce = AVGS.indexOf('force')>=0
  try
    hasRunningProcess = await checkRunningProcess()
    if (not isForce) and hasRunningProcess
      return before_exit HAS_RUNNING_PROCESS
    startTs = new Date()
    await updateProcessStatusForWatch(startTs)
  catch error
    debug.error error
  # update stored script
  try
    await updateStoredScript()
    debug.info 'updateStoredScript success'
  catch err
    debug.error err
    return EXIT 0,'updateStoredScript error'

  SysData.findOne {_id:SYSDATA_ID}, {},(err,ret)->
    return perror err, 1 if err
    debug.verbose "sysData of #{SYSDATA_ID}", ret
    tokenClusterTs = ret?.tokenClusterTs or new Date()
    gResumeMt = ret?.resumeMt
    if 'test' in AVGS
      try
        ret = await ping()
        ret2 = await getIndices()
        ret3 = await getAliases()
      catch err
        debug.error err
      console.log '+++++ping',ret,ret2,ret3
      return EXIT 0,'test exit, ret above'
    else if ('upgrade' in AVGS)
      debug.info "upgrade at #{new Date()}"
      isMajor = 'major' in AVGS
      if isMajor
        return perror new Error('Not supported upgrade major, use init'), 1
      isMinor = 'minor' in AVGS
      action = if isMajor then 'major' else 'minor'
      return getTokenAndDoImport({
        action:"upgrade-#{action}",
      })
    else if ('routine' in AVGS)
      unless token= ret?.token
        throw new Error('Token is not found')
        EXIT 1
      try
        resumeToken  = JSON.parse(token)
      catch e # if no token, exit
        debug.error 'invalid resumeToken ',resumeToken
        EXIT 1
      watchProp(resumeToken,new Date(tokenClusterTs))
    else if ('init' in AVGS)
      debug.info "init move to ES at #{new Date()}"
      return getTokenAndDoImport({
        action:'init'
      })
    else
      EXIT 0, 'No action found'

main()
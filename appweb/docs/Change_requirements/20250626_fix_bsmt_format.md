# 需求 [fix_bsmt_format]

## 反馈

1. 用户反馈`I found no matter when I try to locate the properties with the filtering condition "walkout basement", there will appear none, although I did find some by opening the details of the properties. Wondering why. It is so discouraging and disappointing since I will only consider those properties with walkout basement. Thanks.`

## 需求提出人:    Fred/Rain

## 修改人：       Luo xiaowei

## 提出日期:      2025-06-21

## 原因

1. 源数据的`bsmt`字段值发生改变,导致mapping错误(`formatBasement`函数)。

## 解决办法

1. `formatBasement`函数添加新内容的对应关系。

## 数据调查

### formatBasement函数映射规则 (已更新)
```coffeescript
bsmtMapping = [
  {regx:/^Full/i,fld:'Full'}
  {regx:/^Half/i,fld:'Half'}
  {regx:/^(Finished|Fin|Remodeled)/i, fld:'Fin'},
  {regx:/W\/O|Walk[\s\-]*Out|Walkout/i, fld:'W/O'},
  {regx:/^(Unfinished|Unfin)/i, fld:'unFin'},
  {regx:/^(Partial|part)/i, fld:'Prt'},
  {regx:/^(Sep|Separate|Entrance|Exterior)/i, fld:'Sep'},
  {regx:/^(Apartment|Apt|Suite)/i, fld:'Apt'},
  {regx:/^(Walk[\s\-]*Up|W\/U)/i, fld:'W/U'},
  {regx:/^(None|Non|No|N|N\/A)$/i, fld:'NON'},
  {regx:/^(NAN|Unknown|Not[\s]*Applicable)/i, fld:'NAN'},
  {regx:/^(Crawl|Crw|Low)/i, fld:'Crw'},
  {regx:/^(Slab|Cellar|Dugout)/i, fld:'Slab'},
  {regx:/^Y$/i, fld:'Y'},
]
```

**主要改进**:
- 增强了Walk-Out和Walk-Up的匹配模式，支持空格和连字符变体
- 添加了"Remodeled"到已装修类别
- 添加了"Exterior"到独立入口类别
- 添加了"No"和"N/A"到无地下室类别
- 添加了"Cellar"和"Dugout"到基础类型
- 修复了"Not Applicable"的匹配模式

### 各数据源bsmt字段值及映射结果

#### 1. TRB房源

| 原始值 | After formatBasement |
|--------|---------------------|
| 'Apartment' | ['Apt'] |
| 'Crawl Space' | ['Crw'] |
| 'Development Potential' | [] (删除字段) |
| 'Exposed Rock' | [] (删除字段) |
| 'Finished' | ['Fin'] |
| 'Finished with Walk-Out' | ['Fin', 'W/O'] |
| 'Full' | ['Full'] |
| 'Half' | ['Half'] |
| 'No' | ['NON'] |
| 'None' | ['NON'] |
| 'Other' | [] (删除字段) |
| 'Partial Basement' | ['Prt'] |
| 'Partially Finished' | ['Prt', 'Fin'] |
| 'Separate Entrance' | ['Sep'] |
| 'Unfinished' | ['unFin'] |
| 'Unknown' | ['NAN'] |
| 'Walk-Out' | ['W/O'] |
| 'Walk-Up' | ['W/U'] |
| 'Yes' | ['Y'] |

#### 2. DDF房源

| 原始值 | After formatBasement |
|--------|---------------------|
| 'Apartment in basement' | ['Apt'] |
| 'Cellar' | ['Slab'] |
| 'Common' | [] (删除字段) |
| 'Crawl space' | ['Crw'] |
| 'Dugout' | ['Slab'] |
| 'Finished' | ['Fin'] |
| 'Full' | ['Full'] |
| 'Low' | ['Crw'] |
| 'N/A' | ['NON'] |
| 'None' | ['NON'] |
| 'Not Applicable' | ['NAN'] |
| 'Other, See Remarks' | [] (删除字段) |
| 'Partial' | ['Prt'] |
| 'Partially finished' | ['Prt', 'Fin'] |
| 'Remodeled Basement' | ['Fin'] |
| 'See Remarks' | [] (删除字段) |
| 'Separate entrance' | ['Sep'] |
| 'Slab' | ['Slab'] |
| 'Suite' | ['Apt'] |
| 'Unfinished' | ['unFin'] |
| 'Unknown' | ['NAN'] |
| 'Walk out' | ['W/O'] |
| 'Walk-up' | ['W/U'] |

#### 3. BRE房源

| 原始值 | After formatBasement |
|--------|---------------------|
| 'Crawl Space' | ['Crw'] |
| 'Exterior Entry' | ['Sep'] |
| 'Finished' | ['Fin'] |
| 'Full' | ['Full'] |
| 'None' | ['NON'] |
| 'Partial' | ['Prt'] |
| 'Partially Finished' | ['Prt', 'Fin'] |
| 'Unfinished' | ['unFin'] |

#### 4. CAR房源

| 原始值 | After formatBasement |
|--------|---------------------|
| 'Crawl Space' | ['Crw'] |
| 'Development Potential' | [] (删除字段) |
| 'Exposed Rock' | [] (删除字段) |
| 'Finished' | ['Fin'] |
| 'Full' | ['Full'] |
| 'None' | ['NON'] |
| 'Other' | [] (删除字段) |
| 'Partial' | ['Prt'] |
| 'Partially Finished' | ['Prt', 'Fin'] |
| 'Separate Entrance' | ['Sep'] |
| 'Sump Pump' | [] (删除字段) |
| 'Unfinished' | ['unFin'] |
| 'Walk-Out Access' | ['W/O'] |
| 'Walk-Up Access' | ['W/U'] |

#### 5. EDM房源

| 原始值 | After formatBasement |
|--------|---------------------|
| 'Finished' | ['Fin'] |
| 'Full' | ['Full'] |
| 'No Basement' | ['NON'] |
| 'None' | ['NON'] |
| 'Part' | ['Prt'] |
| 'Partial' | ['Prt'] |
| 'Partially Finished' | ['Prt', 'Fin'] |
| 'See Remarks' | [] (删除字段) |
| 'Unfinished' | ['unFin'] |

#### 6. OTW房源

| 原始值 | After formatBasement |
|--------|---------------------|
| 'Cellar' | ['Slab'] |
| 'Common' | [] (删除字段) |
| 'Crawl' | ['Crw'] |
| 'Full' | ['Full'] |
| 'Low' | ['Crw'] |
| 'None' | ['NON'] |
| 'Other (See Remarks)' | [] (删除字段) |
| 'Part' | ['Prt'] |
| 'Slab' | ['Slab'] |

#### 7. CLG房源

| 原始值 | After formatBasement |
|--------|---------------------|
| 'Crawl Space' | ['Crw'] |
| 'Finished' | ['Fin'] |
| 'Full' | ['Full'] |
| 'None' | ['NON'] |
| 'Partial' | ['Prt'] |
| 'Partially Finished' | ['Prt', 'Fin'] |
| 'See Remarks' | [] (删除字段) |
| 'Separate/Exterior Entry' | ['Sep'] |
| 'Suite' | ['Apt'] |
| 'Unfinished' | ['unFin'] |
| 'Walk-Out To Grade' | ['W/O'] |
| 'Walk-Up To Grade' | ['W/U'] |

#### 8. RHB房源

| 原始值 | After formatBasement |
|--------|---------------------|
| 'Crawl Space' | ['Crw'] |
| 'Finished' | ['Fin'] |
| 'Full' | ['Full'] |
| 'None' | ['NON'] |
| 'Other (see Remarks)' | [] (删除字段) |
| 'Partial' | ['Prt'] |
| 'Partially Finished' | ['Prt', 'Fin'] |
| 'Separate Entrance' | ['Sep'] |
| 'Sump Pump' | [] (删除字段) |
| 'Unfinished' | ['unFin'] |
| 'Walk-Out Access' | ['W/O'] |
| 'Walk-Up Access' | ['W/U'] |

### 映射结果汇总

通过formatBasement函数处理后，所有原始bsmt值被映射为以下标准化字段：

- **'Full'**: 完整地下室
- **'Half'**: 半地下室
- **'Fin'**: 已装修
- **'W/O'**: 步出式地下室
- **'unFin'**: 未装修
- **'Prt'**: 部分地下室
- **'Sep'**: 独立入口
- **'Apt'**: 地下室公寓/套房
- **'W/U'**: 步上式地下室
- **'NON'**: 无地下室
- **'NAN'**: 未知/不适用
- **'Crw'**: 爬行空间/低矮地下室
- **'Slab'**: 平板基础
- **'Y'**: 是

**注意**: 无法匹配任何规则的值会导致bsmt字段被删除（空数组）。

## 影响范围

1. 房源import时`bsmt`字段的mapping
2. 房源高级检索

## 是否需要补充UT

1. 需要修复UT

## 确认日期:    2025-06

## online-step

1. 重启watch&import
2. 执行batch修复历史数据
```shell
./start.sh -n fix_bsmt -cmd "lib/batchBase.coffee batch/prop/fix_bsmt.coffee dryrun -mt [YYYY-MM-DD]"

测试服执行结果:
Completed, Total process time 5.620316666666667 mins, processed(11397346):2.027M/m, unchanged(10833098):1.927M/m, dryRun(564248):100.3K/m
```

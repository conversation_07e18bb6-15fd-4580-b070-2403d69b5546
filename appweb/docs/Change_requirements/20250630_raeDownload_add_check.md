# 需求 [raeDownload_add_check]

## 反馈

客户反馈EDME4403569 已经下市，但仍然显示active

## 需求提出人:    Sophie
## 修改人：       Maggie

## 提出日期:      2025-06-30

## 原因
edm会直接把数据删除了，需要添加check


## 解决办法

1. 在raeDownload中添加check，每天2点筛选数数据库中MlsStatus: {$in: ['Active', 'Conditional']},mt在14天前的，然后通过reso查询，如果数据不存在，DCnt加1，当大于3时，设置MlsStatus为"Delete"
2. 修改raeDownload，carDownload，trebResoDownload，保留'phoHL','tnHL','docHL'字段

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-06-30

## online-step

1. 重启watch&import
2. 重启raeDownload
3. 重启carDownload
4. 重启trebResoDownload

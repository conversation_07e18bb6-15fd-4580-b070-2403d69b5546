# 需求 [fix_script_score]

## 反馈

1. Rain反馈存在大量以下error log,script_score 现在不给VIP和inreal显示merged房源了，这个简化下吧，找一下地址对比下效果，看AI能不能优化下score算法
```
Caused by:
		circuit_breaking_exception: [script] Too many dynamic script compilations within, max: [150/5m]; please use indexed, or scripts with parameters instead; this limit can be changed by the [script.max_compilations_rate] setting
	Root causes:
		circuit_breaking_exception: [script] Too many dynamic script compilations within, max: [150/5m]; please use indexed, or scripts with parameters instead; this limit can be changed by the [script.max_compilations_rate] setting
    at SniffingTransport._request (/opt/rmappweb/src/node_modules/@elastic/transport/lib/Transport.js:543:27)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at /opt/rmappweb/src/node_modules/@elastic/transport/lib/Transport.js:641:32
    at SniffingTransport.request (/opt/rmappweb/src/node_modules/@elastic/transport/lib/Transport.js:637:20)
    at Client.SearchApi [as search] (/opt/rmappweb/src/node_modules/@elastic/elasticsearch/lib/api/api/search.js:72:12)
    at exports.searchIndexByBody (/opt/rmappweb/src/lib/elasticsearch_async.coffee:217:14)
    at Object.exports.search (/opt/rmappweb/src/libapp/propElasticSearch.coffee:2883:14)
    at Function.findListingsByElasticSearch (/opt/rmappweb/src/model/properties.coffee:2264:16)
    at queryDB (/opt/rmappweb/src/model/properties.coffee:1382:46)
 {
  options: { redaction: { type: 'replace', additionalKeys: [] } },
  meta: {
    body: {
          script_score: {
            script: {
              source: "def val = doc['searchAddr'].value;def regFir = val =~ /^54 garni/;def regSec = val =~ /^\\w+\\s54 garni/;String ptype2 = '';if(doc['ptype2'].empty || doc['ptype2'].size() == 0 || doc['ptype2'].value == ''){ptype2 = ''; } else {ptype2 = doc['ptype2'].value;}String src = '';if(doc['src'].empty || doc['src'].size() == 0 || doc['src'].value == ''){src = ''; } else {src = doc['src'].value;}def ptype = doc['ptype'].value;def isCondo = ptype2 =~ /Parking|Other|Condo|Apartment/i;def isCommercial = ptype =~ /o|b/i;def score = 0;if ((isCondo || isCommercial) && (doc['unt'].empty || doc['unt'].size() == 0 || doc['unt'].value == '')){score = 40;} else if (regFir){score = 10;} else if (regSec){score = 20;} else {score = 30;}if(src =~ /DDF/i){score += 1;}return score;"
            }
          }
```

## 需求提出人:    Rain

## 修改人：       Luo xiaowei

## 提出日期:      2025-06-03

## 原因

1. 抛出该异常是因为 在5分钟内动态编译了太多次脚本（超过150次）,传入的 script_score 脚本是动态的、inline的，并且可能每次查询都生成一个新脚本（例如脚本内容稍有不同，比如拼接字符串、条件变化），Elasticsearch 就会重新编译它。

## score算法存在的问题

1. 脚本动态拼接严重影响性能
   1. 每次构造查询都在拼接字符串生成新的 script。
   2. 由于拼接内容不同，Elasticsearch 将每一次都视为新脚本，触发动态编译。
   3. 容易触发 script.max_compilations_rate 限制，出现 circuit_breaking_exception 错误。
2. 正则表达式复杂，计算量大
   1. 直接在 script 中使用 =~ /^#{fieldVal}/ 等正则；
   2. 匹配前缀、包含关键词等操作，在地址字段上开销大；
   3. 易导致 regex.limit-factor 超限，性能不可控。
3. 评分逻辑混合了多种职责
   1. 地址匹配逻辑、房产类型判断、是否有单位号、来源质量全部耦合在 script 中；
   2. score 值逻辑复杂、不透明、不易维护。

## 解决办法

1. 脚本算法简化与优化目标
   1. 逻辑清晰：每一类加权有单独分支
   2. 结构固定：方便使用 Stored Script
   3. 正则简化：最大程度避免 costly pattern
   4. 参数外部化：动态值不再拼进 script，而是通过 params 注入
   5. 可以复用：跨项目、不同 query 可共用同一份脚本
2. 优化后的改进方案(固定算分内容`src,ptype2,unt`在init时计算,地址/id匹配动态算分)
   1. 在es init时计算unt,ptype,ptype2,src的score
   2. 使用 Stored Script（存储脚本）替代内联 script
      1. 统一注册一个固定的 script，例如：search_by_wildcard
      2. 将原脚本逻辑中的变量改用 params 注入
      3. script 中不再拼接变量，所有字段值统一传参处理
      4. 查询时只传入 params.prefix 和必要的元字段（score）
   3. 重构地址评分算法
      1. 地址前缀完全匹配, 加100,`val.startsWith(prefix)`
      2. 地址前缀添加字母加空格之后匹配, 加90,`/^\\w+\\s#{prefix}/`(由于stored_script不能使用动态拼接的正则表达式,改用字符串操作来代替,见2.3.6 `script_score`)
      3. 其他情况下, 加80
      4. 当`ptype2`存在`Locker`时减25，存在`Parking`时减30。目的是避免`20 Lombard St`的`Locker/Parking`房源排在了`unt +  20 lombard st`的`Apartment`房源前面
      5. 总评分为地址评分`*500`之后与`score`字段相加,`(500 * score) + origScore`
      6. 按照总评分降序
      7. script_score
         ```JSON
            {
              "script": {
                "lang": "painless",
                "source": """
                 def field = params.field;
                 def val = doc.containsKey(field) && doc[field].size() > 0 ? doc[field].value.toLowerCase() : "";
                 def score = 0;
                 def origScore = doc.containsKey('score') && !doc['score'].empty ? doc['score'].value : 0;
                 if (field == "searchAddr") {
                   def prefix = params.prefix.toLowerCase();
                   def prefixIndex = val.indexOf(prefix);
                   if (val.startsWith(prefix)) {
                     score += 100;
                   } else {
                     score += 30; # 非地址前缀匹配先+30,后面判断是否满足/\w+\s{prefix}/的条件
                     if(prefixIndex > 1) {
                       def charBeforePrefix = val.charAt(prefixIndex - 1);
                       if (charBeforePrefix == ' ') {
                         def allWordCharBeforeSpace = true;
                         for (int i = 0; i < prefixIndex - 1; i++) {
                           def c = val.charAt(i);
                           if (!Character.isLetterOrDigit(c) && c != '_') {
                             allWordCharBeforeSpace = false;
                             break;
                           }
                         }
                         if (allWordCharBeforeSpace) {
                           score += 20; # 满足 /\w+\s{prefix}/的条件,+20以达到地址前缀添加字母加空格之后匹配, 加50的目的
                         }
                       }
                     }
                   }
                 } else {
                   score = 100 - val.length();
                 }
                 return (200 * score) + origScore;
                """
              }
            }
         ```
3. `script_score`调用改为
```JSON
"functions" = [{
 "script_score":{
   "script":{
     "id": "search_by_wildcard",
     "params": {}
 }}
}]
```
1. 在`watchPropAndUpdateElasticSearch`batch启动时,更新ES的`_scripts`信息。在`src/lib/elasticsearch_async.coffee`下添加`updateStoredScript`函数更新es的`_scripts`

## 2025-06-12 新需求

1. 使用id/addr检索时,按以下条件排序。
   1. status: A 优先于 U
   2. saletp: Sale 优先于 Lease
   3. address match: 地址匹配度
   4. merged: not merge 优先于 merged
   5. ptype2 locker/parking: locker 优先于 parking 但是低于其它ptype2
   6. src: DDF 低于其它src
   7. condo房源有没有unt: 有unt 优先于 没有unt
   8. comercial: Residential 优先于 Commercial/Other
   9. spcts: 降序排列
<!-- status(asc),saletp(desc),score(3,4,5,6,8可以在插入es数据库时计算)(desc),spcts(desc) -->

## 影响范围

1. addr/id检索房源列表排序

## 是否需要补充UT

1. 需要补充UT

## 确认日期:    2025-06-04

## Online step

1. es重新init
2. 重启server

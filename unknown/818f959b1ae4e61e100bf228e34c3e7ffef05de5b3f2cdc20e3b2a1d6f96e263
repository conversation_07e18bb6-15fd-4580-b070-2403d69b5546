# 需求 [fix_rmSqft]

## 反馈

1. Tao反馈condo单元里面不显示具体尺寸了。

## 需求提出人:    Tao/Fred

## 修改人：       <PERSON><PERSON>

## 提出日期:      2025-07-06

## 原因

1. 房源在import时检查`rmSqft`等字段的unset操作(getUnsetForRequiredFileds),在添加`rmSqft`(addRmSqft)之前,导致房源在rni更新之后`rmSqft`在import时被unset掉了。

## 相关文件

1. docs/Change_requirements/20250407_fix_unset_rmSqft.md

## 解决办法

1. 修改房源import时对`rmSqft`等字段的unset检查时机。
2. 添加batch修复`_mt`在`2025-04-01`之后的数据。

## 影响范围

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-07-07

## online-step

1. 重启watch&import
2. 执行batch
```shell
./start.sh -t batch -n fix_rmSqft -cmd "lib/batchBase.coffee batch/prop/fix_rmSqft.coffee dryrun -d 2025-04-01"

测试服执行结果:
Completed, Total process time 56.99718333333333 mins, processed(1669391):29.28K/m, noData(1441864):25.29K/m, dryRun(227527):3.991K/m
```
